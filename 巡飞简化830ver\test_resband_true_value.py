"""
测试ResBand算法的真正价值：在候选集合内的智能学习
验证ResBand不是简单的规则映射，而是真正的自适应学习算法
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_resband_true_learning_value():
    """测试ResBand在候选集合内的真正学习价值"""
    print("🎯 测试ResBand算法的真正学习价值")
    print("=" * 60)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    print(f"📊 ResBand候选集合设计:")
    for stage, req in resband.scenario_resolution_requirements.items():
        candidates = req["candidate_resolutions"]
        print(f"   {stage}: 候选分辨率 {candidates}")
        print(f"     -> ResBand需要在{len(candidates)}个选项中学习最优选择")
    
    # 测试各个场景下的学习过程
    test_scenarios = [
        ("simple_static", "简单静态场景", simulate_simple_static_with_variance),
        ("complex_static", "复杂静态场景", simulate_complex_static_with_variance),
        ("complex_dynamic", "复杂动态场景", simulate_complex_dynamic_with_variance)
    ]
    
    episode = 0
    learning_results = {}
    
    for scenario_stage, description, performance_simulator in test_scenarios:
        print(f"\n🎬 {description} - ResBand学习过程")
        print("=" * 50)
        
        # 获取该场景的候选分辨率
        candidates = resband.scenario_resolution_requirements[scenario_stage]["candidate_resolutions"]
        print(f"候选分辨率: {candidates}")
        
        if len(candidates) == 1:
            print(f"⚠️  只有1个候选分辨率，ResBand无学习空间")
            continue
        
        # 模拟该场景下的学习过程
        stage_selections = []
        stage_rewards = []
        
        for i in range(30):  # 每个场景30个episodes
            episode += 1
            
            # ResBand选择分辨率
            selected_config = resband.select_resolution(episode, scenario_stage)
            stage_selections.append(resband.current_arm)
            
            # 模拟性能（包含分辨率差异）
            episode_reward, critic_loss, violations, success = performance_simulator(
                resband.current_arm, i
            )
            stage_rewards.append(episode_reward)
            
            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"Episode {episode:2d}: 选择臂{resband.current_arm}, 奖励={episode_reward:.1f}, 成功={success}")
            
            # 每10个episode分析学习进展
            if (i + 1) % 10 == 0:
                analyze_learning_progress(resband, candidates, stage_selections, stage_rewards, i + 1)
        
        # 分析该场景的最终学习结果
        learning_results[scenario_stage] = analyze_final_learning_result(
            scenario_stage, candidates, stage_selections, stage_rewards, resband
        )
    
    # 生成ResBand价值分析报告
    generate_resband_value_report(learning_results)

def simulate_simple_static_with_variance(arm, episode_in_stage):
    """模拟简单静态场景，不同分辨率有性能差异"""
    progress = min(episode_in_stage / 20.0, 1.0)
    
    # 简单场景：粗分辨率(臂0)最优，中等分辨率(臂1)稍差
    if arm == 0:  # 粗分辨率
        base_reward = 25000 + progress * 5000
        variance = 1500
    elif arm == 1:  # 中等分辨率
        base_reward = 23000 + progress * 5000  # 稍低但仍可接受
        variance = 1200
    else:  # 其他分辨率
        base_reward = 20000 + progress * 5000
        variance = 2000
    
    episode_reward = np.random.normal(base_reward, variance)
    episode_reward = max(10000, episode_reward)
    
    critic_loss = np.random.normal(0.3 - progress * 0.15, 0.08)
    violations = np.random.poisson(0.05 * (1 - progress))
    success = episode_reward > 22000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def simulate_complex_static_with_variance(arm, episode_in_stage):
    """模拟复杂静态场景，中等分辨率最优"""
    progress = min(episode_in_stage / 25.0, 1.0)
    
    # 复杂静态：中等分辨率(臂1)最优，其他分辨率各有优劣
    if arm == 0:  # 粗分辨率
        base_reward = 20000 + progress * 6000  # 效率高但精度不足
        variance = 2500
    elif arm == 1:  # 中等分辨率
        base_reward = 26000 + progress * 7000  # 最优平衡
        variance = 1500
    elif arm == 2:  # 精细分辨率
        base_reward = 24000 + progress * 6500  # 精度高但效率低
        variance = 1800
    else:
        base_reward = 18000 + progress * 5000
        variance = 3000
    
    episode_reward = np.random.normal(base_reward, variance)
    episode_reward = max(8000, episode_reward)
    
    critic_loss = np.random.normal(0.4 - progress * 0.2, 0.1)
    violations = np.random.poisson(0.15 * (1 - progress))
    success = episode_reward > 20000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def simulate_complex_dynamic_with_variance(arm, episode_in_stage):
    """模拟复杂动态场景，精细分辨率明显更优"""
    progress = min(episode_in_stage / 30.0, 1.0)
    
    # 复杂动态：精细分辨率(臂2)明显最优，中等分辨率(臂1)勉强可用
    if arm == 1:  # 中等分辨率
        base_reward = 18000 + progress * 8000  # 勉强可用
        variance = 3000
    elif arm == 2:  # 精细分辨率
        base_reward = 28000 + progress * 10000  # 明显最优
        variance = 2000
    else:  # 其他分辨率
        base_reward = 12000 + progress * 5000  # 明显不足
        variance = 4000
    
    episode_reward = np.random.normal(base_reward, variance)
    episode_reward = max(5000, episode_reward)
    
    critic_loss = np.random.normal(0.6 - progress * 0.3, 0.15)
    violations = np.random.poisson(0.3 * (1 - progress) * (3 - arm))  # 低分辨率更容易违反
    success = episode_reward > 18000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def analyze_learning_progress(resband, candidates, selections, rewards, episodes):
    """分析学习进展"""
    print(f"   📈 前{episodes}个episode学习进展:")
    
    # 统计各候选臂的选择次数
    for arm in candidates:
        count = selections.count(arm)
        percentage = count / episodes * 100
        avg_reward = np.mean([rewards[i] for i, sel in enumerate(selections) if sel == arm]) if count > 0 else 0
        print(f"     臂{arm}: {count}次 ({percentage:.1f}%), 平均奖励={avg_reward:.1f}")
    
    # 显示当前ResBand的学习状态
    print(f"     ResBand状态: Q={[resband.Q[arm] for arm in candidates]}")

def analyze_final_learning_result(scenario, candidates, selections, rewards, resband):
    """分析最终学习结果"""
    print(f"\n📊 {scenario} 最终学习结果:")
    
    # 计算各臂的实际性能
    arm_performance = {}
    for arm in candidates:
        arm_selections = [i for i, sel in enumerate(selections) if sel == arm]
        if arm_selections:
            arm_rewards = [rewards[i] for i in arm_selections]
            arm_performance[arm] = {
                'count': len(arm_selections),
                'avg_reward': np.mean(arm_rewards),
                'selection_rate': len(arm_selections) / len(selections)
            }
        else:
            arm_performance[arm] = {'count': 0, 'avg_reward': 0, 'selection_rate': 0}
    
    # 找出实际最优臂
    best_arm = max(candidates, key=lambda x: arm_performance[x]['avg_reward'])
    
    # 分析ResBand是否学会了选择最优臂
    final_10_selections = selections[-10:]  # 最后10次选择
    best_arm_final_rate = final_10_selections.count(best_arm) / 10
    
    print(f"   实际最优臂: 臂{best_arm} (平均奖励={arm_performance[best_arm]['avg_reward']:.1f})")
    print(f"   ResBand最后10次选择最优臂的比例: {best_arm_final_rate:.1%}")
    
    if best_arm_final_rate >= 0.7:
        learning_quality = "优秀"
    elif best_arm_final_rate >= 0.5:
        learning_quality = "良好"
    else:
        learning_quality = "需改进"
    
    print(f"   学习质量评估: {learning_quality}")
    
    return {
        'candidates': candidates,
        'best_arm': best_arm,
        'final_selection_rate': best_arm_final_rate,
        'learning_quality': learning_quality,
        'arm_performance': arm_performance
    }

def generate_resband_value_report(learning_results):
    """生成ResBand价值分析报告"""
    print(f"\n🎯 ResBand算法真正价值分析报告")
    print("=" * 60)
    
    print(f"📋 核心价值验证:")
    
    total_scenarios = len(learning_results)
    successful_learning = 0
    
    for scenario, result in learning_results.items():
        candidates = result['candidates']
        learning_quality = result['learning_quality']
        
        print(f"\n   {scenario}:")
        print(f"     候选分辨率数量: {len(candidates)}")
        print(f"     学习质量: {learning_quality}")
        print(f"     最优臂识别: 臂{result['best_arm']}")
        print(f"     最终选择准确率: {result['final_selection_rate']:.1%}")
        
        if learning_quality in ["优秀", "良好"]:
            successful_learning += 1
    
    success_rate = successful_learning / total_scenarios
    
    print(f"\n🏆 ResBand算法价值总结:")
    print(f"   学习成功率: {success_rate:.1%}")
    
    if success_rate >= 0.8:
        print(f"   ✅ ResBand算法价值显著：能够在候选集合内智能学习最优分辨率")
        print(f"   ✅ 不是简单的规则映射，而是真正的自适应学习算法")
        print(f"   ✅ 在每个场景内都能发现性能差异并收敛到最优选择")
    elif success_rate >= 0.5:
        print(f"   ⚠️  ResBand算法有一定价值，但仍有改进空间")
    else:
        print(f"   ❌ ResBand算法价值有限，可能需要重新设计")
    
    print(f"\n🎯 与固定规则映射的区别:")
    print(f"   固定规则: 简单静态→臂0, 复杂静态→臂1, 复杂动态→臂2")
    print(f"   ResBand: 在候选集合内通过UCB算法学习最优选择")
    print(f"   优势: 能够发现意外的性能差异，适应环境变化")

if __name__ == "__main__":
    test_resband_true_learning_value()
