"""
训练结果GIF生成器 - 巡飞简化ver
基于训练结果生成导航动画，与简化ver1的staged_gif_generator.py功能相同
"""

import numpy as np
import torch
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import os
import glob
import json
from datetime import datetime

# 设置matplotlib使用英文字体，避免中文字体问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

from loitering_munition_environment import LoiteringMunitionEnvironment
from td3_network import StabilizedTD3Controller
from environment_config import get_environment_config, get_td3_config

def find_training_directory(target_dir=None):
    """查找训练结果目录"""
    print("🔍 查找训练结果目录...")

    # 如果指定了目标目录，优先使用
    if target_dir and os.path.isdir(target_dir):
        print(f"✅ 使用指定训练目录: {target_dir}")
        return target_dir

    # 首先检查历史训练目录（向后兼容）
    historical_dir = "loitering_munition_staged_training_20250727_172938"
    if os.path.isdir(historical_dir):
        print(f"✅ 找到历史训练目录: {historical_dir}")
        return historical_dir

    # 查找新的results/training目录中的训练结果
    results_training_dir = os.path.join("results", "training")
    if os.path.isdir(results_training_dir):
        # 查找training目录下的所有训练结果目录
        pattern = os.path.join(results_training_dir, "loitering_munition_staged_training_*")
        all_matches = glob.glob(pattern)

        # 只保留目录，排除文件
        training_dirs = []
        for d in all_matches:
            if os.path.isdir(d):
                # 确保是纯粹的训练目录，不包含其他后缀
                import re
                basename = os.path.basename(d)
                if re.match(r'^loitering_munition_staged_training_\d{8}_\d{6}$', basename):
                    training_dirs.append(d)

        if training_dirs:
            # 按时间戳排序，获取最新的
            training_dirs.sort(reverse=True)
            latest_dir = training_dirs[0]
            print(f"✅ 找到最新训练目录: {latest_dir}")
            return latest_dir

    # 最后检查当前目录下的旧格式训练目录（向后兼容）
    pattern = "loitering_munition_staged_training_*"
    all_matches = glob.glob(pattern)

    # 只保留目录，排除文件
    training_dirs = []
    for d in all_matches:
        if os.path.isdir(d):
            # 确保是纯粹的训练目录，不包含其他后缀
            import re
            if re.match(r'^loitering_munition_staged_training_\d{8}_\d{6}$', d):
                training_dirs.append(d)

    if training_dirs:
        # 按时间戳排序，获取最新的
        training_dirs.sort(reverse=True)
        latest_dir = training_dirs[0]
        print(f"✅ 找到训练目录: {latest_dir}")
        return latest_dir

    print("❌ 未找到训练结果目录")
    print("💡 请确保已运行分阶段训练并生成了训练结果")
    print("💡 训练结果应该在 results/training/ 目录中")
    return None

def load_trained_model(training_dir, stage_preference=None):
    """加载训练好的模型"""
    # 首先在models子目录中查找模型文件
    models_dir = os.path.join(training_dir, "models")
    if os.path.isdir(models_dir):
        model_files = glob.glob(os.path.join(models_dir, '*_model*.pth'))
    else:
        # 向后兼容：在训练目录根目录查找
        model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))

    if not model_files:
        print("❌ 未找到模型文件")
        print(f"💡 检查路径: {models_dir if os.path.isdir(models_dir) else training_dir}")
        return None, None

    # 按阶段排序模型文件
    stage_models = {}
    for model_file in model_files:
        filename = os.path.basename(model_file)
        if 'stage_1' in filename:
            stage_models[1] = model_file
        elif 'stage_2' in filename:
            stage_models[2] = model_file
        elif 'stage_3' in filename:
            stage_models[3] = model_file

    print(f"📦 找到模型文件: {list(stage_models.keys())}")

    # 选择要测试的模型
    if stage_preference and stage_preference in stage_models:
        selected_model = stage_models[stage_preference]
        selected_stage = stage_preference
    else:
        # 默认选择最高阶段的模型
        selected_stage = max(stage_models.keys())
        selected_model = stage_models[selected_stage]

    print(f"📦 加载模型: {os.path.basename(selected_model)} (Stage {selected_stage})")

    # 创建控制器并加载模型
    td3_config = get_td3_config()
    td3_config.update({
        'state_dim': 15,
        'action_dim': 3,
        'max_action': 1.0
    })
    controller = StabilizedTD3Controller(td3_config)
    controller.load(selected_model)

    # 设置为评估模式（测试模式，不学习）
    controller.actor.eval()
    controller.critic.eval()

    print(f"✅ 模型已设置为评估模式（测试模式）")

    return controller, selected_stage

def generate_navigation_trajectory(controller, stage=1, max_steps=2000):
    """生成导航轨迹 - 使用与训练时相同的DWA+TD3融合逻辑"""
    print(f"🚀 生成导航轨迹 (最大{max_steps}步)...")
    print("🔧 使用DWA+TD3融合逻辑 (与训练时相同)")
    print("📋 测试模式: 只推理，不学习，不更新模型")

    # 根据阶段选择环境配置
    env_configs = {
        1: 'stage1_simple',
        2: 'stage2_complex',
        3: 'stage3_dynamic'
    }

    env_name = env_configs.get(stage, 'stage1_simple')
    print(f"🌍 使用环境配置: {env_name} (Stage {stage})")

    # 创建环境
    env_config = get_environment_config(env_name)
    env = LoiteringMunitionEnvironment(
        bounds=[2000, 2000, 2000],
        environment_config=env_config,
        reward_type='simplified'
    )

    # 创建DWA控制器
    from loitering_munition_dwa import LoiteringMunitionDWA
    dwa = LoiteringMunitionDWA(dt=0.1)

    # 重置环境
    obs = env.reset()

    # 确保环境的max_steps不会限制我们的测试
    env.max_steps = max_steps
    env.step_count = 0

    # 记录轨迹数据
    trajectory = [env.state[:3].copy()]
    rewards = []

    # 记录约束相关数据
    velocities = [env.state[3]]  # 速度
    gammas = [env.state[4]]      # 航迹倾斜角
    psis = [env.state[5]]        # 偏航角
    accelerations_T = []         # 切向加速度
    accelerations_N = []         # 法向加速度
    control_inputs = []          # 控制输入

    print(f"起点: [{env.start[0]:.1f}, {env.start[1]:.1f}, {env.start[2]:.1f}]")
    print(f"目标: [{env.goal[0]:.1f}, {env.goal[1]:.1f}, {env.goal[2]:.1f}]")
    print(f"静态障碍物数量: {len(env.obstacles)}")
    print(f"动态障碍物数量: {len(env.dynamic_obstacles)}")

    # 计算初始距离
    initial_distance = np.linalg.norm(env.state[:3] - env.goal)
    print(f"初始距离: {initial_distance:.1f}m")

    step = 0
    episode_reward = 0

    # 使用torch.no_grad()确保测试时不计算梯度，不更新模型
    with torch.no_grad():
        while step < max_steps:
            # 获取观测值（15维）
            state = env._get_observation()

            # 完全复制训练时的动作生成逻辑
            # 1. DWA生成安全动作集
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
            )

            if safe_controls:
                # 测试时使用后期阶段的逻辑（TD3从安全动作集中选择最优的）
                # 2. 将安全控制转换为归一化动作集
                safe_actions = [dwa.get_normalized_action(control) for control in safe_controls]

                # 3. TD3选择动作（测试时不添加噪声）
                td3_action = controller.select_action(state, noise=0)

                # 4. 从安全动作集中找到最接近TD3选择的动作
                best_idx = 0
                min_distance = float('inf')
                for i, safe_action in enumerate(safe_actions):
                    distance = np.linalg.norm(td3_action - safe_action)
                    if distance < min_distance:
                        min_distance = distance
                        best_idx = i

                action = safe_actions[best_idx]
            else:
                # 如果没有安全动作，使用紧急制动（与训练时相同）
                action = np.array([-0.5, 0.0, 0.0])
                print(f"⚠️ 步骤 {step}: 没有安全动作，使用紧急制动")

            # 5. 将归一化动作转换为实际控制输入（与训练时完全相同）
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])

            # 6. 执行控制输入
            next_obs, reward, done, info = env.step(control_input)

            # 记录数据
            trajectory.append(env.state[:3].copy())
            rewards.append(reward)
            episode_reward += reward

            # 记录约束相关数据
            velocities.append(env.state[3])
            gammas.append(env.state[4])
            psis.append(env.state[5])
            accelerations_T.append(control_input[0])
            accelerations_N.append(control_input[1])
            control_inputs.append(control_input.copy())

            step += 1

            # 每50步输出一次进度
            if step % 50 == 0:
                current_dist = np.linalg.norm(env.state[:3] - env.goal)
                print(f"步骤 {step}: 距离目标 {current_dist:.1f}m, 安全动作数 {len(safe_controls)}, 当前奖励 {reward:.1f}")

            # 检查是否完成
            if done:
                final_distance = np.linalg.norm(env.state[:3] - env.goal)
                success = final_distance <= 50.0
                print(f"✅ 任务完成于第{step}步")
                print(f"最终距离: {final_distance:.1f}m")
                print(f"任务状态: {'成功' if success else '失败'}")
                print(f"距离改善: {initial_distance - final_distance:.1f}m")
                break

    # 最终统计
    final_distance = np.linalg.norm(env.state[:3] - env.goal)
    success = final_distance <= 50.0
    distance_improvement = initial_distance - final_distance

    print(f"总奖励: {episode_reward:.1f}")
    print(f"轨迹点数: {len(trajectory)}")
    print(f"最终距离: {final_distance:.1f}m")
    print(f"距离改善: {distance_improvement:.1f}m ({distance_improvement/initial_distance*100:.1f}%)")
    print(f"任务状态: {'✅ 成功' if success else '❌ 失败'}")

    return {
        'trajectory': np.array(trajectory),
        'rewards': rewards,
        'environment': env,
        'total_steps': step,
        'total_reward': episode_reward,
        'velocities': np.array(velocities),
        'gammas': np.array(gammas),
        'psis': np.array(psis),
        'accelerations_T': np.array(accelerations_T),
        'accelerations_N': np.array(accelerations_N),
        'control_inputs': np.array(control_inputs),
        'initial_distance': initial_distance,
        'final_distance': final_distance,
        'success': success,
        'stage': stage
    }

def create_gif_animation(trajectory_data, fps=12):
    """创建GIF动画"""
    print(f"🎬 创建GIF动画 (FPS: {fps})...")

    trajectory = trajectory_data['trajectory']
    env = trajectory_data['environment']
    stage = trajectory_data.get('stage', 1)

    # 创建图形
    fig = plt.figure(figsize=(14, 12))
    ax = fig.add_subplot(111, projection='3d')

    # 设置坐标轴范围
    ax.set_xlim(0, env.bounds[0])
    ax.set_ylim(0, env.bounds[1])
    ax.set_zlim(0, env.bounds[2])
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    
    # 绘制静态元素
    # 起点和目标
    ax.scatter(*env.start, color='green', s=200, marker='o', label='Start', alpha=0.9)
    ax.scatter(*env.goal, color='red', s=200, marker='*', label='Target', alpha=0.9)
    
    # 绘制静态障碍物
    obstacle_colors = ['orange', 'yellow', 'purple', 'brown', 'pink', 'cyan', 'magenta']
    for i, obstacle in enumerate(env.obstacles):
        # 高分辨率球体
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)

        center = obstacle['center']
        radius = obstacle['radius']
        x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
        y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
        z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))

        color = obstacle_colors[i % len(obstacle_colors)]
        ax.plot_surface(x, y, z, alpha=0.4, color=color, edgecolor='black', linewidth=0.1)

    # 绘制动态障碍物（如果有）
    dynamic_surfaces = []
    if hasattr(env, 'dynamic_obstacles') and env.dynamic_obstacles:
        print(f"🔄 检测到 {len(env.dynamic_obstacles)} 个动态障碍物")
        for i, dyn_obstacle in enumerate(env.dynamic_obstacles):
            u = np.linspace(0, 2 * np.pi, 15)
            v = np.linspace(0, np.pi, 15)

            center = dyn_obstacle['center']
            radius = dyn_obstacle['radius']
            x = center[0] + radius * np.outer(np.cos(u), np.sin(v))
            y = center[1] + radius * np.outer(np.sin(u), np.sin(v))
            z = center[2] + radius * np.outer(np.ones(np.size(u)), np.cos(v))

            # 动态障碍物使用红色系
            surface = ax.plot_surface(x, y, z, alpha=0.6, color='red', edgecolor='darkred', linewidth=0.2)
            dynamic_surfaces.append(surface)
    
    # 初始化动画元素
    line, = ax.plot([], [], [], 'b-', linewidth=3, alpha=0.8, label='Trajectory')
    point, = ax.plot([], [], [], 'bo', markersize=8, alpha=0.9)
    
    ax.legend()

    # 根据阶段设置标题
    stage_names = {1: 'Stage 1 - Simple Environment', 2: 'Stage 2 - Complex Environment', 3: 'Stage 3 - Dynamic Environment'}
    stage_title = stage_names.get(stage, f'Stage {stage}')
    ax.set_title(f'Loitering Munition Navigation - {stage_title}', fontsize=14, fontweight='bold')

    # 添加信息文本框
    info_text = ax.text2D(0.02, 0.98, '', transform=ax.transAxes, fontsize=10,
                         verticalalignment='top', fontweight='bold',
                         bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

    def animate(frame):
        """动画更新函数"""
        if frame < len(trajectory):
            # 更新轨迹线
            line.set_data(trajectory[:frame+1, 0], trajectory[:frame+1, 1])
            line.set_3d_properties(trajectory[:frame+1, 2])

            # 更新当前位置点
            current_pos = trajectory[frame]
            point.set_data([current_pos[0]], [current_pos[1]])
            point.set_3d_properties([current_pos[2]])

            # 更新标题显示当前步数
            ax.set_title(f'Loitering Munition Navigation - {stage_title} - Step {frame+1}/{len(trajectory)}',
                        fontsize=14, fontweight='bold')

            # 更新信息文本
            current_dist = np.linalg.norm(current_pos - env.goal)
            if frame < len(trajectory_data['rewards']):
                current_reward = trajectory_data['rewards'][frame]
                info_text.set_text(f'Step: {frame+1}/{len(trajectory)}\n'
                                  f'Position: ({current_pos[0]:.0f}, {current_pos[1]:.0f}, {current_pos[2]:.0f})\n'
                                  f'Distance to Target: {current_dist:.1f}m\n'
                                  f'Current Reward: {current_reward:.1f}')

        return line, point
    
    # 创建动画
    anim = animation.FuncAnimation(
        fig, animate, frames=len(trajectory), 
        interval=1000//fps, blit=False, repeat=True
    )
    
    # 生成文件名并保存到results/gifs目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    success_status = 'SUCCESS' if trajectory_data.get('success', False) else 'FAILED'

    # 确保gifs目录存在
    gifs_dir = os.path.join("results", "gifs", f"stage{stage}")
    os.makedirs(gifs_dir, exist_ok=True)

    gif_filename = f'loitering_munition_stage{stage}_{success_status}_{timestamp}.gif'
    gif_filepath = os.path.join(gifs_dir, gif_filename)

    print(f"💾 保存GIF动画: {gif_filepath}")
    print("⏳ 正在生成GIF，请稍候...")
    anim.save(gif_filepath, writer='pillow', fps=fps, dpi=100)
    plt.close()

    return gif_filepath

def generate_static_plots(trajectory_data):
    """生成静态分析图"""
    print("📊 生成静态分析图...")
    
    trajectory = trajectory_data['trajectory']
    rewards = trajectory_data['rewards']
    env = trajectory_data['environment']
    
    # 创建多子图
    fig = plt.figure(figsize=(16, 12))
    
    # 3D轨迹图
    ax1 = fig.add_subplot(221, projection='3d')
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 'b-', linewidth=3, alpha=0.8)
    ax1.scatter(*env.start, color='green', s=100, marker='o', label='Start')
    ax1.scatter(*env.goal, color='red', s=100, marker='*', label='Target')
    ax1.scatter(*trajectory[-1], color='blue', s=100, marker='X', label='End')
    
    # 绘制障碍物
    for i, obstacle in enumerate(env.obstacles):
        center = obstacle['center']
        ax1.scatter(*center, color='orange', s=50, marker='s', alpha=0.7)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('3D Trajectory')
    ax1.legend()
    
    # XY平面投影
    ax2 = fig.add_subplot(222)
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2, alpha=0.8)
    ax2.scatter(*env.start[:2], color='green', s=100, marker='o', label='Start')
    ax2.scatter(*env.goal[:2], color='red', s=100, marker='*', label='Target')
    
    # 绘制障碍物投影
    for obstacle in env.obstacles:
        center = obstacle['center']
        radius = obstacle['radius']
        circle = plt.Circle(center[:2], radius, fill=False, color='orange', alpha=0.7)
        ax2.add_patch(circle)
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('XY Plane Projection')
    ax2.legend()
    ax2.set_aspect('equal')
    ax2.grid(True, alpha=0.3)
    
    # 奖励曲线
    ax3 = fig.add_subplot(223)
    ax3.plot(range(len(rewards)), rewards, 'g-', linewidth=2)
    ax3.set_xlabel('Step')
    ax3.set_ylabel('Reward')
    ax3.set_title('Reward Curve')
    ax3.grid(True, alpha=0.3)
    
    # 距离变化
    ax4 = fig.add_subplot(224)
    distances = [np.linalg.norm(pos - env.goal) for pos in trajectory]
    ax4.plot(range(len(distances)), distances, 'r-', linewidth=2)
    ax4.set_xlabel('Step')
    ax4.set_ylabel('Distance to Target (m)')
    ax4.set_title('Distance to Target')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存静态图到results/analysis目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    analysis_dir = os.path.join("results", "analysis")
    os.makedirs(analysis_dir, exist_ok=True)

    static_filename = f'loitering_munition_analysis_{timestamp}.png'
    static_filepath = os.path.join(analysis_dir, static_filename)
    plt.savefig(static_filepath, dpi=300, bbox_inches='tight')
    plt.close()

    return static_filepath

def generate_constraint_analysis(trajectory_data):
    """生成详细的约束分析报告"""
    print("📊 生成约束分析报告...")

    env = trajectory_data['environment']
    velocities = trajectory_data['velocities']
    gammas = trajectory_data['gammas']
    psis = trajectory_data['psis']
    accelerations_T = trajectory_data['accelerations_T']
    accelerations_N = trajectory_data['accelerations_N']

    # 从环境中读取约束参数
    V_min = env.V_min
    V_max = env.V_max
    a_T_max = env.a_T_max
    a_N_max = env.a_N_max
    gamma_max = env.gamma_max

    # 创建约束分析图
    fig = plt.figure(figsize=(20, 15))

    # 时间轴
    time_steps = np.arange(len(velocities))

    # 1. 速度约束分析
    ax1 = fig.add_subplot(231)
    ax1.plot(time_steps, velocities, 'b-', linewidth=2, label='Actual Velocity')
    ax1.axhline(y=V_min, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'V_min = {V_min:.1f} m/s')
    ax1.axhline(y=V_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'V_max = {V_max:.1f} m/s')
    ax1.fill_between(time_steps, V_min, V_max, alpha=0.2, color='green', label='Safe Zone')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Velocity (m/s)')
    ax1.set_title('Velocity Constraints', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 检查违反情况
    v_violations = np.sum((velocities < V_min) | (velocities > V_max))
    ax1.text(0.02, 0.98, f'Violations: {v_violations}/{len(velocities)}',
             transform=ax1.transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if v_violations > 0 else 'lightgreen'))

    # 2. 切向加速度约束分析
    ax2 = fig.add_subplot(232)
    if len(accelerations_T) > 0:
        ax2.plot(time_steps[1:], accelerations_T, 'g-', linewidth=2, label='Actual a_T')
        ax2.axhline(y=a_T_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_T_max = {a_T_max:.1f} m/s²')
        ax2.axhline(y=-a_T_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_T_min = {-a_T_max:.1f} m/s²')
        ax2.fill_between(time_steps[1:], -a_T_max, a_T_max, alpha=0.2, color='green', label='Safe Zone')

        # 检查违反情况
        aT_violations = np.sum((accelerations_T < -a_T_max) | (accelerations_T > a_T_max))
        ax2.text(0.02, 0.98, f'Violations: {aT_violations}/{len(accelerations_T)}',
                 transform=ax2.transAxes, fontsize=12, verticalalignment='top',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if aT_violations > 0 else 'lightgreen'))

    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Tangential Acceleration (m/s²)')
    ax2.set_title('Tangential Acceleration Constraints', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 法向加速度约束分析
    ax3 = fig.add_subplot(233)
    if len(accelerations_N) > 0:
        ax3.plot(time_steps[1:], accelerations_N, 'orange', linewidth=2, label='Actual a_N')
        ax3.axhline(y=a_N_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_N_max = {a_N_max:.1f} m/s²')
        ax3.axhline(y=-a_N_max, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'a_N_min = {-a_N_max:.1f} m/s²')
        ax3.fill_between(time_steps[1:], -a_N_max, a_N_max, alpha=0.2, color='green', label='Safe Zone')

        # 检查违反情况
        aN_violations = np.sum((accelerations_N < -a_N_max) | (accelerations_N > a_N_max))
        ax3.text(0.02, 0.98, f'Violations: {aN_violations}/{len(accelerations_N)}',
                 transform=ax3.transAxes, fontsize=12, verticalalignment='top',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if aN_violations > 0 else 'lightgreen'))

    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Normal Acceleration (m/s²)')
    ax3.set_title('Normal Acceleration Constraints', fontsize=14, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 航迹倾斜角约束分析
    ax4 = fig.add_subplot(234)
    gammas_deg = np.degrees(gammas)
    gamma_max_deg = np.degrees(gamma_max)
    ax4.plot(time_steps, gammas_deg, 'purple', linewidth=2, label='Actual γ')
    ax4.axhline(y=gamma_max_deg, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'γ_max = {gamma_max_deg:.1f}°')
    ax4.axhline(y=-gamma_max_deg, color='red', linestyle='--', linewidth=2, alpha=0.8, label=f'γ_min = {-gamma_max_deg:.1f}°')
    ax4.fill_between(time_steps, -gamma_max_deg, gamma_max_deg, alpha=0.2, color='green', label='Safe Zone')
    ax4.set_xlabel('Time Steps')
    ax4.set_ylabel('Flight Path Angle (degrees)')
    ax4.set_title('Flight Path Angle Constraints', fontsize=14, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 检查违反情况
    gamma_violations = np.sum((gammas < -gamma_max) | (gammas > gamma_max))
    ax4.text(0.02, 0.98, f'Violations: {gamma_violations}/{len(gammas)}',
             transform=ax4.transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow' if gamma_violations > 0 else 'lightgreen'))

    # 5. 约束违反统计
    ax5 = fig.add_subplot(235)
    constraint_names = ['Velocity', 'Tangential Acc', 'Normal Acc', 'Flight Path Angle']
    violation_counts = [v_violations, aT_violations if len(accelerations_T) > 0 else 0,
                       aN_violations if len(accelerations_N) > 0 else 0, gamma_violations]
    total_steps = [len(velocities), len(accelerations_T), len(accelerations_N), len(gammas)]

    colors = ['red' if count > 0 else 'green' for count in violation_counts]
    bars = ax5.bar(constraint_names, violation_counts, color=colors, alpha=0.7, edgecolor='black')

    # 添加百分比标签
    for i, (bar, count, total) in enumerate(zip(bars, violation_counts, total_steps)):
        if total > 0:
            percentage = (count / total) * 100
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{count}\n({percentage:.1f}%)', ha='center', va='bottom', fontsize=10, fontweight='bold')

    ax5.set_ylabel('Number of Violations')
    ax5.set_title('Constraint Violations Summary', fontsize=14, fontweight='bold')
    ax5.tick_params(axis='x', rotation=45)

    # 6. 约束遵守率饼图
    ax6 = fig.add_subplot(236)
    total_violations = sum(violation_counts)
    total_checks = sum(total_steps)
    compliance_rate = ((total_checks - total_violations) / total_checks) * 100 if total_checks > 0 else 100

    sizes = [compliance_rate, 100 - compliance_rate]
    labels = [f'Compliant\n({compliance_rate:.1f}%)', f'Violations\n({100-compliance_rate:.1f}%)']
    colors_pie = ['lightgreen', 'lightcoral']

    wedges, texts, autotexts = ax6.pie(sizes, labels=labels, colors=colors_pie, autopct='',
                                       startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    ax6.set_title('Overall Constraint Compliance', fontsize=14, fontweight='bold')

    plt.tight_layout()

    # 保存约束分析图到results/analysis目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    analysis_dir = os.path.join("results", "analysis")
    os.makedirs(analysis_dir, exist_ok=True)

    constraint_filename = f'loitering_munition_constraints_{timestamp}.png'
    constraint_filepath = os.path.join(analysis_dir, constraint_filename)
    plt.savefig(constraint_filepath, dpi=300, bbox_inches='tight')
    plt.close()

    # 生成约束分析报告
    report = {
        'constraint_parameters': {
            'V_min': V_min,
            'V_max': V_max,
            'a_T_max': a_T_max,
            'a_N_max': a_N_max,
            'gamma_max_deg': gamma_max_deg
        },
        'violations': {
            'velocity': v_violations,
            'tangential_acceleration': aT_violations if len(accelerations_T) > 0 else 0,
            'normal_acceleration': aN_violations if len(accelerations_N) > 0 else 0,
            'flight_path_angle': gamma_violations
        },
        'compliance_rate': compliance_rate,
        'total_steps': len(velocities)
    }

    return constraint_filepath, report

def print_training_summary(training_dir):
    """打印训练摘要"""
    try:
        # 首先在data子目录中查找结果文件
        data_dir = os.path.join(training_dir, "data")
        if os.path.isdir(data_dir):
            results_file = os.path.join(data_dir, 'staged_training_results.json')
        else:
            # 向后兼容：在训练目录根目录查找
            results_file = os.path.join(training_dir, 'staged_training_results.json')

        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            print(f"\n📋 训练摘要 from {training_dir}:")
            print("=" * 50)

            if 'stages' in results:
                for stage_key, stage_data in results['stages'].items():
                    if 'error' not in stage_data:
                        print(f"{stage_key}:")
                        print(f"  • 总episodes: {stage_data.get('total_episodes', 'N/A')}")
                        print(f"  • 成功率: {stage_data.get('success_rate', 0):.2%}")
                        print(f"  • 平均奖励: {stage_data.get('final_avg_reward', 0):.1f}")
                        print(f"  • 随机阶段成功率: {stage_data.get('random_phase_success_rate', 0):.2%}")
                        print(f"  • 固定阶段成功率: {stage_data.get('fixed_phase_success_rate', 0):.2%}")

            print("=" * 50)

    except Exception as e:
        print(f"⚠️ 无法读取训练摘要: {e}")

def select_stage_interactively(training_dir):
    """交互式选择测试阶段"""
    print("\n🎯 可用的训练阶段:")
    print("=" * 40)

    # 检查可用的模型文件
    models_dir = os.path.join(training_dir, "models")
    if os.path.isdir(models_dir):
        model_files = glob.glob(os.path.join(models_dir, '*_model*.pth'))
    else:
        # 向后兼容：在训练目录根目录查找
        model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))
    available_stages = []

    for model_file in model_files:
        filename = os.path.basename(model_file)
        if 'stage_1' in filename:
            available_stages.append(1)
        elif 'stage_2' in filename:
            available_stages.append(2)
        elif 'stage_3' in filename:
            available_stages.append(3)

    available_stages.sort()

    # 显示可用阶段和对应的训练结果
    stage_info = {
        1: {"name": "阶段1 - 简单环境基础训练", "success_rate": "98.3%", "env": "简单静态障碍物"},
        2: {"name": "阶段2 - 复杂静态环境训练", "success_rate": "91.6%", "env": "高密度静态障碍物"},
        3: {"name": "阶段3 - 动态环境适应训练", "success_rate": "4.0%", "env": "动态障碍物环境"}
    }

    for stage in available_stages:
        info = stage_info.get(stage, {})
        print(f"  {stage}. {info.get('name', f'阶段{stage}')}")
        print(f"     成功率: {info.get('success_rate', 'N/A')} | 环境: {info.get('env', 'N/A')}")

    print("=" * 40)
    print("💡 使用方法:")
    for stage in available_stages:
        print(f"   python test_training_gif_generator.py --stage {stage}")
    print("💡 或者使用默认参数:")
    print("   python test_training_gif_generator.py --stage 1  # 推荐，成功率最高")
    print("=" * 40)

    # 由于交互式输入在某些环境中可能有问题，我们提供默认选择
    print("🔧 由于环境限制，自动选择阶段1进行测试")
    print("💡 如需测试其他阶段，请使用 --stage 参数")

    return 1  # 默认返回阶段1

def main(max_steps=1500, fps=12, test_stage=None, target_dir=None):
    """主函数"""
    print("🎬 训练结果GIF生成器 - 巡飞简化ver")
    print("=" * 60)
    print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⚙️  默认设置: 最大步数={max_steps}, 帧率={fps}fps")
    print()

    # 1. 查找训练目录
    training_dir = find_training_directory(target_dir)
    if not training_dir:
        print("❌ 未找到训练结果，请先运行训练")
        return False

    # 2. 打印训练摘要
    print_training_summary(training_dir)

    # 3. 交互式选择测试阶段（如果未指定）
    if test_stage is None:
        test_stage = select_stage_interactively(training_dir)
        if test_stage is None:
            return False

    # 4. 加载训练好的模型
    controller, selected_stage = load_trained_model(training_dir, test_stage)
    if not controller:
        print("❌ 模型加载失败")
        return False

    # 5. 确认开始测试
    print(f"\n🚀 开始测试阶段 {selected_stage}")
    print(f"⚙️  测试参数: 最大步数={max_steps}, 帧率={fps}fps")
    print("⏳ 正在生成导航轨迹...")

    # 6. 生成导航轨迹
    trajectory_data = generate_navigation_trajectory(controller, stage=selected_stage, max_steps=max_steps)

    # 7. 创建GIF动画
    gif_filename = create_gif_animation(trajectory_data, fps=fps)

    # 8. 生成静态分析图
    static_filename = generate_static_plots(trajectory_data)

    # 9. 生成约束分析报告
    constraint_filename, constraint_report = generate_constraint_analysis(trajectory_data)

    # 10. 总结结果
    print(f"\n" + "=" * 60)
    print(f"🎉 阶段 {selected_stage} 测试完成!")
    print(f"=" * 60)
    print(f"📁 GIF动画: {gif_filename}")
    print(f"📊 分析图表: {static_filename}")
    print(f"🔒 约束分析: {constraint_filename}")
    print(f"🎯 轨迹步数: {trajectory_data['total_steps']}")
    print(f"🏆 总奖励: {trajectory_data['total_reward']:.1f}")

    # 检查任务完成情况
    final_pos = trajectory_data['trajectory'][-1]
    goal_pos = trajectory_data['environment'].goal
    final_distance = np.linalg.norm(final_pos - goal_pos)
    success = final_distance <= 50.0

    print(f"📍 最终距离: {final_distance:.1f}m")
    print(f"✅ 任务状态: {'🎯 成功' if success else '❌ 失败'}")

    # 打印约束分析摘要
    print(f"\n🔒 约束遵守情况:")
    print(f"  总体遵守率: {constraint_report['compliance_rate']:.1f}%")
    print(f"  速度约束违反: {constraint_report['violations']['velocity']} 次")
    print(f"  切向加速度违反: {constraint_report['violations']['tangential_acceleration']} 次")
    print(f"  法向加速度违反: {constraint_report['violations']['normal_acceleration']} 次")
    print(f"  航迹倾斜角违反: {constraint_report['violations']['flight_path_angle']} 次")

    # 根据阶段给出建议
    stage_suggestions = {
        1: "💡 阶段1表现优秀，可以尝试测试阶段2",
        2: "💡 阶段2有一定挑战性，建议观察复杂环境中的导航策略",
        3: "💡 阶段3难度很高，预期会有较多失败，这是正常现象"
    }

    if selected_stage in stage_suggestions:
        print(f"\n{stage_suggestions[selected_stage]}")

    print(f"\n📋 生成的文件包含:")
    print(f"  🎬 3D导航动画 (实时信息显示)")
    print(f"  📊 轨迹分析图表 (4个子图)")
    print(f"  🔒 详细约束分析 (6个分析图)")
    print(f"  ✅ 完整的性能验证报告")

    print(f"\n🔄 如需测试其他阶段，请重新运行程序")

    return True

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='训练结果GIF生成器 - 巡飞简化ver')
    parser.add_argument('--steps', type=int, default=1500, help='最大步数 (默认 1500)')
    parser.add_argument('--fps', type=int, default=12, help='帧率 (默认 12)')
    parser.add_argument('--stage', type=int, choices=[1, 2, 3], help='直接指定测试阶段，跳过交互选择 (1, 2, 3)')
    parser.add_argument('--dir', type=str, help='指定训练结果目录')

    args = parser.parse_args()

    print("🎬 训练结果GIF生成器启动")
    print("=" * 50)

    # 如果没有指定stage参数，程序会交互式询问
    if args.stage is None:
        print("💡 程序将询问您要测试哪个阶段")
    else:
        print(f"💡 直接测试阶段 {args.stage}")

    print(f"⚙️  参数设置: 最大步数={args.steps}, 帧率={args.fps}fps")
    print("=" * 50)

    try:
        success = main(max_steps=args.steps, fps=args.fps, test_stage=args.stage, target_dir=args.dir)
        if success:
            print(f"\n🎉 测试完成！")
            print(f"📁 请查看生成的动画和分析文件")
            print(f"\n💡 使用提示:")
            print(f"   • 交互模式: python test_training_gif_generator.py")
            print(f"   • 直接指定: python test_training_gif_generator.py --stage 1")
            print(f"   • 自定义步数: python test_training_gif_generator.py --steps 2000")
            print(f"   • 高帧率: python test_training_gif_generator.py --fps 15")
        else:
            print(f"\n❌ 测试失败或用户取消")
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
        import traceback
        traceback.print_exc()
