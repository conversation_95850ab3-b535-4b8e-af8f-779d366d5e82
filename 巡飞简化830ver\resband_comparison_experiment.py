"""
ResBand算法对比实验
比较固定分辨率、启发式调度和ResBand算法的性能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from staged_training_framework import LoiteringMunitionStagedTrainer
from resolution_bandit import ResolutionConfig, create_paper_configs

class FixedResolutionBaseline:
    """固定分辨率基线"""
    
    def __init__(self, resolution_config: ResolutionConfig):
        self.resolution_config = resolution_config
        self.name = f"Fixed-{resolution_config.name}"
    
    def select_resolution(self, episode):
        return self.resolution_config
    
    def update_performance(self, episode, reward, critic_loss, violations):
        pass  # 固定分辨率不需要更新

class HeuristicScheduler:
    """启发式调度器"""
    
    def __init__(self, configs):
        self.configs = configs
        self.name = "Heuristic-Scheduling"
        self.current_config_idx = 0
        self.episode_count = 0
    
    def select_resolution(self, episode):
        # 每100个episode切换一次分辨率
        if episode > 0 and episode % 100 == 0:
            self.current_config_idx = (self.current_config_idx + 1) % len(self.configs)
            print(f"🔄 启发式调度: 切换到 {self.configs[self.current_config_idx].name}")
        
        return self.configs[self.current_config_idx]
    
    def update_performance(self, episode, reward, critic_loss, violations):
        pass  # 启发式调度不需要更新

def run_comparison_experiment():
    """运行对比实验"""
    print("🔬 ResBand算法对比实验")
    print("=" * 60)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"results/resband_comparison_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义分辨率配置
    configs = create_paper_configs()
    
    # 定义实验方法
    methods = {
        "Fixed-Coarse": FixedResolutionBaseline(configs[0]),  # 粗分辨率
        "Fixed-Medium": FixedResolutionBaseline(configs[1]),  # 中等分辨率
        "Fixed-Fine": FixedResolutionBaseline(configs[2]),    # 细分辨率
        "Heuristic": HeuristicScheduler(configs),             # 启发式调度
        "ResBand": None  # ResBand算法（在训练器中创建）
    }
    
    # 实验配置
    experiment_config = {
        "start_stage": 1,
        "end_stage": 1,  # 只运行第一阶段以节省时间
        "seed": 42,
        "visualization_interval": 50,  # 减少可视化频率
        "episodes_per_method": 200     # 每个方法运行200个episode
    }
    
    # 存储结果
    results = {
        "config": experiment_config,
        "methods": {},
        "comparison": {}
    }
    
    print(f"📊 实验配置:")
    print(f"   阶段: {experiment_config['start_stage']} -> {experiment_config['end_stage']}")
    print(f"   每个方法: {experiment_config['episodes_per_method']} episodes")
    print(f"   输出目录: {output_dir}")
    print()
    
    # 运行每个方法的实验
    for method_name, method in methods.items():
        print(f"🚀 运行 {method_name} 方法...")
        print("-" * 40)
        
        try:
            # 创建训练器
            if method_name == "ResBand":
                # ResBand方法
                trainer = LoiteringMunitionStagedTrainer(
                    start_stage=experiment_config["start_stage"],
                    end_stage=experiment_config["end_stage"],
                    seed=experiment_config["seed"],
                    visualization_interval=experiment_config["visualization_interval"],
                    use_resband=True,
                    resband_config={
                        'exploration_coefficient': 2.0,
                        'stage_length': 20,
                        'reward_weights': (0.7, 0.2, 0.1)
                    }
                )
            else:
                # 其他方法（固定分辨率或启发式）
                trainer = LoiteringMunitionStagedTrainer(
                    start_stage=experiment_config["start_stage"],
                    end_stage=experiment_config["end_stage"],
                    seed=experiment_config["seed"],
                    visualization_interval=experiment_config["visualization_interval"],
                    use_resband=False
                )
            
            # 修改训练配置以运行指定数量的episodes
            from environment_config import TRAINING_STAGES
            TRAINING_STAGES["stage1_simple"]["random_episodes"] = experiment_config["episodes_per_method"]
            TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 0
            TRAINING_STAGES["stage1_simple"]["total_episodes"] = experiment_config["episodes_per_method"]
            
            # 运行训练
            training_results, final_controller = trainer.run_staged_training()
            
            # 保存结果
            method_results = {
                "method_name": method_name,
                "training_results": training_results,
                "final_controller_path": os.path.join(trainer.output_dir, "models") if final_controller else None
            }
            
            results["methods"][method_name] = method_results
            
            print(f"✅ {method_name} 完成")
            
        except Exception as e:
            print(f"❌ {method_name} 失败: {e}")
            results["methods"][method_name] = {"error": str(e)}
    
    # 分析对比结果
    print(f"\n📈 分析对比结果...")
    comparison_results = analyze_comparison_results(results)
    results["comparison"] = comparison_results
    
    # 保存结果
    results_file = os.path.join(output_dir, "comparison_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 生成对比图表
    generate_comparison_plots(results, output_dir)
    
    # 生成报告
    generate_comparison_report(results, output_dir)
    
    print(f"\n🎉 对比实验完成!")
    print(f"📁 结果保存在: {output_dir}")
    print(f"📄 详细结果: {results_file}")
    
    return results

def analyze_comparison_results(results):
    """分析对比结果"""
    comparison = {}
    
    for method_name, method_data in results["methods"].items():
        if "error" in method_data:
            comparison[method_name] = {"error": method_data["error"]}
            continue
        
        training_results = method_data["training_results"]
        stage_results = training_results.get("stages", {})
        
        if not stage_results:
            comparison[method_name] = {"error": "No stage results"}
            continue
        
        # 获取第一个阶段的结果
        stage_key = list(stage_results.keys())[0]
        stage_data = stage_results[stage_key]
        
        if "error" in stage_data:
            comparison[method_name] = {"error": stage_data["error"]}
            continue
        
        # 提取关键指标
        all_rewards = stage_data.get("all_rewards", [])
        success_rate = stage_data.get("success_rate", 0)
        final_avg_reward = stage_data.get("final_avg_reward", 0)
        training_time = stage_data.get("training_time", 0)
        
        comparison[method_name] = {
            "success_rate": success_rate,
            "final_avg_reward": final_avg_reward,
            "training_time": training_time,
            "total_episodes": len(all_rewards),
            "reward_history": all_rewards,
            "convergence_episodes": estimate_convergence_episodes(all_rewards)
        }
    
    return comparison

def estimate_convergence_episodes(rewards, window_size=20, threshold=0.1):
    """估算收敛所需的episode数"""
    if len(rewards) < window_size:
        return len(rewards)
    
    for i in range(window_size, len(rewards)):
        recent_rewards = rewards[i-window_size:i]
        avg_reward = np.mean(recent_rewards)
        
        # 检查是否稳定
        if np.std(recent_rewards) < threshold * abs(avg_reward):
            return i
    
    return len(rewards)

def generate_comparison_plots(results, output_dir):
    """生成对比图表"""
    try:
        comparison = results["comparison"]
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('ResBand算法对比实验结果', fontsize=16)
        
        # 1. 成功率对比
        method_names = []
        success_rates = []
        
        for method_name, data in comparison.items():
            if "error" not in data:
                method_names.append(method_name)
                success_rates.append(data["success_rate"])
        
        if method_names:
            axes[0, 0].bar(method_names, success_rates, alpha=0.7)
            axes[0, 0].set_title('成功率对比')
            axes[0, 0].set_ylabel('成功率')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 最终平均奖励对比
        final_rewards = []
        for method_name in method_names:
            final_rewards.append(comparison[method_name]["final_avg_reward"])
        
        if final_rewards:
            axes[0, 1].bar(method_names, final_rewards, alpha=0.7)
            axes[0, 1].set_title('最终平均奖励对比')
            axes[0, 1].set_ylabel('平均奖励')
            axes[0, 1].tick_params(axis='x', rotation=45)
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 收敛速度对比
        convergence_episodes = []
        for method_name in method_names:
            convergence_episodes.append(comparison[method_name]["convergence_episodes"])
        
        if convergence_episodes:
            axes[1, 0].bar(method_names, convergence_episodes, alpha=0.7)
            axes[1, 0].set_title('收敛速度对比')
            axes[1, 0].set_ylabel('收敛所需episodes')
            axes[1, 0].tick_params(axis='x', rotation=45)
            axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 奖励曲线对比
        for method_name, data in comparison.items():
            if "error" not in data and "reward_history" in data:
                rewards = data["reward_history"]
                episodes = range(1, len(rewards) + 1)
                axes[1, 1].plot(episodes, rewards, label=method_name, alpha=0.7)
        
        axes[1, 1].set_title('奖励曲线对比')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('奖励')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = os.path.join(output_dir, "comparison_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 对比图表已保存: {plot_file}")
        
    except Exception as e:
        print(f"⚠️ 生成图表失败: {e}")

def generate_comparison_report(results, output_dir):
    """生成对比报告"""
    comparison = results["comparison"]
    
    report_lines = []
    report_lines.append("# ResBand算法对比实验报告")
    report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 实验配置
    report_lines.append("## 实验配置")
    config = results["config"]
    report_lines.append(f"- 训练阶段: {config['start_stage']} -> {config['end_stage']}")
    report_lines.append(f"- 每个方法episodes: {config['episodes_per_method']}")
    report_lines.append(f"- 随机种子: {config['seed']}")
    report_lines.append("")
    
    # 结果对比
    report_lines.append("## 结果对比")
    report_lines.append("")
    report_lines.append("| 方法 | 成功率 | 最终平均奖励 | 收敛episodes | 训练时间(s) |")
    report_lines.append("|------|--------|--------------|--------------|-------------|")
    
    for method_name, data in comparison.items():
        if "error" in data:
            report_lines.append(f"| {method_name} | ❌ 失败 | ❌ 失败 | ❌ 失败 | ❌ 失败 |")
        else:
            success_rate = f"{data['success_rate']:.1%}"
            final_reward = f"{data['final_avg_reward']:.2f}"
            convergence = f"{data['convergence_episodes']}"
            training_time = f"{data['training_time']:.1f}"
            report_lines.append(f"| {method_name} | {success_rate} | {final_reward} | {convergence} | {training_time} |")
    
    report_lines.append("")
    
    # 结论
    report_lines.append("## 结论")
    report_lines.append("")
    
    # 找出最佳方法
    best_method = None
    best_score = -float('inf')
    
    for method_name, data in comparison.items():
        if "error" not in data:
            # 综合评分：成功率 * 0.4 + 奖励 * 0.4 + 收敛速度 * 0.2
            score = (data['success_rate'] * 0.4 + 
                    data['final_avg_reward'] / 100 * 0.4 + 
                    (1 - data['convergence_episodes'] / 200) * 0.2)
            
            if score > best_score:
                best_score = score
                best_method = method_name
    
    if best_method:
        report_lines.append(f"**最佳方法**: {best_method}")
        report_lines.append("")
        report_lines.append("### 性能分析:")
        
        for method_name, data in comparison.items():
            if "error" not in data:
                report_lines.append(f"- **{method_name}**:")
                report_lines.append(f"  - 成功率: {data['success_rate']:.1%}")
                report_lines.append(f"  - 最终奖励: {data['final_avg_reward']:.2f}")
                report_lines.append(f"  - 收敛速度: {data['convergence_episodes']} episodes")
                report_lines.append("")
    
    # 保存报告
    report_file = os.path.join(output_dir, "comparison_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"📄 对比报告已保存: {report_file}")

if __name__ == "__main__":
    run_comparison_experiment()
