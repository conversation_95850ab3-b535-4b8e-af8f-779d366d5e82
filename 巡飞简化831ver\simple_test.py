"""
简单测试，逐步定位问题
"""

import numpy as np
import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """测试环境创建"""
    try:
        print("🔍 测试环境创建...")
        from loitering_munition_environment import LoiteringMunitionEnvironment
        from environment_config import ENVIRONMENT_CONFIGS

        env_config = ENVIRONMENT_CONFIGS['stage1_simple']
        env = LoiteringMunitionEnvironment(
            bounds=[2000, 2000, 2000],
            environment_config=env_config,
            reward_type='simplified'
        )
        print("✅ 环境创建成功")
        
        # 测试重置
        state = env.reset()
        print(f"✅ 环境重置成功，状态形状: {state.shape}")
        print(f"   状态值: {state}")
        
        # 测试场景保存
        scenario_data = env.save_scenario()
        print(f"✅ 场景保存成功，包含键: {list(scenario_data.keys())}")
        
        return env, state, scenario_data
        
    except Exception as e:
        print(f"❌ 环境测试失败: {e}")
        traceback.print_exc()
        return None, None, None

def test_resband():
    """测试ResBand"""
    try:
        print("\n🔍 测试ResBand...")
        from resolution_bandit import ResolutionBandit, create_paper_configs
        
        resband = ResolutionBandit(
            configs=create_paper_configs(),
            exploration_coefficient=2.0,
            stage_length=20,
            reward_weights=(0.7, 0.2, 0.1),
            output_dir="test_results"
        )
        print("✅ ResBand创建成功")
        
        return resband
        
    except Exception as e:
        print(f"❌ ResBand测试失败: {e}")
        traceback.print_exc()
        return None

def test_resband_selection(resband, scenario_data, state):
    """测试ResBand分辨率选择"""
    try:
        print("\n🔍 测试ResBand分辨率选择...")
        
        obstacles = scenario_data.get('obstacles', [])
        goal = scenario_data.get('goal', None)
        bounds = scenario_data.get('bounds', None)
        
        print(f"   障碍物数量: {len(obstacles)}")
        print(f"   状态: {state}")
        print(f"   目标: {goal}")
        print(f"   边界: {bounds}")
        
        # 测试分辨率选择
        resolution_config = resband.select_resolution(
            episode=1,
            obstacles=obstacles,
            current_state=state,
            goal=goal,
            bounds=bounds
        )
        
        print(f"✅ 分辨率选择成功: {resolution_config.name}")
        return resolution_config
        
    except Exception as e:
        print(f"❌ ResBand选择测试失败: {e}")
        traceback.print_exc()
        return None

def test_dwa(resolution_config, env, obstacles, goal):
    """测试DWA"""
    try:
        print("\n🔍 测试DWA...")
        from loitering_munition_dwa import LoiteringMunitionDWA

        dwa = LoiteringMunitionDWA(resolution_config=resolution_config)
        print("✅ DWA创建成功")

        # 获取6维状态（DWA需要的格式）
        dwa_state = env.state  # 这是6维的 [x, y, z, V, gamma, psi]
        print(f"   DWA状态: {dwa_state}")

        # 测试安全控制生成
        safe_controls = dwa.generate_safe_control_set(
            dwa_state, obstacles, goal, max_actions=5
        )
        
        print(f"✅ 安全控制生成成功，数量: {len(safe_controls)}")
        return dwa, safe_controls
        
    except Exception as e:
        print(f"❌ DWA测试失败: {e}")
        traceback.print_exc()
        return None, None

def main():
    """主测试函数"""
    print("🚀 开始逐步测试...")
    
    # 测试环境
    env, state, scenario_data = test_environment()
    if env is None:
        return
    
    # 测试ResBand
    resband = test_resband()
    if resband is None:
        return
    
    # 测试ResBand选择
    resolution_config = test_resband_selection(resband, scenario_data, state)
    if resolution_config is None:
        return
    
    # 测试DWA
    obstacles = scenario_data.get('obstacles', [])
    goal = scenario_data.get('goal', None)
    dwa, safe_controls = test_dwa(resolution_config, env, obstacles, goal)
    if dwa is None:
        return
    
    print("\n🎉 所有测试通过！")

if __name__ == "__main__":
    main()
