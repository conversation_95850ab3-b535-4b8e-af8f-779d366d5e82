"""
测试ResBand分辨率切换逻辑
验证"先粗后细再到最优"的策略是否正确工作
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_resband_switching():
    """测试ResBand分辨率切换逻辑"""
    print("🎰 测试ResBand分辨率切换逻辑")
    print("=" * 50)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    print(f"\n📊 初始状态:")
    print(f"   当前臂: {resband.current_arm} ({resband.configs[resband.current_arm].name})")
    print(f"   选择次数: {resband.N}")
    print(f"   平均回报: {resband.Q}")
    print(f"   训练阶段: {resband.training_stage}")
    
    # 模拟20个episodes的分辨率选择
    print(f"\n🔄 模拟分辨率选择过程:")
    print("-" * 50)
    
    for episode in range(20):
        # 选择分辨率
        selected_config = resband.select_resolution(episode)
        
        print(f"Episode {episode+1:2d}: 选择 {selected_config.name} (臂{resband.current_arm})")
        
        # 模拟训练进展：从低性能到高性能的学习过程
        if episode >= 0:  # 从第1个episode开始有性能数据
            # 模拟训练进展：早期性能差，后期性能好
            progress_factor = min(episode / 15.0, 1.0)  # 15个episode后达到最佳性能

            # 模拟不同分辨率的性能差异
            if resband.current_arm == 0:  # 粗分辨率
                base_reward = 15000 + progress_factor * 10000  # 15k -> 25k
                episode_reward = np.random.normal(base_reward, 2000)
                critic_loss = np.random.normal(0.5 - progress_factor * 0.2, 0.1)
                violations = np.random.poisson(0.3 - progress_factor * 0.2)
            elif resband.current_arm == 1:  # 中粗分辨率
                base_reward = 18000 + progress_factor * 12000  # 18k -> 30k
                episode_reward = np.random.normal(base_reward, 1500)
                critic_loss = np.random.normal(0.4 - progress_factor * 0.2, 0.1)
                violations = np.random.poisson(0.2 - progress_factor * 0.15)
            else:  # 中等分辨率
                base_reward = 20000 + progress_factor * 15000  # 20k -> 35k
                episode_reward = np.random.normal(base_reward, 1000)
                critic_loss = np.random.normal(0.3 - progress_factor * 0.15, 0.1)
                violations = np.random.poisson(0.1 - progress_factor * 0.08)

            # 确保合理范围
            episode_reward = max(5000, episode_reward)
            violations = max(0, int(violations))
            success = episode_reward > 20000 and violations == 0

            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)

            print(f"         性能: 奖励={episode_reward:.1f}, 损失={critic_loss:.3f}, 违反={violations}, 成功={success}")
            print(f"         训练阶段: {resband.training_stage}")
        
        print(f"         状态: N={resband.N}, Q={resband.Q.round(1)}")
        
        # 每5个episode显示详细状态
        if (episode + 1) % 5 == 0:
            print(f"   📈 第{episode+1}个episode后状态:")
            print(f"      选择次数: {resband.N}")
            print(f"      平均回报: {resband.Q.round(1)}")
            print(f"      训练阶段: {resband.training_stage}")
            print()
    
    print("\n📊 最终结果:")
    print(f"   最终选择的臂: {resband.current_arm} ({resband.configs[resband.current_arm].name})")
    print(f"   各臂选择次数: {resband.N}")
    print(f"   各臂平均回报: {resband.Q.round(1)}")
    print(f"   训练阶段: {resband.training_stage}")

    # 分析结果
    print("\n🔍 结果分析:")
    if resband.training_stage == "exploration":
        print("   🔍 仍在探索阶段，建立基本策略")
    elif resband.training_stage == "refinement":
        print("   🔧 在精化阶段，平衡精度和效率")
    else:
        print("   🎯 在优化阶段，追求最佳性能")
        best_arm = np.argmax(resband.Q)
        print(f"   🏆 性能最佳的分辨率: 臂{best_arm} ({resband.configs[best_arm].name})")
        print(f"   📈 最佳平均回报: {resband.Q[best_arm]:.1f}")
    
    # 验证是否实现了"先粗后细"
    print("\n🎯 验证'先粗后细'策略:")
    selection_history = resband.arm_selection_history
    if len(selection_history) >= 3:
        first_selections = [h['arm'] for h in selection_history[:3]]
        print(f"   前3次选择: {first_selections}")
        if first_selections == [0, 1, 2]:
            print("   ✅ 成功实现'先粗后细'策略")
        else:
            print("   ❌ 未按预期顺序选择")
    
    return resband

if __name__ == "__main__":
    test_resband_switching()
