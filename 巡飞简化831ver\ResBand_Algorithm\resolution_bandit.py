"""
Resolution Bandit (ResBand) 算法实现 - 改进版本
基于多臂老虎机的自适应分辨率调度算法，与强化学习训练深度融合
"""

import numpy as np
import json
import os
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class ResolutionConfig:
    """分辨率配置类"""
    
    def __init__(self, name: str, a_T_resolution: float, a_N_resolution: float, 
                 mu_resolution: float, description: str = ""):
        self.name = name
        self.a_T_resolution = a_T_resolution
        self.a_N_resolution = a_N_resolution
        self.mu_resolution = mu_resolution
        self.description = description
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "a_T_resolution": self.a_T_resolution,
            "a_N_resolution": self.a_N_resolution,
            "mu_resolution": self.mu_resolution,
            "description": self.description
        }
    
    def __str__(self) -> str:
        return f"{self.name}: a_T={self.a_T_resolution}, a_N={self.a_N_resolution}, μ={self.mu_resolution}"

class ResolutionBandit:
    """
    Resolution Bandit (ResBand) 算法 - 改进版本
    
    基于多臂老虎机的自适应分辨率调度算法，与强化学习训练深度融合
    新增功能：
    1. 动态调整探索策略
    2. 基于训练进度的自适应机制
    3. 与强化学习损失函数结合
    4. 实时性能监控和调整
    """
    
    def __init__(self,
                 configs: List[ResolutionConfig] = None,
                 exploration_coefficient: float = 2.0,
                 stage_length: int = 20,
                 reward_weights: Tuple[float, float, float] = (0.7, 0.2, 0.1),
                 output_dir: str = "results",
                 use_fast_configs: bool = False,
                 adaptive_exploration: bool = True,
                 performance_threshold: float = 0.6):
        """
        初始化ResBand算法

        Args:
            configs: 分辨率配置列表（如果为None，将根据use_fast_configs自动选择）
            exploration_coefficient: UCB探索系数
            stage_length: 每个阶段的episode数量
            reward_weights: 回报函数权重 (α, β, γ)
            output_dir: 输出目录
            use_fast_configs: 是否使用快速配置（计算量更小）
            adaptive_exploration: 是否启用自适应探索
            performance_threshold: 性能阈值，用于触发自适应调整
        """
        # 自动选择配置
        if configs is None:
            if use_fast_configs:
                self.configs = create_fast_configs()
            else:
                self.configs = create_paper_configs()
        else:
            self.configs = configs
        self.K = len(self.configs)  # 臂的数量
        self.c = exploration_coefficient
        self.L = stage_length
        self.alpha, self.beta, self.gamma = reward_weights
        
        # 新增：自适应参数
        self.adaptive_exploration = adaptive_exploration
        self.performance_threshold = performance_threshold
        self.base_exploration_coefficient = exploration_coefficient
        
        # 老虎机状态
        self.Q = np.zeros(self.K)  # 每个臂的平均回报
        self.N = np.zeros(self.K, dtype=int)  # 每个臂被选择的次数
        self.M = 0  # 总训练阶段数
        self.current_arm = np.random.randint(0, self.K)  # 当前选择的臂
        
        # 新增：训练进度跟踪
        self.training_progress = {
            'total_episodes': 0,
            'success_rate_history': [],
            'avg_reward_history': [],
            'critic_loss_history': [],
            'violation_rate_history': []
        }
        
        # 新增：自适应调整历史
        self.adaptation_history = []
        
        # 历史记录
        self.arm_selection_history = []  # 臂选择历史
        self.reward_history = []  # 回报历史
        self.stage_performance = {}  # 每个阶段的性能记录
        
        # 输出目录
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🎰 ResBand算法初始化完成（改进版本）")
        print(f"   臂数量: {self.K}")
        print(f"   探索系数: {self.c}")
        print(f"   阶段长度: {self.L} episodes")
        print(f"   回报权重: α={self.alpha}, β={self.beta}, γ={self.gamma}")
        print(f"   自适应探索: {self.adaptive_exploration}")
        print(f"   性能阈值: {self.performance_threshold}")
        print(f"   分辨率配置:")
        for i, config in enumerate(self.configs):
            print(f"     Arm {i}: {config}")
    
    def select_resolution(self, episode: int, training_metrics: Dict = None) -> ResolutionConfig:
        """
        选择当前episode的分辨率配置 - 改进版本
        
        Args:
            episode: 当前episode编号
            training_metrics: 训练指标字典，包含成功率、平均奖励等
            
        Returns:
            选择的分辨率配置
        """
        # 更新训练进度
        self.training_progress['total_episodes'] = episode + 1
        
        # 新增：基于训练指标的自适应调整
        if training_metrics is not None:
            self._update_training_progress(training_metrics)
            self._adaptive_adjustment(episode)
        
        # 检查是否需要更新臂选择
        if episode > 0 and episode % self.L == 0:
            self._update_arm_selection()
        
        # 记录选择历史
        self.arm_selection_history.append({
            'episode': episode,
            'arm': self.current_arm,
            'config': self.configs[self.current_arm].to_dict(),
            'exploration_coefficient': self.c,
            'training_metrics': training_metrics
        })
        
        return self.configs[self.current_arm]
    
    def _update_training_progress(self, training_metrics: Dict):
        """更新训练进度指标"""
        if 'success_rate' in training_metrics:
            self.training_progress['success_rate_history'].append(training_metrics['success_rate'])
        if 'avg_reward' in training_metrics:
            self.training_progress['avg_reward_history'].append(training_metrics['avg_reward'])
        if 'critic_loss' in training_metrics:
            self.training_progress['critic_loss_history'].append(training_metrics['critic_loss'])
        if 'violation_rate' in training_metrics:
            self.training_progress['violation_rate_history'].append(training_metrics['violation_rate'])
    
    def _adaptive_adjustment(self, episode: int):
        """自适应调整策略"""
        if not self.adaptive_exploration:
            return
        
        # 计算最近的性能指标
        recent_success_rate = self._get_recent_success_rate()
        recent_avg_reward = self._get_recent_avg_reward()
        
        # 根据性能调整探索策略
        if recent_success_rate is not None and recent_avg_reward is not None:
            # 如果性能良好，减少探索，增加利用
            if recent_success_rate > self.performance_threshold and recent_avg_reward > 0:
                self.c = max(0.5, self.c * 0.95)  # 减少探索
                adaptation_type = "reduce_exploration"
            # 如果性能较差，增加探索
            elif recent_success_rate < self.performance_threshold * 0.5 or recent_avg_reward < -10:
                self.c = min(5.0, self.c * 1.1)  # 增加探索
                adaptation_type = "increase_exploration"
            else:
                adaptation_type = "maintain"
            
            # 记录自适应调整
            self.adaptation_history.append({
                'episode': episode,
                'old_exploration_coefficient': self.c,
                'adaptation_type': adaptation_type,
                'recent_success_rate': recent_success_rate,
                'recent_avg_reward': recent_avg_reward
            })
    
    def _get_recent_success_rate(self, window: int = 10) -> Optional[float]:
        """获取最近的成功率"""
        if len(self.training_progress['success_rate_history']) >= window:
            return np.mean(self.training_progress['success_rate_history'][-window:])
        return None
    
    def _get_recent_avg_reward(self, window: int = 10) -> Optional[float]:
        """获取最近的平均奖励"""
        if len(self.training_progress['avg_reward_history']) >= window:
            return np.mean(self.training_progress['avg_reward_history'][-window:])
        return None
    
    def update_performance(self, episode: int, episode_reward: float, 
                          critic_loss: float, violation_count: int,
                          success: bool = False, training_metrics: Dict = None):
        """
        更新性能指标 - 改进版本
        
        Args:
            episode: episode编号
            episode_reward: episode奖励
            critic_loss: Critic网络损失
            violation_count: 约束违反次数
            success: 是否成功
            training_metrics: 额外的训练指标
        """
        stage = episode // self.L
        
        if stage not in self.stage_performance:
            self.stage_performance[stage] = {
                'rewards': [],
                'critic_losses': [],
                'violations': [],
                'successes': [],
                'arm': self.current_arm
            }
        
        self.stage_performance[stage]['rewards'].append(episode_reward)
        self.stage_performance[stage]['critic_losses'].append(critic_loss)
        self.stage_performance[stage]['violations'].append(violation_count)
        self.stage_performance[stage]['successes'].append(success)
        
        # 新增：实时性能监控
        if training_metrics is not None:
            self._update_training_progress(training_metrics)
    
    def _update_arm_selection(self):
        """更新臂选择（改进的UCB策略）"""
        self.M += 1
        m = self.M
        
        # 计算当前臂的回报
        if m > 1 and (m-1) in self.stage_performance and len(self.stage_performance[m-1]['rewards']) > 0:
            r_bandit = self._compute_bandit_reward(m-1)
            
            # 更新平均回报
            self.Q[self.current_arm] = (
                (self.Q[self.current_arm] * self.N[self.current_arm] + r_bandit) / 
                (self.N[self.current_arm] + 1)
            )
            self.N[self.current_arm] += 1
            
            # 记录回报历史
            self.reward_history.append({
                'stage': m-1,
                'arm': self.current_arm,
                'reward': r_bandit,
                'avg_reward': self.Q[self.current_arm],
                'exploration_coefficient': self.c
            })
            
            print(f"🎯 更新臂 {self.current_arm} 的回报: {r_bandit:.3f}, 新平均回报: {self.Q[self.current_arm]:.3f}")
        else:
            print(f"⚠️  阶段 {m-1} 没有足够的数据来计算回报")
        
        # 计算UCB值并选择新臂
        ucb_values = np.zeros(self.K)
        for i in range(self.K):
            if self.N[i] == 0:
                ucb_values[i] = np.inf  # 强制探索未尝试的臂
            else:
                # 改进的UCB公式，考虑训练进度
                progress_factor = min(1.0, self.training_progress['total_episodes'] / 1000)
                adjusted_c = self.c * (1 + progress_factor * 0.5)  # 随训练进度调整探索
                ucb_values[i] = self.Q[i] + adjusted_c * np.sqrt(np.log(m) / self.N[i])
        
        self.current_arm = np.argmax(ucb_values)
        
        print(f"🎰 Stage {m}: 选择臂 {self.current_arm} ({self.configs[self.current_arm].name})")
        print(f"   UCB值: {ucb_values}")
        print(f"   平均回报: {self.Q}")
        print(f"   选择次数: {self.N}")
        print(f"   当前探索系数: {self.c:.3f}")
    
    def _compute_bandit_reward(self, stage: int) -> float:
        """
        计算老虎机回报函数 - 改进版本
        
        Args:
            stage: 阶段编号
            
        Returns:
            老虎机回报值
        """
        if stage not in self.stage_performance:
            return 0.0
        
        stage_data = self.stage_performance[stage]
        
        # 计算阶段平均指标
        avg_reward = np.mean(stage_data['rewards'])
        avg_critic_loss = np.mean(stage_data['critic_losses'])
        avg_violations = np.mean(stage_data['violations'])
        success_rate = np.mean(stage_data['successes']) if stage_data['successes'] else 0.0
        
        # 计算与前一阶段的差异
        if stage > 0 and (stage-1) in self.stage_performance:
            prev_data = self.stage_performance[stage-1]
            prev_avg_reward = np.mean(prev_data['rewards'])
            prev_avg_critic_loss = np.mean(prev_data['critic_losses'])
            prev_avg_violations = np.mean(prev_data['violations'])
            prev_success_rate = np.mean(prev_data['successes']) if prev_data['successes'] else 0.0
            
            delta_reward = avg_reward - prev_avg_reward
            delta_critic_loss = -(avg_critic_loss - prev_avg_critic_loss)  # 负号使损失下降变为正回报
            delta_violations = -(avg_violations - prev_avg_violations)  # 负号使违反次数减少变为正回报
            delta_success_rate = success_rate - prev_success_rate
        else:
            # 第一阶段，使用绝对值
            delta_reward = avg_reward
            delta_critic_loss = -avg_critic_loss
            delta_violations = -avg_violations
            delta_success_rate = success_rate
        
        # 改进的加权回报函数，加入成功率
        r_bandit = (self.alpha * delta_reward + 
                   self.beta * delta_critic_loss + 
                   self.gamma * delta_violations +
                   0.3 * delta_success_rate)  # 新增成功率权重（固定权重）
        
        return r_bandit
    
    def get_optimal_config(self) -> ResolutionConfig:
        """获取最优分辨率配置"""
        if np.sum(self.N) == 0:
            return self.configs[0]  # 如果还没有选择过任何臂，返回第一个
        
        best_arm = np.argmax(self.Q)
        return self.configs[best_arm]
    
    def get_training_summary(self) -> Dict:
        """获取训练摘要"""
        return {
            'total_episodes': self.training_progress['total_episodes'],
            'current_exploration_coefficient': self.c,
            'adaptation_count': len(self.adaptation_history),
            'best_arm': np.argmax(self.Q) if np.sum(self.N) > 0 else 0,
            'arm_selection_distribution': self.N.tolist(),
            'average_rewards': self.Q.tolist()
        }
    
    def save_results(self, filename: str = None):
        """保存算法结果 - 改进版本"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"resband_results_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        results = {
            "algorithm_config": {
                "K": self.K,
                "exploration_coefficient": self.c,
                "base_exploration_coefficient": self.base_exploration_coefficient,
                "stage_length": self.L,
                "reward_weights": [self.alpha, self.beta, self.gamma],
                "adaptive_exploration": self.adaptive_exploration,
                "performance_threshold": self.performance_threshold
            },
            "configs": [config.to_dict() for config in self.configs],
            "final_state": {
                "Q": self.Q.tolist(),
                "N": self.N.tolist(),
                "M": self.M,
                "current_arm": self.current_arm
            },
            "training_progress": self.training_progress,
            "adaptation_history": self.adaptation_history,
            "arm_selection_history": self.arm_selection_history,
            "reward_history": self.reward_history,
            "stage_performance": self.stage_performance,
            "optimal_config": self.get_optimal_config().to_dict(),
            "training_summary": self.get_training_summary()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"📁 ResBand结果已保存到: {filepath}")
        return filepath
    
    def plot_results(self):
        """绘制算法结果图表 - 改进版本"""
        try:
            import matplotlib.pyplot as plt
            
            # 创建图表
            fig, axes = plt.subplots(3, 2, figsize=(15, 15))
            fig.suptitle('ResBand算法性能分析（改进版本）', fontsize=16)
            
            # 1. 臂选择历史
            episodes = [h['episode'] for h in self.arm_selection_history]
            arms = [h['arm'] for h in self.arm_selection_history]
            
            axes[0, 0].plot(episodes, arms, 'o-', alpha=0.7)
            axes[0, 0].set_title('臂选择历史')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('臂编号')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 探索系数变化
            if self.adaptation_history:
                adaptation_episodes = [h['episode'] for h in self.adaptation_history]
                exploration_coeffs = [h['old_exploration_coefficient'] for h in self.adaptation_history]
                axes[0, 1].plot(adaptation_episodes, exploration_coeffs, 's-', alpha=0.7)
                axes[0, 1].set_title('探索系数变化')
                axes[0, 1].set_xlabel('Episode')
                axes[0, 1].set_ylabel('探索系数')
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 平均回报
            if self.reward_history:
                stages = [h['stage'] for h in self.reward_history]
                rewards = [h['reward'] for h in self.reward_history]
                avg_rewards = [h['avg_reward'] for h in self.reward_history]
                
                axes[1, 0].plot(stages, rewards, 'o-', label='阶段回报', alpha=0.7)
                axes[1, 0].plot(stages, avg_rewards, 's-', label='平均回报', alpha=0.7)
                axes[1, 0].set_title('回报变化')
                axes[1, 0].set_xlabel('阶段')
                axes[1, 0].set_ylabel('回报')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 训练进度指标
            if self.training_progress['success_rate_history']:
                episodes = range(len(self.training_progress['success_rate_history']))
                success_rates = self.training_progress['success_rate_history']
                axes[1, 1].plot(episodes, success_rates, 'o-', alpha=0.7)
                axes[1, 1].axhline(y=self.performance_threshold, color='r', linestyle='--', 
                                  label=f'阈值 ({self.performance_threshold})')
                axes[1, 1].set_title('成功率变化')
                axes[1, 1].set_xlabel('阶段')
                axes[1, 1].set_ylabel('成功率')
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)
            
            # 5. 选择次数分布
            arm_names = [config.name for config in self.configs]
            axes[2, 0].bar(range(self.K), self.N, alpha=0.7)
            axes[2, 0].set_title('臂选择次数分布')
            axes[2, 0].set_xlabel('臂编号')
            axes[2, 0].set_ylabel('选择次数')
            axes[2, 0].set_xticks(range(self.K))
            axes[2, 0].set_xticklabels(arm_names, rotation=45)
            axes[2, 0].grid(True, alpha=0.3)
            
            # 6. 最终平均回报
            axes[2, 1].bar(range(self.K), self.Q, alpha=0.7)
            axes[2, 1].set_title('最终平均回报')
            axes[2, 1].set_xlabel('臂编号')
            axes[2, 1].set_ylabel('平均回报')
            axes[2, 1].set_xticks(range(self.K))
            axes[2, 1].set_xticklabels(arm_names, rotation=45)
            axes[2, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = f"resband_analysis_{timestamp}.png"
            plot_path = os.path.join(self.output_dir, plot_filename)
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📊 ResBand分析图表已保存到: {plot_path}")
            return plot_path
            
        except ImportError:
            print("⚠️ matplotlib未安装，跳过图表生成")
            return None

def create_default_configs() -> List[ResolutionConfig]:
    """创建默认的分辨率配置"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.5, "计算效率优先，控制粗糙"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.25, "平衡精度和效率"),
        ResolutionConfig("细分辨率", 1.0, 4.0, 0.1, "高精度控制，计算代价大")
    ]
    return configs

def create_paper_configs() -> List[ResolutionConfig]:
    """创建论文中使用的分辨率配置"""
    configs = [
        ResolutionConfig("粗分辨率", 3.0, 12.0, 0.3, "计算效率优先"),
        ResolutionConfig("中等分辨率", 1.5, 6.0, 0.15, "论文默认配置"),
        ResolutionConfig("细分辨率", 0.8, 3.0, 0.08, "高精度控制")
    ]
    return configs

def create_fast_configs() -> List[ResolutionConfig]:
    """创建快速测试用的分辨率配置（计算量更小）"""
    configs = [
        ResolutionConfig("超粗分辨率", 5.0, 15.0, 0.5, "极快计算"),
        ResolutionConfig("粗分辨率", 3.0, 12.0, 0.3, "快速计算"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.2, "平衡配置")
    ]
    return configs
