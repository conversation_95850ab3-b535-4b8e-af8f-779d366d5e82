"""
调试训练错误
"""

import traceback
import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 开始调试训练过程...")
    
    from staged_training_framework import LoiteringMunitionStagedTrainer
    from environment_config import print_config_summary
    
    print("✅ 模块导入成功")
    
    # 创建训练器
    trainer = LoiteringMunitionStagedTrainer(
        start_stage=1,
        end_stage=1,
        use_resband=True,
        seed=42,
        visualization_interval=10
    )
    
    print("✅ 训练器创建成功")
    
    # 尝试运行第一个episode
    print("🎯 尝试运行训练...")
    
    trainer.run_staged_training()
    
    print("✅ 训练完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    print("\n📋 详细错误信息:")
    traceback.print_exc()
    
    # 尝试找到具体的错误位置
    import sys
    exc_type, exc_value, exc_traceback = sys.exc_info()
    
    print("\n🔍 错误追踪:")
    tb = exc_traceback
    while tb is not None:
        frame = tb.tb_frame
        filename = frame.f_code.co_filename
        line_number = tb.tb_lineno
        function_name = frame.f_code.co_name
        
        if 'basic_dwa_rl_framework' in filename:
            print(f"  📁 文件: {os.path.basename(filename)}")
            print(f"  📍 行号: {line_number}")
            print(f"  🔧 函数: {function_name}")
            
            # 尝试显示出错的代码行
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if line_number <= len(lines):
                        print(f"  💻 代码: {lines[line_number-1].strip()}")
            except:
                pass
            print()
        
        tb = tb.tb_next
