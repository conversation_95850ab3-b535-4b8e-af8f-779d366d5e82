{"feature_resolution_mapping": {"0.6_0.8_0.2_0.0_0.6": {"feature_vector": [0.6, 0.8, 0.2, 0.0, 0.6], "first_seen_episode": 15, "inferred_arm": 2, "arm_performance": {"0": {"count": 12, "total_reward": 372000.0, "avg_reward": 31000.0, "performance_history": [{"episode": 25, "reward": 28000, "critic_loss": 0.25, "violations": 0, "success": true}, {"episode": 32, "reward": 31500, "critic_loss": 0.2, "violations": 0, "success": true}, {"episode": 38, "reward": 33000, "critic_loss": 0.18, "violations": 0, "success": true}, {"episode": 44, "reward": 32500, "critic_loss": 0.19, "violations": 0, "success": true}]}, "1": {"count": 5, "total_reward": 135000.0, "avg_reward": 27000.0, "performance_history": [{"episode": 20, "reward": 26000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 28, "reward": 27500, "critic_loss": 0.3, "violations": 0, "success": true}]}, "2": {"count": 8, "total_reward": 192000.0, "avg_reward": 24000.0, "performance_history": [{"episode": 15, "reward": 22000, "critic_loss": 0.5, "violations": 0, "success": true}, {"episode": 18, "reward": 24000, "critic_loss": 0.45, "violations": 0, "success": true}, {"episode": 22, "reward": 25000, "critic_loss": 0.4, "violations": 0, "success": true}]}}, "learning_insight": "障碍物虽多但分布均匀，路径宽阔，粗分辨率足够且更高效"}, "0.1_0.2_0.9_0.0_0.1": {"feature_vector": [0.1, 0.2, 0.9, 0.0, 0.1], "first_seen_episode": 35, "inferred_arm": 0, "arm_performance": {"0": {"count": 8, "total_reward": 144000.0, "avg_reward": 18000.0, "performance_history": [{"episode": 35, "reward": 15000, "critic_loss": 0.6, "violations": 2, "success": false}, {"episode": 40, "reward": 17000, "critic_loss": 0.55, "violations": 1, "success": false}, {"episode": 45, "reward": 20000, "critic_loss": 0.5, "violations": 0, "success": true}]}, "1": {"count": 4, "total_reward": 96000.0, "avg_reward": 24000.0, "performance_history": [{"episode": 38, "reward": 22000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 42, "reward": 25000, "critic_loss": 0.35, "violations": 0, "success": true}]}, "2": {"count": 10, "total_reward": 290000.0, "avg_reward": 29000.0, "performance_history": [{"episode": 47, "reward": 26000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 50, "reward": 29000, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 53, "reward": 31000, "critic_loss": 0.25, "violations": 0, "success": true}, {"episode": 56, "reward": 30000, "critic_loss": 0.28, "violations": 0, "success": true}]}}, "learning_insight": "障碍物虽少但形成狭窄通道，需要精确控制才能通过"}, "0.3_0.4_0.6_0.8_0.3": {"feature_vector": [0.3, 0.4, 0.6, 0.8, 0.3], "first_seen_episode": 60, "inferred_arm": 2, "arm_performance": {"0": {"count": 3, "total_reward": 54000.0, "avg_reward": 18000.0, "performance_history": [{"episode": 65, "reward": 16000, "critic_loss": 0.6, "violations": 2, "success": false}, {"episode": 70, "reward": 19000, "critic_loss": 0.5, "violations": 1, "success": false}]}, "1": {"count": 15, "total_reward": 435000.0, "avg_reward": 29000.0, "performance_history": [{"episode": 68, "reward": 26000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 72, "reward": 29500, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 75, "reward": 31000, "critic_loss": 0.28, "violations": 0, "success": true}, {"episode": 78, "reward": 30500, "critic_loss": 0.29, "violations": 0, "success": true}]}, "2": {"count": 6, "total_reward": 156000.0, "avg_reward": 26000.0, "performance_history": [{"episode": 60, "reward": 24000, "critic_loss": 0.45, "violations": 0, "success": true}, {"episode": 63, "reward": 26000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 67, "reward": 27000, "critic_loss": 0.38, "violations": 0, "success": true}]}}, "learning_insight": "动态障碍物运动相对规律，中等分辨率在精度和响应速度间达到最佳平衡"}, "0.4_0.5_0.5_0.2_0.4": {"feature_vector": [0.4, 0.5, 0.5, 0.2, 0.4], "first_seen_episode": 80, "inferred_arm": 1, "arm_performance": {"0": {"count": 8, "total_reward": 240000.0, "avg_reward": 30000.0, "performance_history": [{"episode": 85, "reward": 26000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 95, "reward": 32000, "critic_loss": 0.25, "violations": 0, "success": true}, {"episode": 100, "reward": 33000, "critic_loss": 0.2, "violations": 0, "success": true}]}, "1": {"count": 12, "total_reward": 336000.0, "avg_reward": 28000.0, "performance_history": [{"episode": 80, "reward": 25000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 88, "reward": 30000, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 92, "reward": 29000, "critic_loss": 0.32, "violations": 0, "success": true}]}, "2": {"count": 5, "total_reward": 125000.0, "avg_reward": 25000.0, "performance_history": [{"episode": 82, "reward": 27000, "critic_loss": 0.35, "violations": 0, "success": true}]}}, "learning_insight": "随着智能体学习进展，对精细控制的需求降低，粗分辨率在后期更高效"}}}