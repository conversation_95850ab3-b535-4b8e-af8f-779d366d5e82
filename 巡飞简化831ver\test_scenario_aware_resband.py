"""
测试场景感知的ResBand分辨率自适应算法
验证场景阶段与分辨率选择的协调性
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_scenario_aware_resband():
    """测试场景感知的ResBand算法"""
    print("🎬 测试场景感知的ResBand分辨率自适应算法")
    print("=" * 60)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    print(f"\n📊 初始状态:")
    print(f"   当前场景阶段: {resband.current_scenario_stage}")
    print(f"   训练进展: {resband.training_progress}")
    print(f"   当前臂: {resband.current_arm} ({resband.configs[resband.current_arm].name})")
    
    # 模拟完整的分阶段训练过程
    scenarios = [
        ("simple_static", 15, "简单静态场景"),
        ("complex_static", 15, "复杂静态场景"),
        ("complex_dynamic", 15, "复杂动态场景")
    ]
    
    episode = 0
    
    for scenario_stage, num_episodes, description in scenarios:
        print(f"\n🎬 开始 {description} 训练")
        print("=" * 50)
        
        for i in range(num_episodes):
            episode += 1
            
            # 选择分辨率
            selected_config = resband.select_resolution(episode, scenario_stage)
            
            print(f"Episode {episode:2d}: {description} - 选择 {selected_config.name} (臂{resband.current_arm})")
            
            # 模拟不同场景和训练进展的性能
            episode_reward, critic_loss, violations, success = simulate_performance(
                scenario_stage, resband.training_progress, resband.current_arm, i
            )
            
            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"         性能: 奖励={episode_reward:.1f}, 损失={critic_loss:.3f}, 违反={violations}, 成功={success}")
            print(f"         训练进展: {resband.training_progress}")
            
            # 每5个episode显示详细状态
            if (i + 1) % 5 == 0:
                print(f"   📈 {description} 第{i+1}个episode后状态:")
                print(f"      选择次数: {resband.N}")
                print(f"      平均回报: {resband.Q.round(1)}")
                print(f"      训练进展: {resband.training_progress}")
                if len(resband.recent_success_rates) > 0:
                    print(f"      最近成功率: {np.mean(resband.recent_success_rates[-5:]):.2f}")
                print()
    
    print("\n📊 最终结果分析:")
    print(f"   最终选择的臂: {resband.current_arm} ({resband.configs[resband.current_arm].name})")
    print(f"   各臂选择次数: {resband.N}")
    print(f"   各臂平均回报: {resband.Q.round(1)}")
    print(f"   最终训练进展: {resband.training_progress}")
    
    # 分析场景感知效果
    print("\n🔍 场景感知效果分析:")
    analyze_scenario_awareness(resband)
    
    return resband

def simulate_performance(scenario_stage, training_progress, current_arm, episode_in_stage):
    """
    模拟不同场景阶段和训练进展下的性能
    
    Args:
        scenario_stage: 场景阶段
        training_progress: 训练进展
        current_arm: 当前选择的臂
        episode_in_stage: 在当前阶段的episode编号
    
    Returns:
        episode_reward, critic_loss, violations, success
    """
    # 训练进展因子
    progress_factors = {"early": 0.3, "middle": 0.6, "late": 1.0}
    progress_factor = progress_factors[training_progress]
    
    # 场景复杂度因子
    complexity_factors = {
        "simple_static": 1.0,
        "complex_static": 0.8,
        "complex_dynamic": 0.6
    }
    complexity_factor = complexity_factors[scenario_stage]
    
    # 分辨率适配因子
    resolution_factors = {
        "simple_static": [1.0, 0.95, 0.9],    # 简单场景：粗分辨率足够
        "complex_static": [0.8, 1.0, 1.1],    # 复杂静态：中等分辨率最佳
        "complex_dynamic": [0.6, 0.9, 1.2]    # 复杂动态：细分辨率最佳
    }
    resolution_factor = resolution_factors[scenario_stage][current_arm]
    
    # 计算基础性能
    base_reward = 15000 + progress_factor * 15000  # 15k -> 30k
    base_reward *= complexity_factor * resolution_factor
    
    # 添加随机性
    episode_reward = np.random.normal(base_reward, 2000)
    episode_reward = max(5000, episode_reward)
    
    # 计算其他指标
    critic_loss = np.random.normal(0.5 - progress_factor * 0.3, 0.1)
    critic_loss = max(0.1, critic_loss)
    
    # 违反次数：复杂场景更容易违反，低分辨率在复杂场景中更容易违反
    violation_prob = (1 - complexity_factor) * (1 - resolution_factor) * (1 - progress_factor)
    violation_prob = max(0.0, min(1.0, violation_prob))  # 确保在[0,1]范围内
    violations = np.random.poisson(violation_prob * 1.5)
    
    # 成功判断
    success = episode_reward > 20000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def analyze_scenario_awareness(resband):
    """分析场景感知效果"""
    
    # 分析选择历史
    selection_history = resband.arm_selection_history
    
    if len(selection_history) == 0:
        print("   ⚠️  没有选择历史数据")
        return
    
    # 按场景阶段分组分析
    scenario_selections = {}
    for record in selection_history:
        stage = record.get('scenario_stage', 'unknown')
        if stage not in scenario_selections:
            scenario_selections[stage] = []
        scenario_selections[stage].append(record['arm'])
    
    print("   📈 各场景阶段的分辨率选择模式:")
    for stage, selections in scenario_selections.items():
        if selections:
            most_common_arm = max(set(selections), key=selections.count)
            usage_rate = selections.count(most_common_arm) / len(selections)
            print(f"      {stage}: 主要使用臂{most_common_arm} ({resband.configs[most_common_arm].name}), 使用率{usage_rate:.1%}")
    
    # 验证场景适配性
    print("   🎯 场景适配性验证:")
    
    expected_preferences = {
        "simple_static": [0, 1],      # 应该偏好粗分辨率和中粗分辨率
        "complex_static": [1, 2],     # 应该偏好中粗分辨率和中等分辨率
        "complex_dynamic": [1, 2]     # 应该偏好中粗分辨率和中等分辨率
    }
    
    for stage, expected_arms in expected_preferences.items():
        if stage in scenario_selections:
            selections = scenario_selections[stage]
            preferred_selections = sum(1 for arm in selections if arm in expected_arms)
            adaptation_rate = preferred_selections / len(selections) if selections else 0
            
            if adaptation_rate >= 0.7:
                print(f"      ✅ {stage}: 良好适配 ({adaptation_rate:.1%})")
            elif adaptation_rate >= 0.5:
                print(f"      ⚠️  {stage}: 部分适配 ({adaptation_rate:.1%})")
            else:
                print(f"      ❌ {stage}: 适配不佳 ({adaptation_rate:.1%})")

if __name__ == "__main__":
    test_scenario_aware_resband()
