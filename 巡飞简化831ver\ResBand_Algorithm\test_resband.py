"""
ResBand算法测试脚本
用于验证ResBand算法的基本功能
"""

import numpy as np
import time
from resolution_bandit import ResolutionBandit, create_paper_configs
from resband_trainer import ResBandTrainer

def test_resolution_bandit():
    """测试ResBand核心算法"""
    print("🧪 测试ResBand核心算法...")
    
    # 创建ResBand实例
    configs = create_paper_configs()
    resband = ResolutionBandit(
        configs=configs,
        exploration_coefficient=2.0,
        stage_length=10,
        reward_weights=(0.7, 0.2, 0.1)
    )
    
    # 测试分辨率选择
    for episode in range(30):
        resolution = resband.select_resolution(episode)
        
        # 模拟性能指标
        episode_reward = np.random.normal(50, 10)
        critic_loss = np.random.normal(0.1, 0.05)
        violations = np.random.randint(0, 3)
        
        resband.update_performance(episode, episode_reward, critic_loss, violations)
        
        if episode % 10 == 0:
            print(f"Episode {episode}: 选择分辨率 {resolution.name}")
    
    # 测试结果保存和绘图
    resband.save_results("test_resband_results.json")
    resband.plot_results()
    
    print("✅ ResBand核心算法测试完成")

def test_resband_trainer():
    """测试ResBand训练器"""
    print("\n🧪 测试ResBand训练器...")
    
    # 创建训练器
    trainer = ResBandTrainer(
        use_resband=True,
        resband_config={
            'exploration_coefficient': 2.0,
            'stage_length': 10,
            'reward_weights': (0.7, 0.2, 0.1)
        },
        output_dir="test_results"
    )
    
    # 运行短时间训练
    print("开始短时间训练（10个episodes）...")
    results = trainer.train(
        num_episodes=10,
        save_interval=5,
        plot_interval=5
    )
    
    print(f"✅ 训练完成!")
    print(f"🎯 成功率: {results['success_rate']:.1%}")
    print(f"📊 平均奖励: {results['final_avg_reward']:.2f}")
    
    # 运行测试
    print("\n开始测试（5个episodes）...")
    test_results = trainer.run_test(num_test_episodes=5)
    
    print(f"✅ 测试完成!")
    print(f"🎯 测试成功率: {test_results['successes'].count(True) / len(test_results['successes']):.1%}")
    
    return trainer, results, test_results

def test_fixed_resolution():
    """测试固定分辨率模式"""
    print("\n🧪 测试固定分辨率模式...")
    
    # 创建不使用ResBand的训练器
    trainer = ResBandTrainer(
        use_resband=False,
        output_dir="test_results_fixed"
    )
    
    # 运行短时间训练
    print("开始短时间训练（10个episodes）...")
    results = trainer.train(
        num_episodes=10,
        save_interval=5,
        plot_interval=5
    )
    
    print(f"✅ 固定分辨率训练完成!")
    print(f"🎯 成功率: {results['success_rate']:.1%}")
    print(f"📊 平均奖励: {results['final_avg_reward']:.2f}")
    
    return trainer, results

def main():
    """主测试函数"""
    print("🎰 ResBand算法测试程序")
    print("=" * 50)
    
    try:
        # 测试ResBand核心算法
        test_resolution_bandit()
        
        # 测试ResBand训练器
        resband_trainer, resband_results, resband_test_results = test_resband_trainer()
        
        # 测试固定分辨率模式
        fixed_trainer, fixed_results = test_fixed_resolution()
        
        # 对比结果
        print("\n📊 测试结果对比:")
        print("-" * 30)
        print(f"ResBand模式:")
        print(f"  训练成功率: {resband_results['success_rate']:.1%}")
        print(f"  平均奖励: {resband_results['final_avg_reward']:.2f}")
        print(f"  测试成功率: {resband_test_results['successes'].count(True) / len(resband_test_results['successes']):.1%}")
        
        print(f"\n固定分辨率模式:")
        print(f"  训练成功率: {fixed_results['success_rate']:.1%}")
        print(f"  平均奖励: {fixed_results['final_avg_reward']:.2f}")
        
        print("\n🎉 所有测试完成!")
        print("📁 测试结果保存在 test_results/ 目录中")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
