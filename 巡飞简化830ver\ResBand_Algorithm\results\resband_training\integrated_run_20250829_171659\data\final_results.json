{"total_training_time": 0.0013887882232666016, "total_episodes": 225, "overall_success_rate": 0.0, "overall_avg_reward": NaN, "total_violations": 0, "training_history": {"stages": {"stage1": {"error": "__init__() got an unexpected keyword argument 'obstacle_count'"}, "stage2": {"error": "__init__() got an unexpected keyword argument 'obstacle_count'"}, "stage3": {"error": "__init__() got an unexpected keyword argument 'obstacle_count'"}}, "global_episodes": [], "global_rewards": [], "global_successes": [], "global_violations": [], "resolutions": [], "training_metrics": []}, "resband_summary": {"total_episodes": 0, "current_exploration_coefficient": 2.0, "adaptation_count": 0, "best_arm": 0, "arm_selection_distribution": [0, 0, 0], "average_rewards": [0.0, 0.0, 0.0]}}