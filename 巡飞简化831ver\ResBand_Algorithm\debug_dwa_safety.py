"""
调试DWA安全动作生成
检查DWA是否能在当前环境中找到安全动作
"""

import numpy as np
from simple_environment import SimpleLoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from resolution_bandit import ResolutionConfig

def test_dwa_safety():
    """测试DWA安全动作生成"""
    print("🔍 测试DWA安全动作生成...")
    
    # 创建环境和DWA
    env = SimpleLoiteringMunitionEnvironment()
    dwa = LoiteringMunitionDWA()
    
    # 使用快速配置
    fast_config = ResolutionConfig("快速测试", 5.0, 15.0, 0.5)
    dwa.update_resolution(fast_config)
    
    # 测试多个场景
    success_count = 0
    total_tests = 10
    
    for i in range(total_tests):
        print(f"\n--- 测试场景 {i+1} ---")
        
        # 重置环境
        state = env.reset()
        print(f"起点: {state[:3]}")
        print(f"终点: {env.get_goal()}")
        print(f"障碍物数量: {len(env.get_obstacles())}")
        
        # 测试前几步是否能找到安全动作
        step_success = 0
        for step in range(5):  # 测试前5步
            safe_controls = dwa.generate_safe_control_set(
                state,
                env.get_obstacles(),
                env.get_goal(),
                max_actions=10
            )
            
            if safe_controls:
                step_success += 1
                print(f"  步骤 {step+1}: 找到 {len(safe_controls)} 个安全动作")
                
                # 选择第一个安全动作并执行
                control = safe_controls[0]
                next_state, reward, done, info = env.step(control)
                
                print(f"    动作: {control}")
                print(f"    奖励: {reward:.2f}")
                print(f"    碰撞: {info.get('collision', False)}")
                print(f"    越界: {info.get('out_of_bounds', False)}")
                
                if done:
                    if info.get('success', False):
                        print(f"    ✅ 成功到达目标！")
                    else:
                        print(f"    ❌ 提前终止")
                    break
                
                state = next_state
            else:
                print(f"  步骤 {step+1}: ❌ 没有找到安全动作")
                break
        
        if step_success >= 3:  # 如果前5步中至少3步能找到安全动作
            success_count += 1
            print(f"✅ 场景 {i+1} 通过测试")
        else:
            print(f"❌ 场景 {i+1} 测试失败")
    
    print(f"\n📊 测试结果:")
    print(f"成功场景: {success_count}/{total_tests}")
    print(f"成功率: {success_count/total_tests:.1%}")
    
    if success_count < total_tests * 0.5:
        print("\n⚠️  DWA安全动作生成成功率较低，可能的原因：")
        print("1. 障碍物密度过高")
        print("2. 分辨率设置不合适")
        print("3. 安全距离设置过大")
        print("4. 环境边界过小")

def test_simple_scenario():
    """测试简单场景"""
    print("\n🎯 测试简单场景...")
    
    env = SimpleLoiteringMunitionEnvironment()
    dwa = LoiteringMunitionDWA()
    
    # 使用快速配置
    fast_config = ResolutionConfig("快速测试", 5.0, 15.0, 0.5)
    dwa.update_resolution(fast_config)
    
    # 手动设置简单场景
    env.start = np.array([100, 100, 100])
    env.goal = np.array([200, 200, 100])  # 更近的目标
    env.obstacles = [  # 只有一个障碍物
        {
            'center': np.array([150, 120, 100]),
            'radius': 20
        }
    ]
    env.dynamic_obstacles = []  # 没有动态障碍物
    
    # 重置状态
    env.state = np.array([100, 100, 100, 25.0, 0.0, 0.0])
    env.step_count = 0
    
    print(f"起点: {env.start}")
    print(f"终点: {env.goal}")
    print(f"距离: {np.linalg.norm(env.goal - env.start):.1f}米")
    print(f"障碍物: 1个，半径20米")
    
    # 测试能否找到安全动作
    state = env.state
    safe_controls = dwa.generate_safe_control_set(
        state,
        env.get_obstacles(),
        env.get_goal(),
        max_actions=10
    )
    
    if safe_controls:
        print(f"✅ 找到 {len(safe_controls)} 个安全动作")
        print(f"第一个安全动作: {safe_controls[0]}")
        
        # 执行动作
        next_state, reward, done, info = env.step(safe_controls[0])
        print(f"执行结果:")
        print(f"  奖励: {reward:.2f}")
        print(f"  新位置: {next_state[:3]}")
        print(f"  碰撞: {info.get('collision', False)}")
        print(f"  越界: {info.get('out_of_bounds', False)}")
    else:
        print("❌ 没有找到安全动作")

if __name__ == "__main__":
    test_dwa_safety()
    test_simple_scenario()
