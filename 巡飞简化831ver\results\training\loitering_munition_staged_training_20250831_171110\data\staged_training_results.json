{"start_time": "2025-08-31T17:11:10.485074", "config": {"start_stage": 1, "end_stage": 1, "seed": 42, "use_resband": true, "resband_config": {"exploration_coefficient": 2.0, "stage_length": 20, "reward_weights": [0.7, 0.2, 0.1]}, "td3_config": {"state_dim": 15, "action_dim": 3, "max_action": 1.0, "lr": 0.0003, "batch_size": 256, "gamma": 0.99, "tau": 0.005, "policy_noise": 0.2, "noise_clip": 0.5, "policy_freq": 2, "buffer_size": 1000000, "hidden_dim": 256}, "lm_config": {"bounds": [2000, 2000, 2000], "dt": 0.1, "max_steps": 2000, "V_min": 15.0, "V_max": 60.0, "V_cruise": 25.0, "a_T_max": 8.0, "a_N_max": 39.24, "gamma_max": 1.047, "d_safe": 5.0, "g": 9.81}, "dwa_config": {"predict_time": 3.0, "min_safe_distance": 5.0, "a_T_resolution": 1.5, "a_N_resolution": 6.0, "mu_resolution": 0.15, "alpha": 0.4, "beta": 0.2, "gamma": 0.3, "delta": 0.1}}, "stages": {"stage_1": {"error": "The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()"}}, "end_time": "2025-08-31T17:11:12.958742", "total_training_time": 2.473667621612549}