"""
测试真正的元学习：ResBand基于场景特征自主学习分辨率选择
验证"学习如何学习"的能力
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_true_meta_learning():
    """测试真正的元学习能力"""
    print("🧠 测试ResBand的真正元学习能力：基于场景特征自主学习")
    print("=" * 70)
    
    # 创建启用场景特征学习的ResBand
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    print(f"📊 元学习配置:")
    print(f"   场景特征学习: {'启用' if resband.use_scenario_feature_learning else '禁用'}")
    print(f"   特征维度: {len(resband.scenario_analyzer.feature_names)}")
    print(f"   特征名称: {resband.scenario_analyzer.feature_names}")
    
    # 创建多样化的测试场景
    test_scenarios = create_diverse_scenarios()
    
    print(f"\n🎬 创建了{len(test_scenarios)}个多样化测试场景")
    
    episode = 0
    learning_progress = {}
    
    # 第一轮：初始学习阶段
    print(f"\n🔍 第一轮：初始学习阶段（每个场景尝试5次）")
    print("-" * 60)
    
    for scenario_name, scenario_data in test_scenarios.items():
        print(f"\n📍 场景: {scenario_name}")
        
        scenario_selections = []
        scenario_rewards = []
        
        for trial in range(5):  # 每个场景尝试5次
            episode += 1
            
            # 基于场景特征选择分辨率
            selected_config = resband.select_resolution(
                episode=episode,
                obstacles=scenario_data['obstacles'],
                current_state=scenario_data['current_state'],
                goal=scenario_data['goal'],
                bounds=scenario_data['bounds']
            )
            
            scenario_selections.append(resband.current_arm)
            
            # 模拟该场景下不同分辨率的真实性能
            episode_reward, critic_loss, violations, success = simulate_scenario_performance(
                scenario_data, resband.current_arm, trial
            )
            scenario_rewards.append(episode_reward)
            
            # 更新性能（关键：让ResBand学习特征到分辨率的映射）
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"   Trial {trial+1}: 选择臂{resband.current_arm}, 奖励={episode_reward:.1f}")
        
        # 分析初始学习结果
        avg_reward = np.mean(scenario_rewards)
        most_selected = max(set(scenario_selections), key=scenario_selections.count)
        
        learning_progress[scenario_name] = {
            'initial_selections': scenario_selections.copy(),
            'initial_rewards': scenario_rewards.copy(),
            'initial_avg_reward': avg_reward,
            'initial_preferred_arm': most_selected
        }
        
        print(f"   初始学习结果: 平均奖励={avg_reward:.1f}, 偏好臂={most_selected}")
    
    # 第二轮：验证学习效果
    print(f"\n🎯 第二轮：验证学习效果（重复相同场景）")
    print("-" * 60)
    
    for scenario_name, scenario_data in test_scenarios.items():
        print(f"\n📍 场景: {scenario_name} (重复测试)")
        
        scenario_selections = []
        scenario_rewards = []
        
        for trial in range(5):  # 再次尝试相同场景
            episode += 1
            
            # 基于已学习的特征映射选择分辨率
            selected_config = resband.select_resolution(
                episode=episode,
                obstacles=scenario_data['obstacles'],
                current_state=scenario_data['current_state'],
                goal=scenario_data['goal'],
                bounds=scenario_data['bounds']
            )
            
            scenario_selections.append(resband.current_arm)
            
            # 模拟性能
            episode_reward, critic_loss, violations, success = simulate_scenario_performance(
                scenario_data, resband.current_arm, trial
            )
            scenario_rewards.append(episode_reward)
            
            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"   Trial {trial+1}: 选择臂{resband.current_arm}, 奖励={episode_reward:.1f}")
        
        # 分析学习改进
        avg_reward = np.mean(scenario_rewards)
        most_selected = max(set(scenario_selections), key=scenario_selections.count)
        
        learning_progress[scenario_name]['final_selections'] = scenario_selections
        learning_progress[scenario_name]['final_rewards'] = scenario_rewards
        learning_progress[scenario_name]['final_avg_reward'] = avg_reward
        learning_progress[scenario_name]['final_preferred_arm'] = most_selected
        
        # 计算改进
        improvement = avg_reward - learning_progress[scenario_name]['initial_avg_reward']
        print(f"   学习后结果: 平均奖励={avg_reward:.1f}, 偏好臂={most_selected}")
        print(f"   性能改进: {improvement:+.1f} ({improvement/learning_progress[scenario_name]['initial_avg_reward']*100:+.1f}%)")
    
    # 生成元学习分析报告
    generate_meta_learning_report(resband, learning_progress)

def create_diverse_scenarios():
    """创建多样化的测试场景"""
    scenarios = {}
    
    # 场景1：低密度静态场景
    scenarios['低密度静态'] = {
        'obstacles': [
            {'center': [600, 600, 100], 'radius': 30, 'motion_type': None},
            {'center': [700, 700, 100], 'radius': 25, 'motion_type': None}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200]
    }
    
    # 场景2：高密度静态场景
    scenarios['高密度静态'] = {
        'obstacles': [
            {'center': [550, 550, 100], 'radius': 40, 'motion_type': None},
            {'center': [600, 600, 100], 'radius': 35, 'motion_type': None},
            {'center': [650, 650, 100], 'radius': 30, 'motion_type': None},
            {'center': [700, 700, 100], 'radius': 45, 'motion_type': None},
            {'center': [750, 750, 100], 'radius': 25, 'motion_type': None}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200]
    }
    
    # 场景3：低密度动态场景
    scenarios['低密度动态'] = {
        'obstacles': [
            {'center': [600, 600, 100], 'radius': 40, 'motion_type': 'linear', 
             'motion_params': {'velocity': [10, 5, 0]}},
            {'center': [700, 700, 100], 'radius': 35, 'motion_type': 'circular',
             'motion_params': {'center_orbit': [700, 700, 100], 'radius_orbit': 50, 'angular_speed': 0.3, 'phase': 0}}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200]
    }
    
    # 场景4：高密度动态场景
    scenarios['高密度动态'] = {
        'obstacles': [
            {'center': [550, 550, 100], 'radius': 35, 'motion_type': 'linear',
             'motion_params': {'velocity': [15, -10, 0]}},
            {'center': [600, 600, 100], 'radius': 40, 'motion_type': 'circular',
             'motion_params': {'center_orbit': [600, 600, 100], 'radius_orbit': 60, 'angular_speed': 0.5, 'phase': 0}},
            {'center': [650, 650, 100], 'radius': 30, 'motion_type': 'oscillating',
             'motion_params': {'center_base': [650, 650, 100], 'amplitude': [20, 15, 0], 'frequency': 0.4, 'phase': 0}},
            {'center': [700, 700, 100], 'radius': 45, 'motion_type': 'linear',
             'motion_params': {'velocity': [-8, 12, 0]}}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200]
    }
    
    return scenarios

def simulate_scenario_performance(scenario_data, selected_arm, trial):
    """模拟不同场景下不同分辨率的性能"""
    obstacles = scenario_data['obstacles']
    
    # 计算场景复杂度
    obstacle_count = len(obstacles)
    dynamic_count = sum(1 for obs in obstacles if obs.get('motion_type') is not None)
    dynamic_ratio = dynamic_count / obstacle_count if obstacle_count > 0 else 0
    
    # 不同分辨率在不同场景下的性能模拟
    if dynamic_ratio == 0:  # 静态场景
        if obstacle_count <= 2:  # 低密度
            # 粗分辨率最优
            base_rewards = [28000, 26000, 24000]  # 粗、中、细
        else:  # 高密度
            # 中等分辨率最优
            base_rewards = [22000, 28000, 26000]  # 粗、中、细
    else:  # 动态场景
        if obstacle_count <= 2:  # 低密度动态
            # 中等分辨率最优
            base_rewards = [20000, 27000, 25000]  # 粗、中、细
        else:  # 高密度动态
            # 精细分辨率最优
            base_rewards = [15000, 22000, 30000]  # 粗、中、细
    
    # 添加随机性和学习进展
    base_reward = base_rewards[selected_arm]
    learning_bonus = trial * 500  # 模拟学习进展
    noise = np.random.normal(0, 1500)
    
    episode_reward = base_reward + learning_bonus + noise
    episode_reward = max(10000, episode_reward)
    
    critic_loss = np.random.normal(0.4, 0.1)
    violations = np.random.poisson(0.1)
    success = episode_reward > 20000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def generate_meta_learning_report(resband, learning_progress):
    """生成元学习分析报告"""
    print(f"\n🧠 ResBand元学习能力分析报告")
    print("=" * 70)
    
    print(f"📊 学习到的特征映射数量: {len(resband.feature_resolution_mapping)}")
    
    total_improvement = 0
    successful_learning = 0
    
    for scenario_name, progress in learning_progress.items():
        print(f"\n📍 {scenario_name}:")
        
        initial_avg = progress['initial_avg_reward']
        final_avg = progress['final_avg_reward']
        improvement = final_avg - initial_avg
        improvement_pct = improvement / initial_avg * 100
        
        print(f"   初始性能: {initial_avg:.1f}")
        print(f"   学习后性能: {final_avg:.1f}")
        print(f"   性能改进: {improvement:+.1f} ({improvement_pct:+.1f}%)")
        
        initial_arm = progress['initial_preferred_arm']
        final_arm = progress['final_preferred_arm']
        
        if initial_arm != final_arm:
            print(f"   分辨率选择变化: 臂{initial_arm} → 臂{final_arm}")
        else:
            print(f"   分辨率选择稳定: 臂{final_arm}")
        
        total_improvement += improvement_pct
        if improvement > 0:
            successful_learning += 1
    
    avg_improvement = total_improvement / len(learning_progress)
    learning_success_rate = successful_learning / len(learning_progress)
    
    print(f"\n🏆 元学习总体评估:")
    print(f"   平均性能改进: {avg_improvement:+.1f}%")
    print(f"   学习成功率: {learning_success_rate:.1%}")
    
    if avg_improvement > 5 and learning_success_rate > 0.7:
        print(f"   ✅ 元学习能力优秀：ResBand成功学会了'如何学习'")
        print(f"   ✅ 能够根据场景特征自主选择最优分辨率")
        print(f"   ✅ 不依赖预设的场景阶段标签")
    elif avg_improvement > 0 and learning_success_rate > 0.5:
        print(f"   ⚠️  元学习能力良好，仍有改进空间")
    else:
        print(f"   ❌ 元学习能力需要进一步优化")
    
    print(f"\n🎯 这就是真正的'学习如何学习'：")
    print(f"   1. 从场景特征中自动提取关键信息")
    print(f"   2. 学习特征到最优分辨率的映射关系")
    print(f"   3. 在新场景中应用学到的知识")
    print(f"   4. 持续优化和改进选择策略")

if __name__ == "__main__":
    test_true_meta_learning()
