"""
ResBand算法对比实验
比较固定分辨率、启发式调度和ResBand算法的性能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

from resband_trainer import ResBandTrainer
from resolution_bandit import ResolutionConfig, create_paper_configs

class FixedResolutionBaseline:
    """固定分辨率基线"""
    
    def __init__(self, resolution_config: ResolutionConfig):
        self.resolution_config = resolution_config
        self.name = f"Fixed-{resolution_config.name}"
    
    def select_resolution(self, episode):
        return self.resolution_config
    
    def update_performance(self, episode, reward, critic_loss, violations):
        pass  # 固定分辨率不需要更新

class HeuristicScheduler:
    """启发式调度器"""
    
    def __init__(self, configs):
        self.configs = configs
        self.name = "Heuristic-Scheduling"
        self.current_config_idx = 0
        self.episode_count = 0
    
    def select_resolution(self, episode):
        # 每100个episode切换一次分辨率
        if episode > 0 and episode % 100 == 0:
            self.current_config_idx = (self.current_config_idx + 1) % len(self.configs)
            print(f"🔄 启发式调度: 切换到 {self.configs[self.current_config_idx].name}")
        
        return self.configs[self.current_config_idx]
    
    def update_performance(self, episode, reward, critic_loss, violations):
        pass  # 启发式调度不需要更新

def run_comparison_experiment():
    """运行对比实验"""
    print("🔬 ResBand算法对比实验")
    print("=" * 60)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"results/resband_comparison_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义分辨率配置
    configs = create_paper_configs()
    
    # 定义实验方法
    methods = {
        "Fixed-Coarse": FixedResolutionBaseline(configs[0]),  # 粗分辨率
        "Fixed-Medium": FixedResolutionBaseline(configs[1]),  # 中等分辨率
        "Fixed-Fine": FixedResolutionBaseline(configs[2]),    # 细分辨率
        "Heuristic": HeuristicScheduler(configs),             # 启发式调度
        "ResBand": None  # ResBand算法（在训练器中创建）
    }
    
    # 实验配置
    experiment_config = {
        "num_episodes": 200,  # 每个方法运行200个episode
        "test_episodes": 50,  # 测试episodes
        "save_interval": 50,
        "plot_interval": 20
    }
    
    # 存储结果
    results = {
        "config": experiment_config,
        "methods": {},
        "comparison": {}
    }
    
    print(f"📊 实验配置:")
    print(f"   每个方法episodes: {experiment_config['num_episodes']}")
    print(f"   测试episodes: {experiment_config['test_episodes']}")
    print(f"   输出目录: {output_dir}")
    print()
    
    # 运行每个方法的实验
    for method_name, method in methods.items():
        print(f"🚀 运行 {method_name} 方法...")
        print("-" * 40)
        
        try:
            # 创建训练器
            if method_name == "ResBand":
                # ResBand方法
                trainer = ResBandTrainer(
                    use_resband=True,
                    resband_config={
                        'exploration_coefficient': 2.0,
                        'stage_length': 20,
                        'reward_weights': (0.7, 0.2, 0.1)
                    },
                    output_dir=os.path.join(output_dir, method_name)
                )
            else:
                # 其他方法（固定分辨率或启发式）
                trainer = ResBandTrainer(
                    use_resband=False,
                    output_dir=os.path.join(output_dir, method_name)
                )
            
            # 运行训练
            training_results = trainer.train(
                num_episodes=experiment_config["num_episodes"],
                save_interval=experiment_config["save_interval"],
                plot_interval=experiment_config["plot_interval"]
            )
            
            # 运行测试
            test_results = trainer.run_test(num_test_episodes=experiment_config["test_episodes"])
            
            # 保存结果
            method_results = {
                "method_name": method_name,
                "training_results": training_results,
                "test_results": test_results,
                "trainer_dir": trainer.run_dir
            }
            
            results["methods"][method_name] = method_results
            
            print(f"✅ {method_name} 完成")
            
        except Exception as e:
            print(f"❌ {method_name} 失败: {e}")
            results["methods"][method_name] = {"error": str(e)}
    
    # 分析对比结果
    print(f"\n📈 分析对比结果...")
    comparison_results = analyze_comparison_results(results)
    results["comparison"] = comparison_results
    
    # 保存结果
    results_file = os.path.join(output_dir, "comparison_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 生成对比图表
    generate_comparison_plots(results, output_dir)
    
    # 生成报告
    generate_comparison_report(results, output_dir)
    
    print(f"\n🎉 对比实验完成!")
    print(f"📁 结果保存在: {output_dir}")
    print(f"📄 详细结果: {results_file}")
    
    return results

def analyze_comparison_results(results):
    """分析对比结果"""
    comparison = {}
    
    for method_name, method_data in results["methods"].items():
        if "error" in method_data:
            comparison[method_name] = {"error": method_data["error"]}
            continue
        
        training_results = method_data["training_results"]
        test_results = method_data["test_results"]
        
        # 提取关键指标
        comparison[method_name] = {
            "training_success_rate": training_results["success_rate"],
            "training_time": training_results["training_time"],
            "final_avg_reward": training_results["final_avg_reward"],
            "total_violations": training_results["total_violations"],
            "test_success_rate": test_results["successes"].count(True) / len(test_results["successes"]),
            "test_avg_reward": np.mean(test_results["rewards"]),
            "convergence_episodes": estimate_convergence_episodes(test_results["rewards"])
        }
    
    return comparison

def estimate_convergence_episodes(rewards, window_size=20, threshold=0.1):
    """估算收敛所需的episode数"""
    if len(rewards) < window_size:
        return len(rewards)
    
    for i in range(window_size, len(rewards)):
        recent_rewards = rewards[i-window_size:i]
        avg_reward = np.mean(recent_rewards)
        
        # 检查是否稳定
        if np.std(recent_rewards) < threshold * abs(avg_reward):
            return i
    
    return len(rewards)

def generate_comparison_plots(results, output_dir):
    """生成对比图表"""
    try:
        comparison = results["comparison"]
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('ResBand算法对比实验结果', fontsize=16)
        
        # 提取方法名称
        method_names = []
        for method_name, data in comparison.items():
            if "error" not in data:
                method_names.append(method_name)
        
        if not method_names:
            print("⚠️ 没有成功的方法结果")
            return
        
        # 1. 训练成功率对比
        training_success_rates = [comparison[name]["training_success_rate"] for name in method_names]
        axes[0, 0].bar(method_names, training_success_rates, alpha=0.7)
        axes[0, 0].set_title('训练成功率对比')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 测试成功率对比
        test_success_rates = [comparison[name]["test_success_rate"] for name in method_names]
        axes[0, 1].bar(method_names, test_success_rates, alpha=0.7)
        axes[0, 1].set_title('测试成功率对比')
        axes[0, 1].set_ylabel('成功率')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 最终平均奖励对比
        final_rewards = [comparison[name]["final_avg_reward"] for name in method_names]
        axes[0, 2].bar(method_names, final_rewards, alpha=0.7)
        axes[0, 2].set_title('最终平均奖励对比')
        axes[0, 2].set_ylabel('平均奖励')
        axes[0, 2].tick_params(axis='x', rotation=45)
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 收敛速度对比
        convergence_episodes = [comparison[name]["convergence_episodes"] for name in method_names]
        axes[1, 0].bar(method_names, convergence_episodes, alpha=0.7)
        axes[1, 0].set_title('收敛速度对比')
        axes[1, 0].set_ylabel('收敛所需episodes')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 训练时间对比
        training_times = [comparison[name]["training_time"] for name in method_names]
        axes[1, 1].bar(method_names, training_times, alpha=0.7)
        axes[1, 1].set_title('训练时间对比')
        axes[1, 1].set_ylabel('训练时间(秒)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 约束违反次数对比
        violations = [comparison[name]["total_violations"] for name in method_names]
        axes[1, 2].bar(method_names, violations, alpha=0.7)
        axes[1, 2].set_title('约束违反次数对比')
        axes[1, 2].set_ylabel('违反次数')
        axes[1, 2].tick_params(axis='x', rotation=45)
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = os.path.join(output_dir, "comparison_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 对比图表已保存: {plot_file}")
        
    except Exception as e:
        print(f"⚠️ 生成图表失败: {e}")

def generate_comparison_report(results, output_dir):
    """生成对比报告"""
    comparison = results["comparison"]
    
    report_lines = []
    report_lines.append("# ResBand算法对比实验报告")
    report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # 实验配置
    report_lines.append("## 实验配置")
    config = results["config"]
    report_lines.append(f"- 每个方法训练episodes: {config['num_episodes']}")
    report_lines.append(f"- 每个方法测试episodes: {config['test_episodes']}")
    report_lines.append(f"- 保存间隔: {config['save_interval']}")
    report_lines.append(f"- 绘图间隔: {config['plot_interval']}")
    report_lines.append("")
    
    # 结果对比
    report_lines.append("## 结果对比")
    report_lines.append("")
    report_lines.append("| 方法 | 训练成功率 | 测试成功率 | 最终平均奖励 | 收敛episodes | 训练时间(s) | 违反次数 |")
    report_lines.append("|------|------------|------------|--------------|--------------|-------------|----------|")
    
    for method_name, data in comparison.items():
        if "error" in data:
            report_lines.append(f"| {method_name} | ❌ 失败 | ❌ 失败 | ❌ 失败 | ❌ 失败 | ❌ 失败 | ❌ 失败 |")
        else:
            training_success = f"{data['training_success_rate']:.1%}"
            test_success = f"{data['test_success_rate']:.1%}"
            final_reward = f"{data['final_avg_reward']:.2f}"
            convergence = f"{data['convergence_episodes']}"
            training_time = f"{data['training_time']:.1f}"
            violations = f"{data['total_violations']}"
            report_lines.append(f"| {method_name} | {training_success} | {test_success} | {final_reward} | {convergence} | {training_time} | {violations} |")
    
    report_lines.append("")
    
    # 结论
    report_lines.append("## 结论")
    report_lines.append("")
    
    # 找出最佳方法
    best_method = None
    best_score = -float('inf')
    
    for method_name, data in comparison.items():
        if "error" not in data:
            # 综合评分：测试成功率 * 0.4 + 奖励 * 0.3 + 收敛速度 * 0.2 + 训练时间 * 0.1
            score = (data['test_success_rate'] * 0.4 + 
                    data['final_avg_reward'] / 100 * 0.3 + 
                    (1 - data['convergence_episodes'] / 200) * 0.2 +
                    (1 - data['training_time'] / 1000) * 0.1)
            
            if score > best_score:
                best_score = score
                best_method = method_name
    
    if best_method:
        report_lines.append(f"**最佳方法**: {best_method}")
        report_lines.append("")
        report_lines.append("### 性能分析:")
        
        for method_name, data in comparison.items():
            if "error" not in data:
                report_lines.append(f"- **{method_name}**:")
                report_lines.append(f"  - 训练成功率: {data['training_success_rate']:.1%}")
                report_lines.append(f"  - 测试成功率: {data['test_success_rate']:.1%}")
                report_lines.append(f"  - 最终奖励: {data['final_avg_reward']:.2f}")
                report_lines.append(f"  - 收敛速度: {data['convergence_episodes']} episodes")
                report_lines.append(f"  - 训练时间: {data['training_time']:.1f}秒")
                report_lines.append(f"  - 违反次数: {data['total_violations']}")
                report_lines.append("")
    
    # 保存报告
    report_file = os.path.join(output_dir, "comparison_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"📄 对比报告已保存: {report_file}")

if __name__ == "__main__":
    run_comparison_experiment()
