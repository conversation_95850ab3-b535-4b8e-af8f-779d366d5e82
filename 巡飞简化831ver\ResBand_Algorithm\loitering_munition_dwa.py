"""
巡飞弹动态窗口算法控制器 - ResBand版本
支持动态分辨率配置的DWA控制器
"""

import numpy as np
import math

class LoiteringMunitionDWA:
    """巡飞弹动态窗口算法控制器 - ResBand版本"""

    def __init__(self, dt=0.1, resolution_config=None):
        self.dt = dt
        self.g = 9.81  # 重力加速度

        # 巡飞弹运动约束（与环境一致）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.V_cruise = 25.0 # 巡航速度（合理的初始速度）
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）

        # DWA参数 - 极度优化计算效率
        self.predict_time = 1.0  # 预测时间窗口（大幅减少）
        self.min_safe_distance = 5.0  # 最小安全距离

        # 控制输入离散化参数（支持动态分辨率）
        if resolution_config is not None:
            self.a_T_resolution = resolution_config.a_T_resolution
            self.a_N_resolution = resolution_config.a_N_resolution
            self.mu_resolution = resolution_config.mu_resolution
        else:
            # 默认分辨率（极度优化）
            self.a_T_resolution = 4.0    # 切向加速度分辨率（大幅减少采样点）
            self.a_N_resolution = 15.0   # 法向加速度分辨率（大幅减少采样点）
            self.mu_resolution = 0.5     # 倾斜角分辨率（大幅减少采样点）

        # 评价函数权重
        self.alpha = 0.4   # 目标方向权重
        self.beta = 0.2    # 速度权重（鼓励巡航速度）
        self.distance_weight = 0.3   # 距离权重（避免与gamma角度冲突）
        self.delta = 0.1   # 障碍物权重
        
    def generate_safe_control_set(self, current_state, obstacles, goal, max_actions=5, bounds=None):
        """
        生成安全控制输入集合 - 优化版本

        Args:
            current_state: [x, y, z, V, γ, ψ] - 当前状态
            obstacles: 障碍物列表
            goal: 目标位置
            max_actions: 最大动作数量（减少到10个）
            bounds: 环境边界 [x_max, y_max, z_max]

        Returns:
            安全的控制输入列表，按评价分数排序
        """
        x, y, z, V, gamma, psi = current_state

        # 计算动态窗口（基于当前状态的可达加速度范围）
        dw = self._calc_dynamic_window(current_state)

        safe_controls_with_scores = []

        # 遍历控制输入空间（使用优化的分辨率）
        # 修复：确保不超出动态窗口范围
        a_T_range = np.arange(dw['a_T_min'], dw['a_T_max'] + self.a_T_resolution/2,
                             self.a_T_resolution)
        a_N_range = np.arange(dw['a_N_min'], dw['a_N_max'] + self.a_N_resolution/2,
                             self.a_N_resolution)
        mu_range = np.arange(dw['mu_min'], dw['mu_max'] + self.mu_resolution/2,
                            self.mu_resolution)

        # 确保不超出约束范围
        a_T_range = a_T_range[a_T_range <= dw['a_T_max']]
        a_N_range = a_N_range[a_N_range <= dw['a_N_max']]
        mu_range = mu_range[mu_range <= dw['mu_max']]

        for a_T in a_T_range:
            for a_N in a_N_range:
                for mu in mu_range:
                    control = np.array([a_T, a_N, mu])
                    
                    # 检查控制输入是否安全
                    if self._is_safe_control(control, current_state, obstacles, bounds):
                        # 计算评价分数
                        score = self.evaluate_control(control, current_state, goal, obstacles)
                        safe_controls_with_scores.append((control, score))

        # 按分数排序（降序）
        safe_controls_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前max_actions个控制输入
        safe_controls = [control for control, score in safe_controls_with_scores[:max_actions]]
        
        return safe_controls
    
    def update_resolution(self, resolution_config):
        """
        更新分辨率配置
        
        Args:
            resolution_config: 新的分辨率配置
        """
        self.a_T_resolution = resolution_config.a_T_resolution
        self.a_N_resolution = resolution_config.a_N_resolution
        self.mu_resolution = resolution_config.mu_resolution
        
        print(f"🔧 DWA分辨率已更新: a_T={self.a_T_resolution}, a_N={self.a_N_resolution}, μ={self.mu_resolution}")
    
    def get_current_resolution(self):
        """获取当前分辨率配置"""
        return {
            'a_T_resolution': self.a_T_resolution,
            'a_N_resolution': self.a_N_resolution,
            'mu_resolution': self.mu_resolution
        }

    def _calc_dynamic_window(self, current_state):
        """计算动态窗口"""
        x, y, z, V, gamma, psi = current_state
        
        # 基于当前状态和约束计算可达控制输入范围
        dw = {
            'a_T_min': -self.a_T_max,
            'a_T_max': self.a_T_max,
            'a_N_min': -self.a_N_max,
            'a_N_max': self.a_N_max,
            'mu_min': -np.pi/2,
            'mu_max': np.pi/2
        }
        
        return dw
    
    def _is_safe_control(self, control, current_state, obstacles, bounds):
        """检查控制输入是否安全"""
        a_T, a_N, mu = control
        x, y, z, V, gamma, psi = current_state
        
        # 预测轨迹
        trajectory = self._predict_trajectory(current_state, control, self.predict_time)
        
        # 检查是否与障碍物碰撞
        for point in trajectory:
            for obs in obstacles:
                if isinstance(obs, dict):
                    obs_center = obs['center']
                    obs_radius = obs['radius']
                else:
                    obs_center = obs[:3]
                    obs_radius = obs[3]
                
                dist = np.linalg.norm(point[:3] - obs_center)
                if dist < obs_radius + self.min_safe_distance:
                    return False
        
        # 检查是否超出边界
        if bounds is not None:
            for point in trajectory:
                if (point[0] < 0 or point[0] > bounds[0] or
                    point[1] < 0 or point[1] > bounds[1] or
                    point[2] < 0 or point[2] > bounds[2]):
                    return False
        
        return True
    
    def _predict_trajectory(self, current_state, control, predict_time):
        """预测轨迹"""
        x, y, z, V, gamma, psi = current_state
        a_T, a_N, mu = control
        
        trajectory = []
        dt = 0.1
        steps = int(predict_time / dt)
        
        for i in range(steps):
            # 简化的运动学模型
            V_new = V + a_T * dt
            V_new = np.clip(V_new, self.V_min, self.V_max)
            
            gamma_new = gamma + (a_N / V) * dt
            gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
            
            psi_new = psi + (a_N * np.sin(mu) / (V * np.cos(gamma))) * dt
            
            x_new = x + V * np.cos(gamma) * np.cos(psi) * dt
            y_new = y + V * np.cos(gamma) * np.sin(psi) * dt
            z_new = z + V * np.sin(gamma) * dt
            
            trajectory.append([x_new, y_new, z_new, V_new, gamma_new, psi_new])
            
            # 更新状态
            x, y, z, V, gamma, psi = x_new, y_new, z_new, V_new, gamma_new, psi_new
        
        return trajectory
    
    def evaluate_control(self, control, current_state, goal, obstacles):
        """评价控制输入"""
        a_T, a_N, mu = control
        x, y, z, V, gamma, psi = current_state
        
        # 目标方向评价
        goal_direction = goal - np.array([x, y, z])
        goal_direction = goal_direction / np.linalg.norm(goal_direction)
        
        # 预测下一个位置
        dt = 0.1
        V_new = np.clip(V + a_T * dt, self.V_min, self.V_max)
        gamma_new = np.clip(gamma + (a_N / V) * dt, -self.gamma_max, self.gamma_max)
        psi_new = psi + (a_N * np.sin(mu) / (V * np.cos(gamma))) * dt
        
        next_pos = np.array([
            x + V * np.cos(gamma) * np.cos(psi) * dt,
            y + V * np.cos(gamma) * np.sin(psi) * dt,
            z + V * np.sin(gamma) * dt
        ])
        
        next_direction = next_pos - np.array([x, y, z])
        if np.linalg.norm(next_direction) > 0:
            next_direction = next_direction / np.linalg.norm(next_direction)
            direction_score = np.dot(goal_direction, next_direction)
        else:
            direction_score = 0
        
        # 速度评价（鼓励巡航速度）
        speed_score = 1.0 - abs(V_new - self.V_cruise) / self.V_cruise
        
        # 距离评价
        current_dist = np.linalg.norm(goal - np.array([x, y, z]))
        next_dist = np.linalg.norm(goal - next_pos)
        distance_score = (current_dist - next_dist) / current_dist if current_dist > 0 else 0
        
        # 障碍物评价
        min_obs_dist = float('inf')
        for obs in obstacles:
            if isinstance(obs, dict):
                obs_center = obs['center']
                obs_radius = obs['radius']
            else:
                obs_center = obs[:3]
                obs_radius = obs[3]
            
            dist = np.linalg.norm(next_pos - obs_center) - obs_radius
            min_obs_dist = min(min_obs_dist, dist)
        
        obstacle_score = min(1.0, min_obs_dist / 50.0) if min_obs_dist > 0 else 0
        
        # 综合评分
        total_score = (self.alpha * direction_score + 
                      self.beta * speed_score + 
                      self.distance_weight * distance_score + 
                      self.delta * obstacle_score)
        
        return total_score
    
    def get_normalized_action(self, control):
        """将控制输入归一化到[-1, 1]范围"""
        a_T, a_N, mu = control
        
        normalized_a_T = a_T / self.a_T_max
        normalized_a_N = a_N / self.a_N_max
        normalized_mu = mu / (np.pi/2)
        
        return np.array([normalized_a_T, normalized_a_N, normalized_mu])
