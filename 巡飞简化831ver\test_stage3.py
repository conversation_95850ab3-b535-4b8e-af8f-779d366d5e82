"""
测试阶段3模型 - 动态环境适应训练
成功率: 4.0% | 环境: 动态障碍物环境
"""

import subprocess
import sys
import os
from datetime import datetime

def main():
    print("🎬 阶段3模型测试 - 动态环境适应训练")
    print("=" * 50)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 预期成功率: 4.0% (极低)")
    print("🌍 测试环境: 动态障碍物环境")
    print("⚙️  测试参数: 最大步数=1500, 帧率=12fps")
    print("⚠️  警告: 此阶段成功率极低，预期会失败")
    print("💡 目的: 观察模型在动态环境中的行为")
    print("=" * 50)
    
    print("🚀 开始生成阶段3的GIF动画...")

    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    gif_generator_path = os.path.join(script_dir, 'test_training_gif_generator.py')

    # 调用完整的GIF生成器
    cmd = [sys.executable, gif_generator_path, '--stage', '3', '--steps', '1500', '--fps', '12']
    print(f"🔧 执行命令: {' '.join(cmd)}")

    try:
        # 直接运行，显示实时输出
        result = subprocess.run(cmd)
        if result.returncode == 0:
            print("\n✅ 阶段3 GIF生成完成!")
            print("📁 请查看生成的文件:")
            print("   • results/gifs/stage3/loitering_munition_stage3_*.gif")
            print("   • results/analysis/loitering_munition_analysis_*.png")
            print("   • results/analysis/loitering_munition_constraints_*.png")
            print("💡 即使任务失败，GIF也能展示模型的尝试过程")
        else:
            print("\n❌ 阶段3 GIF生成失败")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
