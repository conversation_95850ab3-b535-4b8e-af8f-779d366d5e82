This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.11.28)  31 AUG 2025 16:48
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/basic_dwa_rl_framework_717ver/巡飞简化ver/improved_resband_paper.tex
(e:/basic_dwa_rl_framework_717ver/巡飞简化ver/improved_resband_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex.sty (e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count196
\l__pdf_internal_box=\box51
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count197
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen141
\g__ctex_section_depth_int=\count198
\g__ctex_font_size_int=\count199
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-pdftex.def
File: ctex-engine-pdftex.def 2022/07/14 v2.5.10 (pdf)LaTeX adapter (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)) (e:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box53
) (e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)) (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.sty
Package: CJKpunct 2016/05/14 4.8.4
\CJKpunct@cnta=\count266
\CJKpunct@cntb=\count267
\CJKpunct@cntc=\count268
\CJKpunct@cntd=\count269
\CJKpunct@cnte=\count270
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2026 (decimal 8230)
 (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.spa)) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKspace.sty
Package: CJKspace 2021/10/16 3.8.0
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexspa.def
File: ctexspa.def 2022/07/14 v2.5.10 Space info for CJKpunct (CTEX)
)
\ccwd=\dimen142
\l__ctex_ccglue_skip=\skip50
)
\l__ctex_ziju_dim=\dimen143
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count271
\l__zhnum_tmp_int=\count272
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen144
)) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip16
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks20
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks21
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (e:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count285
\float@exts=\toks24
\float@box=\box56
\@float@everytoks=\toks25
\@floatcapt=\box57
) (e:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count286
) (e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
)
\c@ALC@unique=\count287
\c@ALC@line=\count288
\c@ALC@rem=\count289
\c@ALC@depth=\count290
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen153
\Gin@req@width=\dimen154
) (e:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count291
\Gm@cntv=\count292
\c@Gm@tempcnt=\count293
\Gm@bindingoffset=\dimen155
\Gm@wd@mp=\dimen156
\Gm@odd@mp=\dimen157
\Gm@even@mp=\dimen158
\Gm@layoutwidth=\dimen159
\Gm@layoutheight=\dimen160
\Gm@layouthoffset=\dimen161
\Gm@layoutvoffset=\dimen162
\Gm@dimlist=\toks28
) (e:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen163
\lightrulewidth=\dimen164
\cmidrulewidth=\dimen165
\belowrulesep=\dimen166
\belowbottomsep=\dimen167
\aboverulesep=\dimen168
\abovetopsep=\dimen169
\cmidrulesep=\dimen170
\cmidrulekern=\dimen171
\defaultaddspace=\dimen172
\@cmidla=\count294
\@cmidlb=\count295
\@aboverulesep=\dimen173
\@belowrulesep=\dimen174
\@thisruleclass=\count296
\@lastruleclass=\count297
\@thisrulewidth=\dimen175
) (e:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip56
\multirow@cntb=\count298
\multirow@dima=\skip57
\bigstrutjot=\dimen176
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen177
\pgfutil@tempdimb=\dimen178
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box58
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen179
\pgf@y=\dimen180
\pgf@xa=\dimen181
\pgf@ya=\dimen182
\pgf@xb=\dimen183
\pgf@yb=\dimen184
\pgf@xc=\dimen185
\pgf@yc=\dimen186
\pgf@xd=\dimen187
\pgf@yd=\dimen188
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count299
\c@pgf@countb=\count300
\c@pgf@countc=\count301
\c@pgf@countd=\count302
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count303
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count304
\pgfsyssoftpath@bigbuffer@items=\count305
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen189
\pgfmath@count=\count306
\pgfmath@box=\box59
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count307
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen190
\pgf@picmaxx=\dimen191
\pgf@picminy=\dimen192
\pgf@picmaxy=\dimen193
\pgf@pathminx=\dimen194
\pgf@pathmaxx=\dimen195
\pgf@pathminy=\dimen196
\pgf@pathmaxy=\dimen197
\pgf@xx=\dimen198
\pgf@xy=\dimen199
\pgf@yx=\dimen256
\pgf@yy=\dimen257
\pgf@zx=\dimen258
\pgf@zy=\dimen259
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen260
\pgf@path@lasty=\dimen261
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen262
\pgf@shorten@start@additional=\dimen263
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count308
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen264
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen265
\pgf@pt@y=\dimen266
\pgf@pt@temp=\dimen267
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen268
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen269
\pgf@sys@shading@range@num=\count309
\pgf@shadingcount=\count310
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen270
\pgf@nodesepend=\dimen271
) (e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (e:/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen272
\pgffor@skip=\dimen273
\pgffor@stack=\toks39
\pgffor@toks=\toks40
)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count311
\pgfplotmarksize=\dimen274
)
\tikz@lastx=\dimen275
\tikz@lasty=\dimen276
\tikz@lastxsaved=\dimen277
\tikz@lastysaved=\dimen278
\tikz@lastmovetox=\dimen279
\tikz@lastmovetoy=\dimen280
\tikzleveldistance=\dimen281
\tikzsiblingdistance=\dimen282
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count312
\tikznumberofchildren=\count313
\tikznumberofcurrentchild=\count314
\tikz@fig@count=\count315
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count316
\pgfmatrixcurrentcolumn=\count317
\pgf@matrix@numberofcolumns=\count318
)
\tikz@expandcount=\count319
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/latex/tikz-3dplot/tikz-3dplot.sty (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary3d.code.tex
File: tikzlibrary3d.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen283
))) (e:/texlive/2024/texmf-dist/tex/latex/pgfplots/pgfplots.sty (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)
 (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks41
\t@pgfplots@tokb=\toks42
\t@pgfplots@tokc=\toks43
\pgfplots@tmpa=\dimen284
\c@pgfplots@coordindex=\count320
\c@pgfplots@scanlineindex=\count321
 (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_loader.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks44
\t@pgf@tokb=\toks45
\t@pgf@tokc=\toks46
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_pgfutil-common-lists.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructureext.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count322
) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.tex
\c@pgfplotstable@counta=\count323
\t@pgfplotstable@a=\toks47
) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count324
 (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.pgfsys-pdftex.def))) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex))) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen285
\pgfdecoratedremainingdistance=\dimen286
\pgfdecoratedinputsegmentcompleteddistance=\dimen287
\pgfdecoratedinputsegmentremainingdistance=\dimen288
\pgf@decorate@distancetomove=\dimen289
\pgf@decorate@repeatstate=\count325
\pgfdecorationsegmentamplitude=\dimen290
\pgfdecorationsegmentlength=\dimen291
)
\tikz@lib@dec@box=\box69
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathmorphing.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathmorphing.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathreplacing.code.tex (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathreplacing.code.tex)) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count326
\pgfplots@xmin@reg=\dimen292
\pgfplots@xmax@reg=\dimen293
\pgfplots@ymin@reg=\dimen294
\pgfplots@ymax@reg=\dimen295
\pgfplots@zmin@reg=\dimen296
\pgfplots@zmax@reg=\dimen297
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
) (./improved_resband_paper.aux)
\openout1 = `improved_resband_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
 (e:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count327
\scratchdimen=\dimen298
\scratchbox=\box70
\nofMPsegments=\count328
\nofMParguments=\count329
\everyMPshowfont=\toks48
\MPscratchCnt=\count330
\MPscratchDim=\dimen299
\MPnumerator=\count331
\makeMPintoPDFobject=\count332
\everyMPtoPDFconversion=\toks49
) (e:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (e:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package pgfplots notification 'compat/show suggested version=true': document has been generated with the most recent feature set (\pgfplotsset{compat=1.18}).

LaTeX Font Info:    Trying to load font information for C70+rm on input line 27.
(e:/texlive/2024/texmf-dist/tex/latex/ctex/fd/c70rm.fd
File: c70rm.fd 2022/07/14 v2.5.10 Chinese font definition (CTEX)
)
Package CJKpunct Info: use punctuation spaces for family 'rm' with punctstyle (quanjiao) on input line 27.
LaTeX Font Info:    Trying to load font information for U+msa on input line 27.
 (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 27.
 (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) (e:/texlive/2024/texmf-dist/tex/generic/ctex/zhmap/ctex-zhmap-windows.tex
File: ctex-zhmap-windows.tex 2022/07/14 v2.5.10 Windows font map loader for pdfTeX and DVIPDFMx (CTEX)
{e:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{UGBK.sfd}{Unicode.sfd}) [1

] [2{e:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}] [3] [4] [5] [6] (./improved_resband_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********
 ) 
Here is how much of TeX's memory you used:
 30119 strings out of 474116
 738597 string characters out of 5747716
 1952190 words of memory out of 5000000
 51513 multiletter control sequences out of 15000+600000
 659688 words of font info for 410 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 88i,10n,93p,892b,609s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb>
Output written on improved_resband_paper.pdf (6 pages, 567738 bytes).
PDF statistics:
 629 PDF objects out of 1000 (max. 8388607)
 408 compressed objects within 5 object streams
 0 named destinations out of 1000 (max. 500000)
 289 words of extra memory for PDF output out of 10000 (max. 10000000)

