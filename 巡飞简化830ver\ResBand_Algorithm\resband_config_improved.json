{"exploration_coefficient": 2.0, "stage_length": 15, "reward_weights": [0.6, 0.2, 0.1], "adaptive_exploration": true, "performance_threshold": 0.5, "description": "ResBand算法改进配置示例", "parameters": {"exploration_coefficient": "UCB探索系数，控制探索程度", "stage_length": "每个阶段的episode数量", "reward_weights": "回报函数权重 [α, β, γ]，分别对应奖励差异、Critic损失下降、约束违反减少", "adaptive_exploration": "是否启用自适应探索", "performance_threshold": "性能阈值，用于触发自适应调整"}}