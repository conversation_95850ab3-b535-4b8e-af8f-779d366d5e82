"""
ResBand经验库数据结构示例
展示经验库的具体内容和组织方式
"""

import json
import numpy as np

def create_experience_database_example():
    """创建一个ResBand经验库的示例数据结构"""
    
    # 模拟经验库数据结构
    experience_database = {
        
        # 1. 特征-分辨率映射库 (核心经验库)
        "feature_resolution_mapping": {
            
            # 特征签名1: 简单静态场景
            "0.1_0.2_0.3_0.0_0.1": {
                "feature_vector": [0.1, 0.2, 0.3, 0.0, 0.1],  # [密度, 复杂度, 路径难度, 动态比例, 空间约束]
                "first_seen_episode": 5,
                "inferred_arm": 0,  # 启发式推断为粗分辨率
                "arm_performance": {
                    "0": {  # 粗分辨率臂
                        "count": 15,  # 使用了15次
                        "total_reward": 420000.0,  # 累计奖励
                        "avg_reward": 28000.0,  # 平均奖励
                        "performance_history": [
                            {"episode": 5, "reward": 25000, "critic_loss": 0.3, "violations": 0, "success": True},
                            {"episode": 12, "reward": 28500, "critic_loss": 0.25, "violations": 0, "success": True},
                            {"episode": 18, "reward": 30000, "critic_loss": 0.2, "violations": 0, "success": True},
                            # ... 更多历史记录
                        ]
                    },
                    "1": {  # 中等分辨率臂
                        "count": 3,  # 只尝试了3次
                        "total_reward": 75000.0,
                        "avg_reward": 25000.0,
                        "performance_history": [
                            {"episode": 8, "reward": 24000, "critic_loss": 0.35, "violations": 0, "success": True},
                            {"episode": 15, "reward": 26000, "critic_loss": 0.3, "violations": 0, "success": True},
                            {"episode": 22, "reward": 25000, "critic_loss": 0.32, "violations": 0, "success": True}
                        ]
                    },
                    "2": {  # 精细分辨率臂
                        "count": 1,  # 只尝试了1次
                        "total_reward": 22000.0,
                        "avg_reward": 22000.0,
                        "performance_history": [
                            {"episode": 10, "reward": 22000, "critic_loss": 0.4, "violations": 0, "success": True}
                        ]
                    }
                }
            },
            
            # 特征签名2: 复杂静态场景
            "0.4_0.6_0.8_0.0_0.4": {
                "feature_vector": [0.4, 0.6, 0.8, 0.0, 0.4],
                "first_seen_episode": 25,
                "inferred_arm": 1,  # 启发式推断为中等分辨率
                "arm_performance": {
                    "0": {
                        "count": 2,
                        "total_reward": 40000.0,
                        "avg_reward": 20000.0,
                        "performance_history": [
                            {"episode": 28, "reward": 18000, "critic_loss": 0.5, "violations": 1, "success": False},
                            {"episode": 35, "reward": 22000, "critic_loss": 0.45, "violations": 0, "success": True}
                        ]
                    },
                    "1": {
                        "count": 8,
                        "total_reward": 220000.0,
                        "avg_reward": 27500.0,
                        "performance_history": [
                            {"episode": 25, "reward": 25000, "critic_loss": 0.35, "violations": 0, "success": True},
                            {"episode": 30, "reward": 28000, "critic_loss": 0.3, "violations": 0, "success": True},
                            {"episode": 33, "reward": 29000, "critic_loss": 0.28, "violations": 0, "success": True},
                            # ... 更多记录
                        ]
                    },
                    "2": {
                        "count": 3,
                        "total_reward": 78000.0,
                        "avg_reward": 26000.0,
                        "performance_history": [
                            {"episode": 27, "reward": 24000, "critic_loss": 0.4, "violations": 0, "success": True},
                            {"episode": 32, "reward": 27000, "critic_loss": 0.35, "violations": 0, "success": True},
                            {"episode": 37, "reward": 27000, "critic_loss": 0.33, "violations": 0, "success": True}
                        ]
                    }
                }
            },
            
            # 特征签名3: 复杂动态场景
            "0.3_0.5_0.7_1.0_0.3": {
                "feature_vector": [0.3, 0.5, 0.7, 1.0, 0.3],
                "first_seen_episode": 45,
                "inferred_arm": 2,  # 启发式推断为精细分辨率
                "arm_performance": {
                    "0": {
                        "count": 1,
                        "total_reward": 15000.0,
                        "avg_reward": 15000.0,
                        "performance_history": [
                            {"episode": 48, "reward": 15000, "critic_loss": 0.6, "violations": 2, "success": False}
                        ]
                    },
                    "1": {
                        "count": 4,
                        "total_reward": 88000.0,
                        "avg_reward": 22000.0,
                        "performance_history": [
                            {"episode": 50, "reward": 20000, "critic_loss": 0.5, "violations": 1, "success": False},
                            {"episode": 53, "reward": 22000, "critic_loss": 0.45, "violations": 0, "success": True},
                            {"episode": 56, "reward": 23000, "critic_loss": 0.42, "violations": 0, "success": True},
                            {"episode": 59, "reward": 23000, "critic_loss": 0.4, "violations": 0, "success": True}
                        ]
                    },
                    "2": {
                        "count": 7,
                        "total_reward": 189000.0,
                        "avg_reward": 27000.0,
                        "performance_history": [
                            {"episode": 45, "reward": 24000, "critic_loss": 0.45, "violations": 0, "success": True},
                            {"episode": 47, "reward": 26000, "critic_loss": 0.4, "violations": 0, "success": True},
                            {"episode": 51, "reward": 27500, "critic_loss": 0.38, "violations": 0, "success": True},
                            {"episode": 54, "reward": 28000, "critic_loss": 0.35, "violations": 0, "success": True},
                            {"episode": 57, "reward": 28500, "critic_loss": 0.33, "violations": 0, "success": True},
                            {"episode": 60, "reward": 27500, "critic_loss": 0.35, "violations": 0, "success": True},
                            {"episode": 62, "reward": 27500, "critic_loss": 0.34, "violations": 0, "success": True}
                        ]
                    }
                }
            }
        },
        
        # 2. 场景特征历史库
        "scenario_feature_history": [
            {
                "episode": 5,
                "feature_vector": [0.1, 0.2, 0.3, 0.0, 0.1],
                "feature_signature": "0.1_0.2_0.3_0.0_0.1",
                "obstacles_count": 2,
                "dynamic_count": 0
            },
            {
                "episode": 25,
                "feature_vector": [0.4, 0.6, 0.8, 0.0, 0.4],
                "feature_signature": "0.4_0.6_0.8_0.0_0.4",
                "obstacles_count": 5,
                "dynamic_count": 0
            },
            {
                "episode": 45,
                "feature_vector": [0.3, 0.5, 0.7, 1.0, 0.3],
                "feature_signature": "0.3_0.5_0.7_1.0_0.3",
                "obstacles_count": 3,
                "dynamic_count": 3
            }
            # ... 更多历史记录
        ],
        
        # 3. 分辨率选择历史库
        "arm_selection_history": [
            {
                "episode": 5,
                "arm": 0,
                "feature_signature": "0.1_0.2_0.3_0.0_0.1",
                "ucb_values": [0.0, 0.0, 0.0],  # 首次选择，UCB值为0
                "selection_reason": "inferred_from_features"
            },
            {
                "episode": 8,
                "arm": 1,
                "feature_signature": "0.1_0.2_0.3_0.0_0.1",
                "ucb_values": [28000.0, 2.5, 2.5],  # UCB探索
                "selection_reason": "ucb_exploration"
            },
            {
                "episode": 12,
                "arm": 0,
                "feature_signature": "0.1_0.2_0.3_0.0_0.1",
                "ucb_values": [28500.2, 25000.8, 22000.5],  # UCB选择最优
                "selection_reason": "ucb_optimal"
            }
            # ... 更多选择历史
        ],
        
        # 4. 学习统计信息
        "learning_statistics": {
            "total_episodes": 65,
            "unique_feature_signatures": 3,
            "total_arm_switches": 12,
            "convergence_episodes": {
                "0.1_0.2_0.3_0.0_0.1": 15,  # 该特征签名15个episode后收敛
                "0.4_0.6_0.8_0.0_0.4": 8,   # 该特征签名8个episode后收敛
                "0.3_0.5_0.7_1.0_0.3": 7    # 该特征签名7个episode后收敛
            },
            "optimal_arms_learned": {
                "0.1_0.2_0.3_0.0_0.1": 0,  # 学习到最优臂为0
                "0.4_0.6_0.8_0.0_0.4": 1,  # 学习到最优臂为1
                "0.3_0.5_0.7_1.0_0.3": 2   # 学习到最优臂为2
            }
        }
    }
    
    return experience_database

def analyze_experience_database(db):
    """分析经验库的学习效果"""
    print("🗄️ ResBand经验库分析报告")
    print("=" * 60)
    
    mapping = db["feature_resolution_mapping"]
    
    for signature, data in mapping.items():
        print(f"\n📊 特征签名: {signature}")
        print(f"   特征向量: {data['feature_vector']}")
        print(f"   首次遇到: Episode {data['first_seen_episode']}")
        print(f"   初始推断: 臂{data['inferred_arm']}")
        
        # 分析各臂性能
        arm_perf = data['arm_performance']
        best_arm = max(arm_perf.keys(), key=lambda x: arm_perf[x]['avg_reward'] if arm_perf[x]['count'] > 0 else 0)
        
        print(f"   各臂性能:")
        for arm, perf in arm_perf.items():
            if perf['count'] > 0:
                print(f"     臂{arm}: {perf['count']}次使用, 平均奖励={perf['avg_reward']:.1f}")
        
        print(f"   ✅ 学习到的最优臂: {best_arm}")
        
        # 分析学习趋势
        if arm_perf[best_arm]['count'] >= 3:
            history = arm_perf[best_arm]['performance_history']
            if len(history) >= 3:
                early_avg = np.mean([h['reward'] for h in history[:2]])
                late_avg = np.mean([h['reward'] for h in history[-2:]])
                improvement = late_avg - early_avg
                print(f"   📈 性能改进: {improvement:+.1f} (早期={early_avg:.1f} → 后期={late_avg:.1f})")

def demonstrate_ucb_calculation(db):
    """演示UCB计算过程"""
    print(f"\n🎰 UCB计算示例")
    print("=" * 40)
    
    # 以第一个特征签名为例
    signature = "0.1_0.2_0.3_0.0_0.1"
    data = db["feature_resolution_mapping"][signature]
    arm_perf = data['arm_performance']
    
    print(f"特征签名: {signature}")
    print(f"当前各臂性能:")
    
    total_trials = sum(perf['count'] for perf in arm_perf.values())
    c = 2.0  # 探索系数
    
    for arm, perf in arm_perf.items():
        if perf['count'] > 0:
            avg_reward = perf['avg_reward']
            exploration_bonus = c * np.sqrt(np.log(total_trials) / perf['count'])
            ucb_value = avg_reward + exploration_bonus
            
            print(f"  臂{arm}: 平均奖励={avg_reward:.1f}, 探索奖励={exploration_bonus:.1f}, UCB={ucb_value:.1f}")

if __name__ == "__main__":
    # 创建示例经验库
    db = create_experience_database_example()
    
    # 分析经验库
    analyze_experience_database(db)
    
    # 演示UCB计算
    demonstrate_ucb_calculation(db)
    
    # 保存示例到文件
    with open("experience_database_example.json", "w", encoding="utf-8") as f:
        json.dump(db, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 经验库示例已保存到: experience_database_example.json")
