# ResBand算法论文描述

## 1. 算法动机

在巡飞弹的DWA-RL控制框架中，DWA层的动作分辨率直接影响控制精度和计算效率。传统的固定分辨率方法无法适应不同训练阶段的需求。因此，我们提出了一种基于多臂老虎机的自适应分辨率选择算法——Resolution Bandit (ResBand)。

## 2. 问题定义

### 2.1 分辨率配置空间
定义分辨率配置为：
$$\mathbf{c} = (\Delta a_T, \Delta a_N, \Delta \mu)$$

其中：
- $\Delta a_T$：切向加速度分辨率
- $\Delta a_N$：法向加速度分辨率
- $\Delta \mu$：倾斜角分辨率

### 2.2 奖励函数
ResBand的奖励函数定义为：
$$R_m = \alpha \cdot \Delta R_m - \beta \cdot \Delta L_m^{critic} - \gamma \cdot N_m^{violation}$$

其中：
- $\Delta R_m$：策略改进奖励
- $\Delta L_m^{critic}$：Critic网络损失下降
- $N_m^{violation}$：约束违反次数
- $\alpha, \beta, \gamma$：权重系数

## 3. 算法描述

### 3.1 核心算法

**Algorithm 1: Resolution Bandit (ResBand)**

**Input:** 分辨率配置集合 $\mathcal{C} = \{\mathbf{c}_1, \mathbf{c}_2, ..., \mathbf{c}_K\}$，探索系数 $\alpha$，阶段长度 $L$，总episode数 $T$

**Output:** 最优分辨率选择策略

1. **Initialize:** $Q(\mathbf{c}_i) = 0, N(\mathbf{c}_i) = 0, \forall \mathbf{c}_i \in \mathcal{C}$
2. **for** $t = 1$ **to** $T$ **do**
3. 　　**if** $t \leq K$ **then**
4. 　　　　$\mathbf{c}_t = \mathbf{c}_t$  // 初始探索
5. 　　**else**
6. 　　　　$\mathbf{c}_t = \arg\max_{\mathbf{c}_i \in \mathcal{C}} \left[Q(\mathbf{c}_i) + \alpha \sqrt{\frac{\ln(t)}{N(\mathbf{c}_i)}}\right]$
7. 　　**end if**
8. 　　更新DWA分辨率：$\text{DWA.update\_resolution}(\mathbf{c}_t)$
9. 　　执行TD3训练episode
10. 　　**if** $t \bmod L == 0$ **then**
11. 　　　　计算综合奖励：$R_m = \alpha \cdot \Delta R_m - \beta \cdot \Delta L_m^{critic} - \gamma \cdot N_m^{violation}$
12. 　　　　更新UCB统计：
13. 　　　　$N(\mathbf{c}_t) = N(\mathbf{c}_t) + 1$
14. 　　　　$Q(\mathbf{c}_t) = Q(\mathbf{c}_t) + \frac{R_m - Q(\mathbf{c}_t)}{N(\mathbf{c}_t)}$
15. 　　**end if**
16. **end for**

### 3.2 UCB选择策略

UCB值计算：
$$\text{UCB}(\mathbf{c}_i, t) = Q(\mathbf{c}_i) + \alpha \sqrt{\frac{\ln(t)}{N(\mathbf{c}_i)}}$$

其中：
- $Q(\mathbf{c}_i)$：配置 $\mathbf{c}_i$ 的平均奖励
- $N(\mathbf{c}_i)$：配置 $\mathbf{c}_i$ 的选择次数
- $\alpha$：探索系数
- $t$：当前episode数

### 3.3 性能更新机制

在每个更新阶段，算法计算：
$$\Delta R_m = R_{episode} - R_{baseline}$$
$$\Delta L_m^{critic} = L_{previous}^{critic} - L_{current}^{critic}$$

然后更新UCB统计：
$$Q(\mathbf{c}_t) \leftarrow Q(\mathbf{c}_t) + \frac{R_m - Q(\mathbf{c}_t)}{N(\mathbf{c}_t)}$$

## 4. 算法特性

### 4.1 收敛性
根据UCB理论，算法在有限时间内收敛到最优配置，收敛速度与探索系数 $\alpha$ 相关。

### 4.2 探索-利用平衡
- 初始阶段：高探索，确保每个配置都被充分评估
- 中期阶段：平衡探索与利用
- 后期阶段：高利用，专注于最优配置

### 4.3 自适应能力
算法能够根据训练进度和环境复杂度自动调整分辨率选择策略。

## 5. 实现细节

### 5.1 分辨率配置集合
```python
configs = [
    ResolutionConfig(2.0, 8.0, 0.3),   # 高精度
    ResolutionConfig(4.0, 15.0, 0.5),  # 中等精度
    ResolutionConfig(6.0, 20.0, 0.7),  # 低精度
    ResolutionConfig(8.0, 25.0, 1.0),  # 极低精度
]
```

### 5.2 关键参数
- 探索系数：$\alpha = 2.0$
- 更新间隔：$L = 20$ episodes
- 权重系数：$(\alpha, \beta, \gamma) = (0.7, 0.2, 0.1)$

## 6. 复杂度分析

- **时间复杂度**：$O(T \cdot K)$，其中 $T$ 为总episode数，$K$ 为配置数量
- **空间复杂度**：$O(T + K)$

## 7. 实验验证

### 7.1 对比基线
- 固定高分辨率
- 固定低分辨率
- 启发式调度策略

### 7.2 评估指标
- 训练成功率
- 平均奖励
- 计算效率
- 约束违反次数

---

*本算法描述可直接用于SCI论文的算法章节，提供了完整的数学描述和实现细节。*
