"""
巡飞弹动态窗口算法控制器 - 融合版本
基于巡飞714ver的DWA控制器，适配简化ver1的训练接口
"""

import numpy as np
import math

class LoiteringMunitionDWA:
    """巡飞弹动态窗口算法控制器 - 融合版本"""

    def __init__(self, dt=0.1, resolution_config=None):
        self.dt = dt
        self.g = 9.81  # 重力加速度

        # 巡飞弹运动约束（与环境一致）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.V_cruise = 25.0 # 巡航速度（合理的初始速度）
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）

        # DWA参数 - 极度优化计算效率
        self.predict_time = 1.0  # 预测时间窗口（大幅减少）
        self.min_safe_distance = 5.0  # 最小安全距离

        # 控制输入离散化参数（支持动态分辨率）
        if resolution_config is not None:
            self.a_T_resolution = resolution_config.a_T_resolution
            self.a_N_resolution = resolution_config.a_N_resolution
            self.mu_resolution = resolution_config.mu_resolution
        else:
            # 默认分辨率（极度优化）
            self.a_T_resolution = 4.0    # 切向加速度分辨率（大幅减少采样点）
            self.a_N_resolution = 15.0   # 法向加速度分辨率（大幅减少采样点）
            self.mu_resolution = 0.5     # 倾斜角分辨率（大幅减少采样点）

        # 评价函数权重
        self.alpha = 0.4   # 目标方向权重
        self.beta = 0.2    # 速度权重（鼓励巡航速度）
        self.distance_weight = 0.3   # 距离权重（避免与gamma角度冲突）
        self.delta = 0.1   # 障碍物权重
        
    def generate_safe_control_set(self, current_state, obstacles, goal, max_actions=5, bounds=None):
        """
        生成安全控制输入集合 - 优化版本

        Args:
            current_state: [x, y, z, V, γ, ψ] - 当前状态
            obstacles: 障碍物列表
            goal: 目标位置
            max_actions: 最大动作数量（减少到10个）
            bounds: 环境边界 [x_max, y_max, z_max]

        Returns:
            安全的控制输入列表，按评价分数排序
        """
        x, y, z, V, gamma, psi = current_state

        # 计算动态窗口（基于当前状态的可达加速度范围）
        dw = self._calc_dynamic_window(current_state)

        safe_controls_with_scores = []

        # 遍历控制输入空间（使用优化的分辨率）
        # 修复：确保不超出动态窗口范围
        a_T_range = np.arange(dw['a_T_min'], dw['a_T_max'] + self.a_T_resolution/2,
                             self.a_T_resolution)
        a_N_range = np.arange(dw['a_N_min'], dw['a_N_max'] + self.a_N_resolution/2,
                             self.a_N_resolution)
        mu_range = np.arange(dw['mu_min'], dw['mu_max'] + self.mu_resolution/2,
                            self.mu_resolution)

        # 确保不超出约束范围
        a_T_range = a_T_range[a_T_range <= dw['a_T_max']]
        a_N_range = a_N_range[a_N_range <= dw['a_N_max']]
        mu_range = mu_range[mu_range <= dw['mu_max']]

        for a_T in a_T_range:
            for a_N in a_N_range:
                for mu in mu_range:
                    control = np.array([a_T, a_N, mu])
                    
                    # 检查控制输入是否安全
                    if self._is_safe_control(control, current_state, obstacles, bounds):
                        # 计算评价分数
                        score = self.evaluate_control(control, current_state, goal, obstacles)
                        safe_controls_with_scores.append((control, score))

        # 按分数排序（降序）
        safe_controls_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前max_actions个控制输入
        safe_controls = [control for control, score in safe_controls_with_scores[:max_actions]]
        
        return safe_controls
    
    def update_resolution(self, resolution_config):
        """
        更新分辨率配置
        
        Args:
            resolution_config: 新的分辨率配置
        """
        self.a_T_resolution = resolution_config.a_T_resolution
        self.a_N_resolution = resolution_config.a_N_resolution
        self.mu_resolution = resolution_config.mu_resolution
        
        print(f"🔧 DWA分辨率已更新: a_T={self.a_T_resolution}, a_N={self.a_N_resolution}, μ={self.mu_resolution}")
    
    def get_current_resolution(self):
        """获取当前分辨率配置"""
        return {
            'a_T_resolution': self.a_T_resolution,
            'a_N_resolution': self.a_N_resolution,
            'mu_resolution': self.mu_resolution
        }

    def _calc_dynamic_window(self, current_state):
        """计算动态窗口"""
        x, y, z, V, gamma, psi = current_state
        
        # 基于当前状态和约束计算可达控制输入范围
        dw = {
            'a_T_min': -self.a_T_max,
            'a_T_max': self.a_T_max,
            'a_N_min': -self.a_N_max,
            'a_N_max': self.a_N_max,
            'mu_min': -np.pi/2,
            'mu_max': np.pi/2
        }
        
        # 考虑速度约束对切向加速度的限制
        if V <= self.V_min + 1.0:  # 接近失速速度
            dw['a_T_min'] = max(dw['a_T_min'], 0.0)  # 只能加速
        elif V >= self.V_max - 1.0:  # 接近最大速度
            dw['a_T_max'] = min(dw['a_T_max'], 0.0)  # 只能减速
        
        # 考虑航迹倾斜角约束
        if gamma >= self.gamma_max - 0.1:  # 接近最大倾斜角
            # 限制会增加倾斜角的控制输入
            pass
        elif gamma <= -self.gamma_max + 0.1:  # 接近最小倾斜角
            # 限制会减少倾斜角的控制输入
            pass
        
        return dw

    def _is_safe_control(self, control, current_state, obstacles, bounds=None):
        """检查控制输入是否安全（支持动态障碍物运动预测）"""
        # 预测自身轨迹
        predicted_trajectory = self._predict_trajectory(control, current_state, self.predict_time)

        # 分离静态和动态障碍物
        static_obstacles = []
        dynamic_obstacles = []

        for obs in obstacles:
            if 'motion_type' in obs and obs['motion_type'] is not None:
                dynamic_obstacles.append(obs)
            else:
                static_obstacles.append(obs)

        # 检查轨迹是否与障碍物碰撞
        for i, pos in enumerate(predicted_trajectory):
            time_step = i * self.dt  # 当前预测时间点

            # 检查静态障碍物碰撞
            for obs in static_obstacles:
                obs_center = np.array(obs['center'])
                dist = np.linalg.norm(pos - obs_center)
                if dist <= obs['radius'] + self.min_safe_distance:
                    return False

            # 检查动态障碍物碰撞（预测障碍物在time_step时刻的位置）
            for obs in dynamic_obstacles:
                predicted_obs_center = self._predict_dynamic_obstacle_position(obs, time_step)
                dist = np.linalg.norm(pos - predicted_obs_center)
                if dist <= obs['radius'] + self.min_safe_distance:
                    return False

            # 检查边界约束
            if bounds is not None:
                if (pos[0] < 0 or pos[0] > bounds[0] or
                    pos[1] < 0 or pos[1] > bounds[1] or
                    pos[2] < 0 or pos[2] > bounds[2]):
                    return False

        return True

    def _predict_dynamic_obstacle_position(self, obstacle, future_time):
        """
        预测动态障碍物在未来时刻的位置

        Args:
            obstacle: 动态障碍物信息
            future_time: 未来时间（相对于当前时刻）

        Returns:
            预测的障碍物中心位置
        """
        current_time = obstacle.get('time', 0.0)
        total_time = current_time + future_time

        motion_type = obstacle['motion_type']
        motion_params = obstacle['motion_params']

        if motion_type == 'linear':
            # 线性运动预测
            velocity = np.array(motion_params['velocity'])
            bounds = motion_params['bounds']

            # 从当前位置开始预测
            current_center = np.array(obstacle['center'])
            predicted_pos = current_center + velocity * future_time

            # 考虑边界反弹（简化处理）
            # 注意：这里简化了反弹计算，实际应该考虑多次反弹
            predicted_pos = np.clip(predicted_pos,
                                  [bounds['x'][0], bounds['y'][0], bounds['z'][0]],
                                  [bounds['x'][1], bounds['y'][1], bounds['z'][1]])

            return predicted_pos

        elif motion_type == 'circular':
            # 圆周运动预测
            center_orbit = np.array(motion_params['center_orbit'])
            radius_orbit = motion_params['radius_orbit']
            angular_speed = motion_params['angular_speed']
            phase = motion_params['phase']

            angle = angular_speed * total_time + phase
            predicted_pos = center_orbit + np.array([
                radius_orbit * np.cos(angle),
                radius_orbit * np.sin(angle),
                0
            ])

            return predicted_pos

        elif motion_type == 'oscillating':
            # 振荡运动预测
            center_base = np.array(motion_params['center_base'])
            amplitude = np.array(motion_params['amplitude'])
            frequency = motion_params['frequency']
            phase = motion_params['phase']

            predicted_pos = center_base + amplitude * np.sin(frequency * total_time + phase)

            return predicted_pos

        else:
            # 未知运动类型，返回当前位置
            return np.array(obstacle['center'])

    def _predict_trajectory(self, control, current_state, predict_time):
        """预测轨迹"""
        a_T, a_N, mu = control
        x, y, z, V, gamma, psi = current_state
        
        trajectory = []
        dt = self.dt
        steps = int(predict_time / dt)
        
        for _ in range(steps):
            # 六自由度运动学方程
            x += V * np.cos(gamma) * np.cos(psi) * dt
            y += V * np.cos(gamma) * np.sin(psi) * dt
            z += V * np.sin(gamma) * dt
            
            V += (a_T - self.g * np.sin(gamma)) * dt
            V = np.clip(V, self.V_min, self.V_max)
            
            if V > 0.1:
                gamma += (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * dt
                psi += (a_N * np.sin(mu)) / (V * np.cos(gamma)) * dt
            
            gamma = np.clip(gamma, -self.gamma_max, self.gamma_max)
            psi = psi % (2 * np.pi)
            
            trajectory.append(np.array([x, y, z]))
        
        return trajectory

    def evaluate_control(self, control, current_state, goal, obstacles):
        """评价控制输入"""
        # 预测最终状态
        final_state = self._predict_final_state(control, current_state, self.predict_time)
        final_pos = final_state[:3]
        final_V = final_state[3]
        
        # 计算到目标的距离
        goal_dist = np.linalg.norm(final_pos - goal)
        
        # 1. 方向评价（朝向目标）
        goal_direction = goal - current_state[:3]
        if np.linalg.norm(goal_direction) > 0:
            goal_direction = goal_direction / np.linalg.norm(goal_direction)

        # 预测运动方向（使用预测轨迹）
        predicted_trajectory = self._predict_trajectory(control, current_state, self.predict_time)
        if len(predicted_trajectory) > 1:
            # 计算预测的运动方向
            movement = predicted_trajectory[-1][:3] - predicted_trajectory[0][:3]
            if np.linalg.norm(movement) > 1e-6:
                movement_direction = movement / np.linalg.norm(movement)
                heading_score = max(0, np.dot(movement_direction, goal_direction))
            else:
                heading_score = 0.0  # 没有运动
        else:
            heading_score = 0.0  # 预测失败

        # 2. 速度评价（符合现实巡飞弹运动规律的速度策略）
        optimal_speed = self._calculate_realistic_optimal_speed(goal_dist)
        speed_score = 1.0 - abs(final_V - optimal_speed) / (self.V_max - self.V_min)

        # 3. 距离评价（鼓励接近目标）
        distance_score = 1.0 / (1.0 + goal_dist / 100.0)

        # 4. 安全评价（距离障碍物越远越好，考虑动态障碍物预测位置）
        min_obs_dist = float('inf')

        for obs in obstacles:
            if 'motion_type' in obs and obs['motion_type'] is not None:
                # 动态障碍物：使用预测位置
                predicted_obs_center = self._predict_dynamic_obstacle_position(obs, self.predict_time)
            else:
                # 静态障碍物：使用当前位置
                predicted_obs_center = np.array(obs['center'])

            dist = np.linalg.norm(final_pos - predicted_obs_center) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        safety_score = min(min_obs_dist / 50.0, 1.0)

        # 综合评价
        total_score = (self.alpha * heading_score +
                      self.beta * speed_score +
                      self.distance_weight * distance_score +
                      self.delta * safety_score)

        return total_score

    def _predict_final_state(self, control, current_state, predict_time):
        """预测最终状态"""
        a_T, a_N, mu = control
        x, y, z, V, gamma, psi = current_state
        
        dt = self.dt
        steps = int(predict_time / dt)
        
        for _ in range(steps):
            # 六自由度运动学方程
            x += V * np.cos(gamma) * np.cos(psi) * dt
            y += V * np.cos(gamma) * np.sin(psi) * dt
            z += V * np.sin(gamma) * dt
            
            V += (a_T - self.g * np.sin(gamma)) * dt
            V = np.clip(V, self.V_min, self.V_max)
            
            if V > 0.1:
                gamma += (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * dt
                psi += (a_N * np.sin(mu)) / (V * np.cos(gamma)) * dt
            
            gamma = np.clip(gamma, -self.gamma_max, self.gamma_max)
            psi = psi % (2 * np.pi)
        
        return np.array([x, y, z, V, gamma, psi])

    def select_best_control(self, current_state, obstacles, goal):
        """选择最优控制输入"""
        safe_controls = self.generate_safe_control_set(current_state, obstacles, goal)
        
        if not safe_controls:
            # 紧急情况：返回制动控制
            return np.array([-self.a_T_max, 0.0, 0.0])
        
        best_control = None
        best_score = -float('inf')
        
        for control in safe_controls:
            score = self.evaluate_control(control, current_state, goal, obstacles)
            if score > best_score:
                best_score = score
                best_control = control
        
        return best_control if best_control is not None else safe_controls[0]

    def get_normalized_action(self, control):
        """将控制输入归一化到[-1, 1]范围（用于RL训练）"""
        # 确保控制输入在有效范围内
        control_clipped = np.array([
            np.clip(control[0], -self.a_T_max, self.a_T_max),
            np.clip(control[1], -self.a_N_max, self.a_N_max),
            np.clip(control[2], -np.pi/2, np.pi/2)
        ])

        normalized_action = np.array([
            control_clipped[0] / self.a_T_max,      # a_T
            control_clipped[1] / self.a_N_max,      # a_N
            control_clipped[2] / (np.pi/2)          # mu
        ])
        return np.clip(normalized_action, -1.0, 1.0)

    def denormalize_action(self, normalized_action):
        """将归一化动作转换为实际控制输入"""
        control = np.array([
            normalized_action[0] * self.a_T_max,
            normalized_action[1] * self.a_N_max,
            normalized_action[2] * (np.pi/2)
        ])
        return control

    def _calculate_realistic_optimal_speed(self, goal_dist):
        """计算符合现实巡飞弹运动规律的最优速度"""
        # 基于实际巡飞弹任务的速度策略
        if goal_dist > 1500:
            return 45.0  # 远距离高速巡航，快速接近
        elif goal_dist > 800:
            return 35.0  # 中远距离中高速
        elif goal_dist > 400:
            return 28.0  # 中距离正常速度
        elif goal_dist > 150:
            return 22.0  # 近距离减速，准备精确制导
        else:
            return 18.0  # 很近时慢速精确接近

    def get_initial_state(self, start_pos, goal_pos):
        """
        获取合理的初始状态 - 借鉴巡飞714ver的设计

        Args:
            start_pos: 起始位置 [x, y, z]
            goal_pos: 目标位置 [x, y, z]

        Returns:
            initial_state: [x, y, z, V, γ, ψ]
        """
        V_cruise = 25.0  # 巡航速度
        gamma_max = np.pi/3  # 最大倾斜角

        # 计算初始朝向（指向目标）
        direction = goal_pos - start_pos
        distance = np.linalg.norm(direction)

        if distance > 0:
            # 计算初始偏航角和倾斜角
            psi_initial = np.arctan2(direction[1], direction[0])
            gamma_initial = np.arcsin(direction[2] / distance)
            # 限制倾斜角范围
            gamma_initial = np.clip(gamma_initial, -gamma_max, gamma_max)
        else:
            psi_initial = 0.0
            gamma_initial = 0.0

        return np.array([
            start_pos[0], start_pos[1], start_pos[2],
            V_cruise,      # 使用巡航速度作为初始速度
            gamma_initial, # 智能计算的初始倾斜角
            psi_initial    # 智能计算的初始偏航角
        ])
