"""
检查点功能使用示例
演示如何使用训练中断和恢复功能
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demonstrate_checkpoint_usage():
    """演示检查点功能的使用方法"""
    
    print("🔧 巡飞弹训练检查点功能使用指南")
    print("=" * 60)
    
    print("\n📋 1. 正常训练（自动保存检查点）")
    print("   命令: python run_staged_training.py --use-resband --checkpoint-interval 50")
    print("   说明: 每50个episode自动保存一次检查点")
    
    print("\n📋 2. 从检查点恢复训练")
    print("   命令: python run_staged_training.py --resume results/training/loitering_munition_staged_training_20250831_171520")
    print("   说明: 从指定的训练目录恢复训练")
    
    print("\n📋 3. 检查点文件结构")
    print("   训练目录/")
    print("   ├── checkpoints/")
    print("   │   ├── checkpoint_stage_1_episode_50_20250831_171520_training_state.json")
    print("   │   ├── checkpoint_stage_1_episode_50_20250831_171520_td3_model.pth")
    print("   │   └── checkpoint_stage_1_episode_50_20250831_171520_resband.json")
    print("   ├── models/")
    print("   ├── plots/")
    print("   └── data/")
    
    print("\n📋 4. 检查点包含的信息")
    print("   ✅ 训练状态: 当前阶段、episode数、全局进度")
    print("   ✅ TD3模型: 网络权重、优化器状态")
    print("   ✅ ResBand状态: 学习到的特征映射、UCB参数")
    print("   ✅ 训练历史: 奖励历史、成功率等统计信息")
    
    print("\n📋 5. 使用场景")
    print("   🔄 训练意外中断后恢复")
    print("   🔄 调整超参数后继续训练")
    print("   🔄 在不同机器间迁移训练")
    print("   🔄 实验对比和分析")
    
    print("\n📋 6. 注意事项")
    print("   ⚠️  确保恢复时的环境配置与保存时一致")
    print("   ⚠️  检查点文件较大，注意存储空间")
    print("   ⚠️  恢复训练时会继续使用原始的随机种子")
    
    print("\n🎯 完整示例命令:")
    print("   # 开始训练并设置检查点间隔")
    print("   python run_staged_training.py --use-resband --start-stage 1 --end-stage 3 --checkpoint-interval 25")
    print()
    print("   # 从检查点恢复训练")
    print("   python run_staged_training.py --resume results/training/loitering_munition_staged_training_20250831_171520")

def check_checkpoint_files(training_dir):
    """检查指定训练目录的检查点文件"""
    checkpoint_dir = os.path.join(training_dir, "checkpoints")
    
    if not os.path.exists(checkpoint_dir):
        print(f"❌ 检查点目录不存在: {checkpoint_dir}")
        return
    
    print(f"📁 检查点目录: {checkpoint_dir}")
    
    checkpoints = []
    for file in os.listdir(checkpoint_dir):
        if file.endswith("_training_state.json"):
            checkpoint_name = file.replace("_training_state.json", "")
            checkpoints.append(checkpoint_name)
    
    if not checkpoints:
        print("   📝 没有找到检查点文件")
        return
    
    print(f"   📝 找到 {len(checkpoints)} 个检查点:")
    for checkpoint in sorted(checkpoints):
        print(f"      - {checkpoint}")
        
        # 检查相关文件是否完整
        base_path = os.path.join(checkpoint_dir, checkpoint)
        files_exist = {
            'training_state': os.path.exists(f"{base_path}_training_state.json"),
            'td3_model': os.path.exists(f"{base_path}_td3_model.pth"),
            'resband': os.path.exists(f"{base_path}_resband.json")
        }
        
        missing_files = [name for name, exists in files_exist.items() if not exists]
        if missing_files:
            print(f"        ⚠️  缺少文件: {', '.join(missing_files)}")
        else:
            print(f"        ✅ 文件完整")

if __name__ == "__main__":
    demonstrate_checkpoint_usage()
    
    # 如果提供了训练目录参数，检查其检查点文件
    if len(sys.argv) > 1:
        training_dir = sys.argv[1]
        print(f"\n🔍 检查训练目录: {training_dir}")
        check_checkpoint_files(training_dir)
