"""
ResBand算法调试脚本
验证算法是否正常工作，特别是奖励更新机制
"""

import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resolution_bandit import ResolutionBandit, create_paper_configs

def debug_resband():
    """调试ResBand算法"""
    print("🔍 开始调试ResBand算法")
    print("=" * 50)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=5,  # 短阶段长度便于观察
        reward_weights=(0.6, 0.2, 0.1),
        adaptive_exploration=True,
        performance_threshold=0.5
    )
    
    print(f"✅ ResBand创建成功，共有 {resband.K} 个臂")
    print(f"初始探索系数: {resband.c}")
    print(f"阶段长度: {resband.L}")
    
    # 模拟训练过程
    print("\n🚀 开始模拟训练...")
    
    for episode in range(20):  # 20个episodes
        # 模拟训练指标
        training_metrics = {
            'success_rate': np.random.uniform(0.2, 0.8),
            'avg_reward': np.random.uniform(-50, 50),
            'critic_loss': np.random.uniform(0.1, 2.0),
            'violation_rate': np.random.uniform(0.0, 0.3)
        }
        
        # 选择分辨率
        resolution = resband.select_resolution(episode, training_metrics)
        
        # 模拟episode结果
        episode_reward = np.random.uniform(-100, 100)
        critic_loss = np.random.uniform(0.1, 2.0)
        violation_count = np.random.randint(0, 5)
        success = np.random.choice([True, False], p=[0.6, 0.4])
        
        # 更新性能
        resband.update_performance(
            episode, episode_reward, critic_loss, violation_count,
            success=success, training_metrics=training_metrics
        )
        
        # 输出详细信息
        print(f"\nEpisode {episode}:")
        print(f"  选择分辨率: {resolution.name}")
        print(f"  当前臂: {resband.current_arm}")
        print(f"  探索系数: {resband.c:.3f}")
        print(f"  平均回报: {resband.Q}")
        print(f"  选择次数: {resband.N}")
        
        # 检查是否触发了臂选择更新
        if episode > 0 and episode % resband.L == 0:
            print(f"  🔄 触发臂选择更新!")
        
        # 检查训练进度
        print(f"  总episodes: {resband.training_progress['total_episodes']}")
        print(f"  自适应次数: {len(resband.adaptation_history)}")
    
    # 获取最终摘要
    summary = resband.get_training_summary()
    print(f"\n📊 最终摘要:")
    print(f"  总episodes: {summary['total_episodes']}")
    print(f"  最终探索系数: {summary['current_exploration_coefficient']:.3f}")
    print(f"  自适应次数: {summary['adaptation_count']}")
    print(f"  最优臂: {summary['best_arm']}")
    print(f"  臂选择分布: {summary['arm_selection_distribution']}")
    print(f"  平均回报: {summary['average_rewards']}")
    
    # 检查是否有变化
    print(f"\n🔍 检查算法是否正常工作:")
    
    # 检查1: 臂选择是否有变化
    unique_arms = set()
    for record in resband.arm_selection_history:
        unique_arms.add(record['arm'])
    
    if len(unique_arms) > 1:
        print(f"✅ 臂选择有变化: 使用了 {len(unique_arms)} 个不同的臂")
    else:
        print(f"❌ 臂选择没有变化: 只使用了 {len(unique_arms)} 个臂")
    
    # 检查2: 探索系数是否有变化
    exploration_coefficients = [record['exploration_coefficient'] for record in resband.arm_selection_history]
    unique_coeffs = set(exploration_coefficients)
    
    if len(unique_coeffs) > 1:
        print(f"✅ 探索系数有变化: {len(unique_coeffs)} 个不同的值")
        print(f"   范围: {min(unique_coeffs):.3f} - {max(unique_coeffs):.3f}")
    else:
        print(f"❌ 探索系数没有变化: 始终为 {list(unique_coeffs)[0]:.3f}")
    
    # 检查3: 奖励值是否有变化
    if len(resband.reward_history) > 0:
        rewards = [record['reward'] for record in resband.reward_history]
        unique_rewards = set([round(r, 3) for r in rewards])
        
        if len(unique_rewards) > 1:
            print(f"✅ 奖励值有变化: {len(unique_rewards)} 个不同的值")
            print(f"   范围: {min(rewards):.3f} - {max(rewards):.3f}")
        else:
            print(f"❌ 奖励值没有变化: 始终为 {list(unique_rewards)[0]:.3f}")
    else:
        print(f"❌ 没有奖励历史记录")
    
    # 检查4: 自适应调整是否工作
    if len(resband.adaptation_history) > 0:
        print(f"✅ 自适应调整工作正常: {len(resband.adaptation_history)} 次调整")
        for i, adapt in enumerate(resband.adaptation_history[-3:]):  # 显示最后3次
            print(f"   调整 {i+1}: {adapt['adaptation_type']} (episode {adapt['episode']})")
    else:
        print(f"❌ 没有自适应调整记录")
    
    return resband

if __name__ == "__main__":
    debug_resband()
