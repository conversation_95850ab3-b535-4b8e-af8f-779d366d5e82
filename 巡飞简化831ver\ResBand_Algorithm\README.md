# ResBand算法 - 巡飞弹自适应分辨率调度

## 概述

ResBand (Resolution Bandit) 是一个基于多臂老虎机的自适应分辨率调度算法，用于优化巡飞弹DWA层的控制输入离散化分辨率。该算法能够智能地在探索效率和计算精度之间找到平衡。

## 🎯 核心特性

- **自适应分辨率调度**: 基于UCB策略智能选择最优分辨率配置
- **多维度评估**: 综合考虑奖励、学习稳定性和安全性
- **计算效率优化**: 仅在阶段结束时更新，计算开销小
- **完整训练框架**: 包含训练、测试、对比实验的完整流程
- **丰富的可视化**: 自动生成训练进度和算法分析图表

## 📁 文件结构

```
ResBand_Algorithm/
├── resolution_bandit.py          # ResBand核心算法
├── loitering_munition_dwa.py     # 支持动态分辨率的DWA控制器
├── simple_environment.py         # 简化的巡飞弹环境
├── simple_td3.py                 # 简化的TD3网络
├── resband_trainer.py            # ResBand训练器
├── resband_comparison.py         # 对比实验脚本
├── run_resband.py                # 主运行脚本
├── resband_config_example.json   # 配置文件示例
└── README.md                     # 本文档
```

## 🚀 快速开始

### 1. 基本训练

```bash
# 使用ResBand算法训练200个episodes
python run_resband.py --mode train --episodes 200
```

### 2. 使用自定义配置

```bash
# 使用自定义配置文件训练
python run_resband.py --mode train --episodes 300 --resband-config my_config.json
```

### 3. 测试模型

```bash
# 测试训练好的模型
python run_resband.py --mode test --test-episodes 100 --model-path results/models/final_model.pth
```

### 4. 运行对比实验

```bash
# 运行完整的对比实验
python run_resband.py --mode comparison
```

## 📊 算法原理

### 核心思想

ResBand算法将不同的离散化分辨率配置视为老虎机的"臂"，通过UCB策略在线学习选择最优的采样策略，实现探索与利用的智能平衡。

### 回报函数

```
r_bandit = α × ΔR_m + β × (-ΔL_m_critic) + γ × (-N_m_violation)
```

其中：
- `ΔR_m`: 阶段m与阶段m-1的平均片段奖励之差
- `-ΔL_m_critic`: Critic网络平均损失的下降值
- `-N_m_violation`: 平均约束违反次数的减少
- `α, β, γ`: 权重系数，满足 α + β + γ = 1

### 分辨率配置

算法支持三种分辨率配置：

1. **粗分辨率**: a_T=3.0, a_N=12.0, μ=0.3 (计算效率优先)
2. **中等分辨率**: a_T=1.5, a_N=6.0, μ=0.15 (论文默认配置)
3. **细分辨率**: a_T=0.8, a_N=3.0, μ=0.08 (高精度控制)

## ⚙️ 配置参数

### ResBand算法参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `exploration_coefficient` | 2.0 | UCB探索系数，控制探索程度 |
| `stage_length` | 20 | 每个阶段的episode数量 |
| `reward_weights` | [0.7, 0.2, 0.1] | 回报函数权重 [α, β, γ] |

### 训练参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `episodes` | 200 | 训练episodes数量 |
| `save_interval` | 50 | 保存间隔 |
| `plot_interval` | 20 | 绘图间隔 |
| `test_episodes` | 50 | 测试episodes数量 |

## 📈 输出结果

### 训练输出

- **模型文件**: `models/final_model.pth`
- **训练历史**: `data/final_training_history.json`
- **ResBand结果**: `data/final_resband_results.json`
- **训练进度图**: `plots/training_progress_episode_*.png`
- **ResBand分析图**: `resband_analysis_*.png`

### 对比实验输出

- **对比结果**: `comparison_results.json`
- **对比图表**: `comparison_plots.png`
- **对比报告**: `comparison_report.md`

## 🧪 实验设计

### 基线方法

1. **Fixed-Coarse**: 固定使用粗分辨率
2. **Fixed-Medium**: 固定使用中等分辨率
3. **Fixed-Fine**: 固定使用细分辨率
4. **Heuristic**: 启发式调度（每100个episode切换）
5. **ResBand**: 我们的自适应算法

### 评估指标

- **成功率**: 成功到达目标的episode比例
- **平均奖励**: 最终阶段的平均奖励
- **收敛速度**: 达到稳定性能所需的episode数
- **训练时间**: 总训练时间
- **约束违反**: 违反安全约束的次数

## 📖 使用示例

### 1. 创建配置文件

```bash
python run_resband.py --create-config
```

### 2. 查看使用示例

```bash
python run_resband.py --help-examples
```

### 3. 编程接口使用

```python
from resband_trainer import ResBandTrainer

# 创建训练器
trainer = ResBandTrainer(
    use_resband=True,
    resband_config={
        'exploration_coefficient': 2.0,
        'stage_length': 20,
        'reward_weights': (0.7, 0.2, 0.1)
    }
)

# 训练
results = trainer.train(num_episodes=200)

# 测试
test_results = trainer.run_test(num_test_episodes=50)
```

## 🔧 高级功能

### 1. 自定义分辨率配置

```python
from resolution_bandit import ResolutionConfig

# 创建自定义配置
custom_configs = [
    ResolutionConfig("超粗", 5.0, 20.0, 0.8, "最高效率"),
    ResolutionConfig("超细", 0.5, 2.0, 0.05, "最高精度")
]
```

### 2. 动态权重调整

```python
# 根据训练进度调整权重
def adaptive_weights(episode, total_episodes):
    progress = episode / total_episodes
    alpha = 0.7 * (1 - progress) + 0.3 * progress
    beta = 0.2
    gamma = 0.1 * (1 - progress) + 0.3 * progress
    return (alpha, beta, gamma)
```

## 📊 性能分析

### 优势

1. **自适应优化**: 根据训练进度自动调整分辨率
2. **探索-利用平衡**: UCB策略确保充分探索和有效利用
3. **多维度评估**: 综合考虑奖励、学习稳定性和安全性
4. **计算效率**: 仅在阶段结束时更新，计算开销小

### 适用场景

- **训练初期**: 倾向于选择粗分辨率以快速探索
- **训练中期**: 平衡分辨率和精度
- **训练后期**: 倾向于选择细分辨率以精细优化

## 🐛 故障排除

### 常见问题

1. **算法不收敛**
   - 检查回报函数权重设置
   - 调整探索系数
   - 增加阶段长度

2. **性能提升不明显**
   - 检查分辨率配置范围
   - 调整UCB参数
   - 验证性能指标计算

3. **计算开销过大**
   - 减少分辨率配置数量
   - 增加阶段长度
   - 简化回报函数

### 调试技巧

1. **启用详细日志**
   ```python
   # 在训练循环中添加调试信息
   print(f"Episode {episode}: 选择分辨率 {resolution}")
   print(f"性能指标: reward={reward}, loss={critic_loss}, violations={violations}")
   ```

2. **可视化分析**
   ```python
   # 生成分析图表
   resband.plot_results()
   ```

## 📚 论文集成

### 在论文中的描述

**3.4 基于元学习的自适应分辨率调度算法**

巡飞弹的大航程与高机动性导致了其控制输入范围大、安全动作集 `U_safe` 规模可变的特点。为确保分层架构的效率，DWA层需对连续控制空间 `u = [a_T, a_N, μ]^T` 进行离散化采样。固定分辨率方案在探索效率与控制精度间难以兼顾：粗分辨率虽计算高效但控制粗糙；细分辨率虽能精细控制但计算成本高昂，且在训练初期易因动作空间过大而降低探索效率。

为解决这一矛盾，我们提出了一种基于多臂老虎机（Multi-Armed Bandit, MAB）的元学习算法——Resolution Bandit (ResBand)。该算法将分辨率配置的选择建模为一个序列决策问题，其核心思想是：**将不同的离散化分辨率配置视为老虎机的"臂"，通过评估每个"臂"（即每种分辨率配置）在阶段性训练中带来的"学习效率回报"，在线学习选择最优的采样策略，从而实现探索与利用的智能平衡。**

### 实验设计

1. **基线方法**
   - Fixed-Coarse: 固定使用粗分辨率
   - Fixed-Medium: 固定使用中等分辨率
   - Fixed-Fine: 固定使用细分辨率
   - Heuristic: 启发式调度（每100个episode切换）

2. **评估指标**
   - 成功率
   - 最终平均奖励
   - 收敛速度
   - 训练时间

3. **结果分析**
   - 性能对比图表
   - 收敛曲线分析
   - 算法选择行为分析

## 🤝 贡献

欢迎提交Issue和Pull Request来改进ResBand算法！

## 📄 许可证

本项目采用MIT许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 创建Pull Request

---

**ResBand算法** - 让巡飞弹的分层运动规划更加智能！🎰
