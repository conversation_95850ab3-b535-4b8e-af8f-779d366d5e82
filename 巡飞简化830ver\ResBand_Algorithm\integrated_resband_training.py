"""
集成ResBand算法的分阶段训练脚本
将改进的ResBand算法直接集成到巡飞简化ver的分阶段训练中
替换原有的固定分辨率选取方式
"""

import numpy as np
import torch
import time
import os
import json
import sys
from datetime import datetime
import matplotlib.pyplot as plt

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resolution_bandit import ResolutionBandit, create_paper_configs, create_fast_configs
from loitering_munition_dwa import LoiteringMunitionDWA
from simple_environment import SimpleLoiteringMunitionEnvironment
from simple_td3 import StabilizedTD3Controller

class IntegratedResBandTrainer:
    """
    集成ResBand算法的分阶段训练器
    
    特点：
    1. 直接使用ResBand的动态分辨率选择
    2. 与强化学习训练深度融合
    3. 支持分阶段训练
    4. 实时自适应调整
    """
    
    def __init__(self, 
                 use_resband=True,
                 resband_config=None,
                 output_dir="results/integrated_resband",
                 stage_configs=None):
        """
        初始化集成训练器
        
        Args:
            use_resband: 是否使用ResBand算法
            resband_config: ResBand配置
            output_dir: 输出目录
            stage_configs: 分阶段配置
        """
        self.use_resband = use_resband
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建输出子目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = os.path.join(output_dir, f"integrated_run_{self.timestamp}")
        os.makedirs(self.run_dir, exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "models"), exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "plots"), exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "data"), exist_ok=True)
        
        # TD3配置
        self.td3_config = {
            'state_dim': 15,
            'action_dim': 3,
            'max_action': 1.0,
            'lr': 3e-4,
            'batch_size': 256,
            'gamma': 0.99,
            'tau': 0.005,
            'policy_noise': 0.2,
            'noise_clip': 0.5,
            'policy_freq': 2,
            'buffer_size': 1000000,
            'hidden_dim': 256
        }
        
        # 分阶段配置
        if stage_configs is None:
            self.stage_configs = {
                'stage1': {
                    'name': '简单环境',
                    'episodes': 100,
                    'obstacle_count': 3,
                    'complexity': 'simple'
                },
                'stage2': {
                    'name': '中等环境',
                    'episodes': 150,
                    'obstacle_count': 6,
                    'complexity': 'medium'
                },
                'stage3': {
                    'name': '复杂环境',
                    'episodes': 200,
                    'obstacle_count': 10,
                    'complexity': 'complex'
                }
            }
        else:
            self.stage_configs = stage_configs
        
        # 初始化ResBand算法
        if self.use_resband:
            if resband_config is None:
                resband_config = {
                    'exploration_coefficient': 2.0,
                    'stage_length': 15,  # 减少阶段长度以更快适应
                    'reward_weights': (0.6, 0.2, 0.1),  # 调整权重（3个权重）
                    'adaptive_exploration': True,
                    'performance_threshold': 0.5,
                    'use_fast_configs': False
                }
            
            self.resband = ResolutionBandit(
                configs=None,
                exploration_coefficient=resband_config['exploration_coefficient'],
                stage_length=resband_config['stage_length'],
                reward_weights=resband_config['reward_weights'],
                output_dir=self.run_dir,
                use_fast_configs=resband_config.get('use_fast_configs', False),
                adaptive_exploration=resband_config.get('adaptive_exploration', True),
                performance_threshold=resband_config.get('performance_threshold', 0.5)
            )
            print(f"🎰 ResBand算法已集成")
        else:
            self.resband = None
            print(f"⚙️ 使用固定分辨率配置")
        
        # 训练记录
        self.training_history = {
            'stages': {},
            'global_episodes': [],
            'global_rewards': [],
            'global_successes': [],
            'global_violations': [],
            'resolutions': [],
            'training_metrics': []
        }
        
        # 实时训练指标
        self.current_training_metrics = {
            'success_rate': 0.0,
            'avg_reward': 0.0,
            'critic_loss': 0.0,
            'violation_rate': 0.0
        }
        
        # 全局episode计数器
        self.global_episode_count = 0
        
        print(f"🚀 集成ResBand训练器初始化完成")
        print(f"📁 输出目录: {self.run_dir}")
        print(f"📊 分阶段配置: {len(self.stage_configs)} 个阶段")
    
    def _update_training_metrics(self, episode_num: int):
        """更新训练指标"""
        if len(self.training_history['global_rewards']) > 0:
            window = min(10, len(self.training_history['global_rewards']))
            recent_rewards = self.training_history['global_rewards'][-window:]
            recent_successes = self.training_history['global_successes'][-window:]
            recent_violations = self.training_history['global_violations'][-window:]
            
            self.current_training_metrics = {
                'success_rate': np.mean(recent_successes),
                'avg_reward': np.mean(recent_rewards),
                'critic_loss': self.controller.get_last_critic_loss() if hasattr(self, 'controller') else 0.0,
                'violation_rate': np.mean(recent_violations) / 100.0
            }
    
    def train_stage(self, stage_name: str, stage_config: dict):
        """训练单个阶段"""
        print(f"\n🎯 开始训练阶段: {stage_config['name']}")
        print(f"📊 配置: {stage_config}")
        print("-" * 60)
        
        # 创建环境
        self.env = SimpleLoiteringMunitionEnvironment(
            obstacle_count=stage_config['obstacle_count'],
            complexity=stage_config['complexity']
        )
        
        # 创建或继承控制器
        if not hasattr(self, 'controller'):
            self.controller = StabilizedTD3Controller(self.td3_config)
            print(f"🆕 创建新的TD3控制器")
        else:
            print(f"🔄 继承上一阶段的控制器")
        
        # 创建DWA控制器
        if self.use_resband:
            resolution_config = self.resband.select_resolution(
                self.global_episode_count,
                training_metrics=self.current_training_metrics
            )
            self.dwa = LoiteringMunitionDWA(dt=0.1, resolution_config=resolution_config)
            print(f"🎰 使用ResBand选择的分辨率: {resolution_config}")
        else:
            self.dwa = LoiteringMunitionDWA(dt=0.1)
            print(f"⚙️ 使用固定分辨率配置")
        
        # 阶段训练记录
        stage_history = {
            'episodes': [],
            'rewards': [],
            'successes': [],
            'violations': [],
            'resolutions': []
        }
        
        success_count = 0
        stage_start_time = time.time()
        
        for episode in range(stage_config['episodes']):
            # 更新训练指标
            self._update_training_metrics(self.global_episode_count)
            
            # ResBand: 更新分辨率配置
            if self.use_resband:
                resolution_config = self.resband.select_resolution(
                    self.global_episode_count,
                    training_metrics=self.current_training_metrics
                )
                self.dwa.update_resolution(resolution_config)
            
            # 训练单个episode
            reward, success, violations, trajectory = self._train_single_episode()
            
            if success:
                success_count += 1
            
            # 记录阶段历史
            stage_history['episodes'].append(episode)
            stage_history['rewards'].append(reward)
            stage_history['successes'].append(success)
            stage_history['violations'].append(violations)
            
            if self.use_resband:
                current_resolution = self.dwa.get_current_resolution()
                stage_history['resolutions'].append({
                    'episode': episode,
                    'a_T': current_resolution['a_T_resolution'],
                    'a_N': current_resolution['a_N_resolution'],
                    'mu': current_resolution['mu_resolution'],
                    'exploration_coefficient': self.resband.c if self.resband else None
                })
            
            # 记录全局历史
            self.training_history['global_episodes'].append(self.global_episode_count)
            self.training_history['global_rewards'].append(reward)
            self.training_history['global_successes'].append(success)
            self.training_history['global_violations'].append(violations)
            self.training_history['training_metrics'].append(self.current_training_metrics.copy())
            
            # 输出进度
            success_rate = success_count / (episode + 1)
            status = "✅成功" if success else "❌失败"
            
            resband_info = ""
            if self.use_resband and self.resband:
                resband_info = f", ResBand探索系数: {self.resband.c:.3f}"
            
            print(f"Stage {stage_name} Episode {episode+1:3d}/{stage_config['episodes']}: "
                  f"奖励 {reward:7.1f}, {status}, 违反 {violations}, "
                  f"成功率 {success_rate:.1%}{resband_info}")
            
            self.global_episode_count += 1
        
        # 阶段完成
        stage_time = time.time() - stage_start_time
        stage_success_rate = success_count / stage_config['episodes']
        stage_avg_reward = np.mean(stage_history['rewards'])
        stage_total_violations = sum(stage_history['violations'])
        
        # 保存阶段结果
        stage_results = {
            'name': stage_config['name'],
            'episodes': stage_config['episodes'],
            'success_rate': stage_success_rate,
            'avg_reward': stage_avg_reward,
            'total_violations': stage_total_violations,
            'training_time': stage_time,
            'history': stage_history
        }
        
        self.training_history['stages'][stage_name] = stage_results
        
        print(f"\n✅ 阶段 {stage_name} 完成!")
        print(f"🎯 成功率: {stage_success_rate:.1%}")
        print(f"⏱️  训练时间: {stage_time:.1f}秒")
        print(f"📊 平均奖励: {stage_avg_reward:.2f}")
        print(f"🚫 总违反次数: {stage_total_violations}")
        
        if self.use_resband and self.resband:
            summary = self.resband.get_training_summary()
            print(f"🎰 ResBand阶段摘要:")
            print(f"   总episodes: {summary['total_episodes']}")
            print(f"   自适应次数: {summary['adaptation_count']}")
            print(f"   当前最优臂: {summary['best_arm']}")
            print(f"   当前探索系数: {summary['current_exploration_coefficient']:.3f}")
        
        return stage_results
    
    def _train_single_episode(self):
        """训练单个episode"""
        observation = self.env.reset()
        state = self.env.state
        episode_reward = 0
        episode_success = False
        episode_violations = 0
        trajectory = []

        while True:
            trajectory.append(state[:3].copy())
            
            # DWA生成安全动作集
            safe_controls = self.dwa.generate_safe_control_set(
                state, 
                self.env.get_obstacles(), 
                self.env.get_goal(),
                max_actions=10
            )
            
            # 选择动作
            if safe_controls:
                safe_actions = [self.dwa.get_normalized_action(control) for control in safe_controls]
                action = self.controller.select_best_action_from_safe_set(observation, safe_actions)
            else:
                action = np.array([-0.5, 0.0, 0.0])
            
            # 执行动作
            control_input = np.array([
                action[0] * self.env.a_T_max,
                action[1] * self.env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            next_observation, reward, done, info = self.env.step(control_input)
            next_state = self.env.state
            episode_reward += reward

            if info.get('collision', False) or info.get('out_of_bounds', False):
                episode_violations += 1

            if info.get('success', False):
                episode_success = True

            # 存储经验
            self.controller.replay_buffer.add(
                observation.copy(),
                action.copy(),
                reward,
                next_observation.copy(),
                done
            )

            # 训练更新
            self.controller.immediate_update(batch_size=self.td3_config['batch_size'])

            if done:
                break

            observation = next_observation
            state = next_state
        
        # ResBand: 更新性能指标
        if self.use_resband:
            critic_loss = self.controller.get_last_critic_loss()
            self.resband.update_performance(
                self.global_episode_count,
                episode_reward,
                critic_loss,
                episode_violations,
                success=episode_success,
                training_metrics=self.current_training_metrics
            )
        
        return episode_reward, episode_success, episode_violations, trajectory
    
    def run_training(self):
        """运行完整的分阶段训练"""
        print(f"\n🚀 开始集成ResBand分阶段训练")
        print(f"📊 总阶段数: {len(self.stage_configs)}")
        print(f"🎰 ResBand集成: {self.use_resband}")
        print("=" * 60)
        
        total_start_time = time.time()
        
        # 依次训练每个阶段
        for stage_name, stage_config in self.stage_configs.items():
            try:
                stage_results = self.train_stage(stage_name, stage_config)
                print(f"✅ 阶段 {stage_name} 训练成功")
            except Exception as e:
                print(f"❌ 阶段 {stage_name} 训练失败: {str(e)}")
                self.training_history['stages'][stage_name] = {'error': str(e)}
        
        # 训练完成
        total_time = time.time() - total_start_time
        
        # 计算总体结果
        total_episodes = sum(config['episodes'] for config in self.stage_configs.values())
        total_successes = sum(self.training_history['global_successes'])
        overall_success_rate = total_successes / total_episodes
        overall_avg_reward = np.mean(self.training_history['global_rewards'])
        total_violations = sum(self.training_history['global_violations'])
        
        # 保存最终结果
        final_results = {
            'total_training_time': total_time,
            'total_episodes': total_episodes,
            'overall_success_rate': overall_success_rate,
            'overall_avg_reward': overall_avg_reward,
            'total_violations': total_violations,
            'training_history': self.training_history
        }
        
        # 保存ResBand结果
        if self.use_resband and self.resband:
            self.resband.save_results()
            self.resband.plot_results()
            final_results['resband_summary'] = self.resband.get_training_summary()
        
        # 保存最终结果
        self.save_final_results(final_results)
        
        print(f"\n🎉 集成ResBand分阶段训练完成!")
        print(f"⏱️  总训练时间: {total_time:.1f}秒")
        print(f"📊 总episodes: {total_episodes}")
        print(f"🎯 总体成功率: {overall_success_rate:.1%}")
        print(f"📈 总体平均奖励: {overall_avg_reward:.2f}")
        print(f"🚫 总违反次数: {total_violations}")
        
        if self.use_resband and self.resband:
            summary = self.resband.get_training_summary()
            print(f"🎰 ResBand最终摘要:")
            print(f"   总episodes: {summary['total_episodes']}")
            print(f"   自适应调整次数: {summary['adaptation_count']}")
            print(f"   最优分辨率配置: Arm {summary['best_arm']}")
            print(f"   最终探索系数: {summary['current_exploration_coefficient']:.3f}")
        
        print(f"📁 结果保存在: {self.run_dir}")
        
        return final_results
    
    def save_final_results(self, results):
        """保存最终结果"""
        # 保存训练历史
        history_file = os.path.join(self.run_dir, "data", "final_training_history.json")
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        
        # 保存最终结果
        results_file = os.path.join(self.run_dir, "data", "final_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 保存最终模型
        if hasattr(self, 'controller'):
            model_file = os.path.join(self.run_dir, "models", "final_model.pth")
            self.controller.save(model_file)
        
        print(f"💾 最终结果已保存到: {self.run_dir}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='集成ResBand算法的分阶段训练')
    parser.add_argument('--use-resband', action='store_true', default=True,
                       help='启用ResBand算法')
    parser.add_argument('--resband-config', type=str, default=None,
                       help='ResBand配置文件路径')
    parser.add_argument('--output-dir', type=str, default='results/integrated_resband',
                       help='输出目录')
    parser.add_argument('--fast-mode', action='store_true',
                       help='快速模式（减少episodes）')
    
    args = parser.parse_args()
    
    # 解析ResBand配置
    resband_config = None
    if args.use_resband:
        if args.resband_config:
            with open(args.resband_config, 'r', encoding='utf-8') as f:
                resband_config = json.load(f)
        else:
            resband_config = {
                'exploration_coefficient': 2.0,
                'stage_length': 15,
                'reward_weights': (0.6, 0.2, 0.1),
                'adaptive_exploration': True,
                'performance_threshold': 0.5,
                'use_fast_configs': args.fast_mode
            }
    
    # 调整阶段配置（快速模式）
    stage_configs = None
    if args.fast_mode:
        stage_configs = {
            'stage1': {'name': '简单环境', 'episodes': 50, 'obstacle_count': 3, 'complexity': 'simple'},
            'stage2': {'name': '中等环境', 'episodes': 75, 'obstacle_count': 6, 'complexity': 'medium'},
            'stage3': {'name': '复杂环境', 'episodes': 100, 'obstacle_count': 10, 'complexity': 'complex'}
        }
    
    # 创建训练器
    trainer = IntegratedResBandTrainer(
        use_resband=args.use_resband,
        resband_config=resband_config,
        output_dir=args.output_dir,
        stage_configs=stage_configs
    )
    
    # 运行训练
    results = trainer.run_training()
    
    return results

if __name__ == "__main__":
    main()
