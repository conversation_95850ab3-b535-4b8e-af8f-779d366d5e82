"""
TD3网络架构 - 融合版本
基于简化ver1的TD3架构，适配巡飞弹的状态和动作空间
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import random
from collections import deque

class Actor(nn.<PERSON>):
    """Actor网络"""
    
    def __init__(self, state_dim, action_dim, max_action, hidden_dim=256):
        super(Actor, self).__init__()
        self.max_action = max_action
        
        self.l1 = nn.Linear(state_dim, hidden_dim)
        self.l2 = nn.Linear(hidden_dim, hidden_dim)
        self.l3 = nn.Linear(hidden_dim, action_dim)
        
    def forward(self, state):
        x = F.relu(self.l1(state))
        x = F.relu(self.l2(x))
        x = self.max_action * torch.tanh(self.l3(x))
        return x

class Critic(nn.Module):
    """Critic网络"""
    
    def __init__(self, state_dim, action_dim, hidden_dim=256):
        super(Critic, self).__init__()
        
        # Q1网络
        self.l1 = nn.Linear(state_dim + action_dim, hidden_dim)
        self.l2 = nn.Linear(hidden_dim, hidden_dim)
        self.l3 = nn.Linear(hidden_dim, 1)
        
        # Q2网络
        self.l4 = nn.Linear(state_dim + action_dim, hidden_dim)
        self.l5 = nn.Linear(hidden_dim, hidden_dim)
        self.l6 = nn.Linear(hidden_dim, 1)
        
    def forward(self, state, action):
        sa = torch.cat([state, action], 1)
        
        q1 = F.relu(self.l1(sa))
        q1 = F.relu(self.l2(q1))
        q1 = self.l3(q1)
        
        q2 = F.relu(self.l4(sa))
        q2 = F.relu(self.l5(q2))
        q2 = self.l6(q2)
        
        return q1, q2
    
    def Q1(self, state, action):
        sa = torch.cat([state, action], 1)
        
        q1 = F.relu(self.l1(sa))
        q1 = F.relu(self.l2(q1))
        q1 = self.l3(q1)
        
        return q1

class ReplayBuffer:
    """经验回放缓冲区"""

    def __init__(self, capacity=1000000, device=None):
        self.buffer = deque(maxlen=capacity)
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def add(self, state, action, reward, next_state, done, safe_actions=None, goal=None, obstacles=None, selected_idx=0):
        """添加经验"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size):
        """采样批次数据"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return (
            torch.FloatTensor(state).to(self.device),
            torch.FloatTensor(action).to(self.device),
            torch.FloatTensor(reward).unsqueeze(1).to(self.device),
            torch.FloatTensor(next_state).to(self.device),
            torch.FloatTensor(done).unsqueeze(1).to(self.device)
        )
    
    def size(self):
        return len(self.buffer)

class TD3Agent:
    """TD3智能体"""
    
    def __init__(self, state_dim, action_dim, max_action, lr=3e-4, gamma=0.99, tau=0.005,
                 policy_noise=0.2, noise_clip=0.5, policy_freq=2, hidden_dim=256):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        self.actor = Actor(state_dim, action_dim, max_action, hidden_dim).to(self.device)
        self.actor_target = Actor(state_dim, action_dim, max_action, hidden_dim).to(self.device)
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr)
        
        self.critic = Critic(state_dim, action_dim, hidden_dim).to(self.device)
        self.critic_target = Critic(state_dim, action_dim, hidden_dim).to(self.device)
        self.critic_target.load_state_dict(self.critic.state_dict())
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=lr)
        
        self.max_action = max_action
        self.gamma = gamma
        self.tau = tau
        self.policy_noise = policy_noise
        self.noise_clip = noise_clip
        self.policy_freq = policy_freq
        
        self.total_it = 0
        
        # 创建经验回放缓冲区
        self.replay_buffer = ReplayBuffer(device=self.device)
    
    def select_action(self, state, noise=0.1):
        """选择动作"""
        state = torch.FloatTensor(state.reshape(1, -1)).to(self.device)
        action = self.actor(state).cpu().data.numpy().flatten()

        if noise != 0:
            action = action + np.random.normal(0, noise, size=action.shape)

        return action.clip(-self.max_action, self.max_action)

    def select_best_action_from_safe_set(self, state, safe_actions):
        """从安全动作集中选择最优动作

        Args:
            state: 当前状态
            safe_actions: DWA生成的安全动作集合

        Returns:
            最优的安全动作
        """
        if not safe_actions:
            return np.array([0.0, 0.0, 0.0])

        # 如果只有一个安全动作，直接返回
        if len(safe_actions) == 1:
            return safe_actions[0]

        # 将状态转换为tensor
        state_tensor = torch.FloatTensor(state.reshape(1, -1)).to(self.device)

        # 计算每个安全动作的Q值
        best_q_value = float('-inf')
        best_action = safe_actions[0]

        for action in safe_actions:
            action_tensor = torch.FloatTensor(action.reshape(1, -1)).to(self.device)

            # 使用critic网络计算Q值（取Q1和Q2的最小值，更保守）
            q1_value, q2_value = self.critic(state_tensor, action_tensor)
            q_value = torch.min(q1_value, q2_value).cpu().data.numpy()[0, 0]

            if q_value > best_q_value:
                best_q_value = q_value
                best_action = action

        return best_action
    
    def train(self, batch_size=256):
        """训练网络"""
        if self.replay_buffer.size() < batch_size:
            return
        
        self.total_it += 1
        
        # 采样批次数据
        state, action, reward, next_state, done = self.replay_buffer.sample(batch_size)
        
        state = state.to(self.device)
        action = action.to(self.device)
        reward = reward.to(self.device)
        next_state = next_state.to(self.device)
        done = done.to(self.device)
        
        with torch.no_grad():
            # 选择下一个动作
            noise = (torch.randn_like(action) * self.policy_noise).clamp(-self.noise_clip, self.noise_clip)
            next_action = (self.actor_target(next_state) + noise).clamp(-self.max_action, self.max_action)
            
            # 计算目标Q值
            target_Q1, target_Q2 = self.critic_target(next_state, next_action)
            target_Q = torch.min(target_Q1, target_Q2)
            target_Q = reward + (1 - done) * self.gamma * target_Q
        
        # 获取当前Q值
        current_Q1, current_Q2 = self.critic(state, action)
        
        # 计算Critic损失
        critic_loss = F.mse_loss(current_Q1, target_Q) + F.mse_loss(current_Q2, target_Q)
        
        # 保存Critic损失（用于ResBand算法）
        self._last_critic_loss = critic_loss.item()
        
        # 优化Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()
        
        # 延迟策略更新
        if self.total_it % self.policy_freq == 0:
            # 计算Actor损失
            actor_loss = -self.critic.Q1(state, self.actor(state)).mean()
            
            # 优化Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
            
            # 软更新目标网络
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
            
            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
    
    def immediate_update(self, batch_size=64):
        """立即更新（用于在线训练）"""
        if self.replay_buffer.size() >= batch_size:
            self.train(batch_size)
    
    def get_last_critic_loss(self):
        """获取最后一次Critic损失（用于ResBand算法）"""
        if hasattr(self, '_last_critic_loss'):
            return self._last_critic_loss
        return 0.0
    
    def save(self, filename):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
        }, filename)
    
    def load(self, filename):
        """加载模型"""
        checkpoint = torch.load(filename, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])
        
        # 更新目标网络
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.critic_target.load_state_dict(self.critic.state_dict())

# 兼容性别名（与简化ver1保持一致）
class StabilizedTD3Controller(TD3Agent):
    """稳定化TD3控制器（兼容性别名）"""
    
    def __init__(self, config):
        super().__init__(
            state_dim=config['state_dim'],
            action_dim=config['action_dim'],
            max_action=config['max_action'],
            lr=config['lr'],
            gamma=config['gamma'],
            tau=config['tau'],
            policy_noise=config['policy_noise'],
            noise_clip=config['noise_clip'],
            policy_freq=config['policy_freq'],
            hidden_dim=config['hidden_dim']
        )
