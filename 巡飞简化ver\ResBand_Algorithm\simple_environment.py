"""
简化的巡飞弹环境 - ResBand版本
用于ResBand算法的训练和测试
"""

import numpy as np
import random

class SimpleLoiteringMunitionEnvironment:
    """简化的巡飞弹环境"""
    
    def __init__(self, bounds=[2000, 2000, 200], dt=0.1):
        self.bounds = np.array(bounds)
        self.dt = dt

        # 巡飞弹参数（与分阶段训练一致）
        self.V_min = 15.0
        self.V_max = 60.0
        self.a_T_max = 8.0
        self.a_N_max = 39.24
        self.gamma_max = np.pi/3
        
        # 环境状态
        self.state = None
        self.start = None
        self.goal = None
        self.obstacles = []
        self.dynamic_obstacles = []
        
        # 任务状态
        self.step_count = 0
        self.max_steps = 2000
        self.success_threshold = 50.0  # 增大成功阈值，更容易成功
        
    def reset(self, scenario_data=None):
        """重置环境"""
        if scenario_data is None:
            # 生成随机场景
            self._generate_random_scenario()
        else:
            # 使用提供的场景数据
            self._load_scenario(scenario_data)
        
        # 计算朝向目标的初始角度
        direction_to_goal = self.goal - self.start
        initial_psi = np.arctan2(direction_to_goal[1], direction_to_goal[0])  # 水平方向角
        initial_gamma = np.arctan2(direction_to_goal[2],
                                  np.sqrt(direction_to_goal[0]**2 + direction_to_goal[1]**2))  # 垂直方向角

        self.state = np.array([
            self.start[0], self.start[1], self.start[2],  # 位置
            30.0,  # 初始速度（巡航速度）
            initial_gamma,   # 朝向目标的初始航迹倾斜角
            initial_psi      # 朝向目标的初始航向角
        ])
        
        self.step_count = 0
        return self._get_observation()
    
    def step(self, action):
        """执行一步动作"""
        # 解析动作
        a_T, a_N, mu = action
        
        # 更新状态
        x, y, z, V, gamma, psi = self.state
        
        # 运动学更新
        V_new = V + a_T * self.dt
        V_new = np.clip(V_new, self.V_min, self.V_max)
        
        gamma_new = gamma + (a_N / V) * self.dt
        gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
        
        psi_new = psi + (a_N * np.sin(mu) / (V * np.cos(gamma))) * self.dt
        
        x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
        y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
        z_new = z + V * np.sin(gamma) * self.dt
        
        # 更新状态
        self.state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new])
        self.step_count += 1
        
        # 计算奖励
        reward = self._calculate_reward()
        
        # 检查终止条件
        done = self._check_termination()
        
        # 信息
        info = {
            'collision': self._check_collision(),
            'out_of_bounds': self._check_out_of_bounds(),
            'success': self._check_success(),
            'timeout': self.step_count >= self.max_steps
        }
        
        return self._get_observation(), reward, done, info
    
    def _generate_random_scenario(self):
        """生成固定场景（与分阶段训练一致）"""
        # 固定起点和终点（与分阶段训练一致）
        self.start = np.array([200.0, 200.0, 200.0])
        self.goal = np.array([1800.0, 1800.0, 1800.0])
        
        # 生成静态障碍物（简化版本，更少障碍物）
        self.obstacles = []
        num_obstacles = random.randint(3, 5)  # 减少障碍物数量

        # 计算起点到终点的路径
        path_vector = self.goal - self.start
        path_length = np.linalg.norm(path_vector)

        for i in range(num_obstacles):
            # 在路径附近生成障碍物
            ratio = (i + 1) / (num_obstacles + 1)  # 均匀分布在路径上
            base_point = self.start + ratio * path_vector

            # 添加一些随机偏移
            offset = np.array([
                random.uniform(-200, 200),
                random.uniform(-200, 200),
                random.uniform(-100, 100)
            ])
            center = base_point + offset

            # 确保障碍物在边界内
            center = np.clip(center, [300, 300, 50], [1700, 1700, 150])

            radius = random.uniform(50, 100)  # 适中的障碍物大小
            self.obstacles.append({
                'center': center,
                'radius': radius
            })
        
        # 生成动态障碍物（简化版本）
        self.dynamic_obstacles = []
        num_dynamic = random.randint(0, 2)  # 减少动态障碍物数量

        for _ in range(num_dynamic):
            center = np.array([
                random.uniform(300, 700),
                random.uniform(300, 700),
                random.uniform(50, 250)
            ])
            radius = random.uniform(20, 60)
            velocity = np.array([
                random.uniform(-5, 5),
                random.uniform(-5, 5),
                random.uniform(-2, 2)
            ])
            self.dynamic_obstacles.append({
                'center': center,
                'radius': radius,
                'velocity': velocity,
                'motion_type': random.choice(['linear', 'circular', 'oscillating'])
            })
    
    def _load_scenario(self, scenario_data):
        """加载场景数据"""
        self.start = np.array(scenario_data['start'])
        self.goal = np.array(scenario_data['goal'])
        self.obstacles = scenario_data['obstacles']
        self.dynamic_obstacles = scenario_data['dynamic_obstacles']
    
    def save_scenario(self):
        """保存场景数据"""
        return {
            'start': self.start.tolist(),
            'goal': self.goal.tolist(),
            'obstacles': self.obstacles,
            'dynamic_obstacles': self.dynamic_obstacles
        }
    
    def _calculate_reward(self):
        """计算奖励"""
        x, y, z, V, gamma, psi = self.state
        current_pos = np.array([x, y, z])
        
        # 距离奖励
        distance_to_goal = np.linalg.norm(current_pos - self.goal)
        distance_reward = -distance_to_goal / 1000.0  # 归一化
        
        # 成功奖励
        if distance_to_goal < self.success_threshold:
            success_reward = 100.0
        else:
            success_reward = 0.0
        
        # 碰撞惩罚
        if self._check_collision():
            collision_penalty = -50.0
        else:
            collision_penalty = 0.0
        
        # 边界惩罚
        if self._check_out_of_bounds():
            boundary_penalty = -30.0
        else:
            boundary_penalty = 0.0
        
        # 速度奖励（鼓励巡航速度）
        cruise_speed = 25.0
        speed_reward = -abs(V - cruise_speed) / cruise_speed
        
        # 总奖励
        total_reward = (distance_reward + success_reward + collision_penalty + 
                       boundary_penalty + speed_reward)
        
        return total_reward
    
    def _check_collision(self):
        """检查碰撞"""
        x, y, z, V, gamma, psi = self.state
        current_pos = np.array([x, y, z])
        
        # 检查静态障碍物
        for obs in self.obstacles:
            dist = np.linalg.norm(current_pos - obs['center'])
            if dist < obs['radius'] + 5.0:  # 安全距离
                return True
        
        # 检查动态障碍物
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(current_pos - obs['center'])
            if dist < obs['radius'] + 5.0:
                return True
        
        return False
    
    def _check_out_of_bounds(self):
        """检查是否超出边界"""
        x, y, z, V, gamma, psi = self.state
        
        if (x < 0 or x > self.bounds[0] or
            y < 0 or y > self.bounds[1] or
            z < 0 or z > self.bounds[2]):
            return True
        
        return False
    
    def _check_success(self):
        """检查是否成功到达目标"""
        x, y, z, V, gamma, psi = self.state
        current_pos = np.array([x, y, z])
        
        distance_to_goal = np.linalg.norm(current_pos - self.goal)
        return distance_to_goal < self.success_threshold
    
    def _check_termination(self):
        """检查是否终止"""
        return (self._check_collision() or 
                self._check_out_of_bounds() or 
                self._check_success() or 
                self.step_count >= self.max_steps)
    
    def _get_observation(self):
        """获取15维观测向量（与分阶段训练一致）"""
        pos = self.state[:3]
        V, gamma, psi = self.state[3:6]

        # 目标方向
        goal_direction = self.goal - pos
        goal_dist = np.linalg.norm(goal_direction)
        if goal_dist > 0:
            goal_direction = goal_direction / goal_dist

        # 最近障碍物距离
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        # 构建15维观测向量
        observation = np.array([
            pos[0] / self.bounds[0],  # 归一化位置
            pos[1] / self.bounds[1],
            pos[2] / self.bounds[2],
            V / self.V_max,           # 归一化速度
            gamma / self.gamma_max,   # 归一化航迹倾斜角
            psi / (2 * np.pi),        # 归一化偏航角
            goal_direction[0],        # 目标方向
            goal_direction[1],
            goal_direction[2],
            goal_dist / 100.0,        # 归一化目标距离
            min_obs_dist / 100.0,     # 归一化最近障碍物距离
            len(self.obstacles) / 10.0,  # 静态障碍物数量
            len(self.dynamic_obstacles) / 10.0,  # 动态障碍物数量
            1.0 if self.dynamic_obstacles else 0.0,  # 动态标志
            pos[2] / self.bounds[2]   # 地形高度
        ], dtype=np.float64)

        return observation

    def get_observation(self):
        """获取观察（状态）"""
        return self._get_observation()

    def get_goal(self):
        """获取目标位置"""
        return self.goal.copy()

    def get_obstacles(self):
        """获取所有障碍物"""
        all_obstacles = self.obstacles.copy()
        all_obstacles.extend(self.dynamic_obstacles)
        return all_obstacles
