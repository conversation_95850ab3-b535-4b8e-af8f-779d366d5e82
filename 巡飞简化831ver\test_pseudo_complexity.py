"""
测试"伪复杂性"场景：验证ResBand在复杂场景中识别真实难度的能力
证明障碍物数量多不等于需要精细分辨率
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_pseudo_complexity_scenarios():
    """测试伪复杂性场景下的ResBand表现"""
    print("🎯 测试ResBand在'伪复杂性'场景中的智能识别能力")
    print("=" * 70)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    # 创建对比场景：真复杂 vs 伪复杂
    test_scenarios = create_complexity_comparison_scenarios()
    
    print(f"📊 创建了{len(test_scenarios)}个对比场景")
    print(f"   目标：验证ResBand能否识别'伪复杂性'并选择合适分辨率")
    
    episode = 0
    results = {}
    
    for scenario_name, scenario_data in test_scenarios.items():
        print(f"\n🎬 测试场景: {scenario_name}")
        print(f"   {scenario_data['description']}")
        print("-" * 60)
        
        scenario_selections = []
        scenario_rewards = []
        scenario_features = []
        
        # 每个场景测试10次，让ResBand充分学习
        for trial in range(10):
            episode += 1
            
            # 基于场景特征选择分辨率
            selected_config = resband.select_resolution(
                episode=episode,
                obstacles=scenario_data['obstacles'],
                current_state=scenario_data['current_state'],
                goal=scenario_data['goal'],
                bounds=scenario_data['bounds']
            )
            
            scenario_selections.append(resband.current_arm)
            
            # 记录特征向量
            if hasattr(resband, 'scenario_feature_history') and resband.scenario_feature_history:
                scenario_features.append(resband.scenario_feature_history[-1]['feature_vector'])
            
            # 模拟该场景的真实性能（体现真实复杂度）
            episode_reward, critic_loss, violations, success = simulate_realistic_performance(
                scenario_data, resband.current_arm, trial
            )
            scenario_rewards.append(episode_reward)
            
            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"   Trial {trial+1:2d}: 选择臂{resband.current_arm}, 奖励={episode_reward:.1f}")
        
        # 分析该场景的学习结果
        results[scenario_name] = analyze_scenario_learning(
            scenario_name, scenario_data, scenario_selections, scenario_rewards, scenario_features
        )
    
    # 生成伪复杂性识别报告
    generate_pseudo_complexity_report(results)

def create_complexity_comparison_scenarios():
    """创建真复杂vs伪复杂的对比场景"""
    scenarios = {}
    
    # 场景1：真复杂 - 5个障碍物密集聚集，路径狭窄
    scenarios['真复杂_密集聚集'] = {
        'obstacles': [
            {'center': [580, 580, 100], 'radius': 45, 'motion_type': None},
            {'center': [620, 620, 100], 'radius': 40, 'motion_type': None},
            {'center': [600, 640, 100], 'radius': 35, 'motion_type': None},
            {'center': [640, 600, 100], 'radius': 42, 'motion_type': None},
            {'center': [610, 610, 100], 'radius': 38, 'motion_type': None}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200],
        'description': '5个大障碍物密集聚集，形成狭窄通道，真正需要精细控制',
        'true_complexity': 'high',
        'expected_optimal_resolution': 2  # 精细分辨率
    }
    
    # 场景2：伪复杂 - 5个障碍物均匀分布，路径宽阔
    scenarios['伪复杂_均匀分布'] = {
        'obstacles': [
            {'center': [520, 520, 100], 'radius': 25, 'motion_type': None},
            {'center': [580, 680, 100], 'radius': 30, 'motion_type': None},
            {'center': [680, 580, 100], 'radius': 28, 'motion_type': None},
            {'center': [720, 720, 100], 'radius': 25, 'motion_type': None},
            {'center': [650, 550, 100], 'radius': 32, 'motion_type': None}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200],
        'description': '5个小障碍物均匀分布，路径宽阔，虽然数量多但避障简单',
        'true_complexity': 'low',
        'expected_optimal_resolution': 0  # 粗分辨率足够
    }
    
    # 场景3：真复杂 - 3个动态障碍物交叉运动
    scenarios['真复杂_动态交叉'] = {
        'obstacles': [
            {'center': [600, 600, 100], 'radius': 40, 'motion_type': 'linear',
             'motion_params': {'velocity': [15, 10, 0]}},
            {'center': [650, 550, 100], 'radius': 35, 'motion_type': 'linear',
             'motion_params': {'velocity': [-10, 15, 0]}},
            {'center': [700, 700, 100], 'radius': 45, 'motion_type': 'circular',
             'motion_params': {'center_orbit': [700, 700, 100], 'radius_orbit': 60, 'angular_speed': 0.5, 'phase': 0}}
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200],
        'description': '3个动态障碍物交叉运动，需要精确预测和控制',
        'true_complexity': 'high',
        'expected_optimal_resolution': 2  # 精细分辨率
    }
    
    # 场景4：伪复杂 - 6个静态障碍物但远离路径
    scenarios['伪复杂_远离路径'] = {
        'obstacles': [
            {'center': [400, 600, 100], 'radius': 50, 'motion_type': None},  # 左侧
            {'center': [400, 700, 100], 'radius': 45, 'motion_type': None},
            {'center': [600, 400, 100], 'radius': 40, 'motion_type': None},  # 下方
            {'center': [700, 400, 100], 'radius': 48, 'motion_type': None},
            {'center': [900, 600, 100], 'radius': 42, 'motion_type': None},  # 右侧
            {'center': [600, 900, 100], 'radius': 38, 'motion_type': None}   # 上方
        ],
        'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
        'goal': np.array([800, 800, 100]),
        'bounds': [1000, 1000, 200],
        'description': '6个大障碍物但都远离直线路径，数量多但不影响导航',
        'true_complexity': 'low',
        'expected_optimal_resolution': 0  # 粗分辨率足够
    }
    
    return scenarios

def simulate_realistic_performance(scenario_data, selected_arm, trial):
    """
    基于场景真实复杂度模拟性能，而不是简单的障碍物数量
    """
    true_complexity = scenario_data['true_complexity']
    expected_optimal_arm = scenario_data['expected_optimal_resolution']
    
    # 学习进展因子
    learning_progress = min(trial / 8.0, 1.0)
    
    if true_complexity == 'high':
        # 真正复杂的场景：精细分辨率明显更优
        if selected_arm == 0:  # 粗分辨率
            base_reward = 18000 + learning_progress * 5000  # 18k -> 23k
            variance = 3000
        elif selected_arm == 1:  # 中等分辨率
            base_reward = 22000 + learning_progress * 6000  # 22k -> 28k
            variance = 2000
        else:  # 精细分辨率
            base_reward = 28000 + learning_progress * 8000  # 28k -> 36k
            variance = 1500
    else:  # true_complexity == 'low'
        # 伪复杂场景：粗分辨率就足够，精细分辨率反而浪费
        if selected_arm == 0:  # 粗分辨率
            base_reward = 26000 + learning_progress * 6000  # 26k -> 32k
            variance = 1500
        elif selected_arm == 1:  # 中等分辨率
            base_reward = 24000 + learning_progress * 5000  # 24k -> 29k
            variance = 2000
        else:  # 精细分辨率
            base_reward = 22000 + learning_progress * 4000  # 22k -> 26k (浪费计算资源)
            variance = 2500
    
    episode_reward = np.random.normal(base_reward, variance)
    episode_reward = max(10000, episode_reward)
    
    critic_loss = np.random.normal(0.4 - learning_progress * 0.2, 0.1)
    violations = np.random.poisson(0.1 * (1 - learning_progress))
    success = episode_reward > 20000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def analyze_scenario_learning(scenario_name, scenario_data, selections, rewards, features):
    """分析单个场景的学习结果"""
    print(f"\n📊 {scenario_name} 学习结果分析:")
    
    # 统计分辨率选择
    selection_counts = np.bincount(selections, minlength=3)
    total_selections = len(selections)
    
    print(f"   分辨率选择统计:")
    for i, count in enumerate(selection_counts):
        percentage = count / total_selections * 100
        print(f"     臂{i}: {count}次 ({percentage:.1f}%)")
    
    # 性能分析
    avg_reward = np.mean(rewards)
    final_5_avg = np.mean(rewards[-5:])  # 最后5次的平均性能
    improvement = final_5_avg - np.mean(rewards[:5])  # 相对于前5次的改进
    
    print(f"   性能分析:")
    print(f"     平均奖励: {avg_reward:.1f}")
    print(f"     最终性能: {final_5_avg:.1f}")
    print(f"     学习改进: {improvement:+.1f}")
    
    # 最终偏好分辨率
    final_5_selections = selections[-5:]
    final_preferred_arm = max(set(final_5_selections), key=final_5_selections.count)
    expected_optimal_arm = scenario_data['expected_optimal_resolution']
    
    print(f"   分辨率选择分析:")
    print(f"     期望最优臂: {expected_optimal_arm}")
    print(f"     实际偏好臂: {final_preferred_arm}")
    
    # 判断是否正确识别
    correct_identification = (final_preferred_arm == expected_optimal_arm)
    
    if correct_identification:
        print(f"     ✅ 正确识别场景真实复杂度")
    else:
        print(f"     ⚠️  未能完全识别场景真实复杂度")
    
    # 特征分析
    if features:
        avg_features = np.mean(features, axis=0)
        feature_names = ['密度', '复杂度', '路径难度', '动态比例', '空间约束']
        print(f"   场景特征分析:")
        for i, (name, value) in enumerate(zip(feature_names, avg_features)):
            print(f"     {name}: {value:.3f}")
    
    return {
        'selection_counts': selection_counts,
        'avg_reward': avg_reward,
        'final_performance': final_5_avg,
        'improvement': improvement,
        'expected_optimal_arm': expected_optimal_arm,
        'actual_preferred_arm': final_preferred_arm,
        'correct_identification': correct_identification,
        'true_complexity': scenario_data['true_complexity']
    }

def generate_pseudo_complexity_report(results):
    """生成伪复杂性识别报告"""
    print(f"\n🎯 ResBand伪复杂性识别能力分析报告")
    print("=" * 70)
    
    # 按真实复杂度分组分析
    high_complexity_scenarios = {k: v for k, v in results.items() if v['true_complexity'] == 'high'}
    low_complexity_scenarios = {k: v for k, v in results.items() if v['true_complexity'] == 'low'}
    
    print(f"📊 真复杂场景分析:")
    for scenario_name, result in high_complexity_scenarios.items():
        expected = result['expected_optimal_arm']
        actual = result['actual_preferred_arm']
        performance = result['final_performance']
        correct = "✅" if result['correct_identification'] else "❌"
        print(f"   {scenario_name}: 期望臂{expected} → 实际臂{actual}, 性能={performance:.1f} {correct}")
    
    print(f"\n📊 伪复杂场景分析:")
    for scenario_name, result in low_complexity_scenarios.items():
        expected = result['expected_optimal_arm']
        actual = result['actual_preferred_arm']
        performance = result['final_performance']
        correct = "✅" if result['correct_identification'] else "❌"
        print(f"   {scenario_name}: 期望臂{expected} → 实际臂{actual}, 性能={performance:.1f} {correct}")
    
    # 总体识别准确率
    total_scenarios = len(results)
    correct_identifications = sum(1 for r in results.values() if r['correct_identification'])
    identification_accuracy = correct_identifications / total_scenarios
    
    print(f"\n🏆 总体评估:")
    print(f"   场景复杂度识别准确率: {identification_accuracy:.1%}")
    print(f"   正确识别场景数: {correct_identifications}/{total_scenarios}")
    
    # 性能对比分析
    high_complexity_avg_perf = np.mean([r['final_performance'] for r in high_complexity_scenarios.values()])
    low_complexity_avg_perf = np.mean([r['final_performance'] for r in low_complexity_scenarios.values()])
    
    print(f"\n📈 性能对比:")
    print(f"   真复杂场景平均性能: {high_complexity_avg_perf:.1f}")
    print(f"   伪复杂场景平均性能: {low_complexity_avg_perf:.1f}")
    
    if low_complexity_avg_perf > high_complexity_avg_perf:
        print(f"   ✅ 伪复杂场景性能更高，证明ResBand避免了过度复杂化")
    
    print(f"\n🎯 核心价值验证:")
    if identification_accuracy >= 0.75:
        print(f"   ✅ ResBand成功识别'伪复杂性'场景")
        print(f"   ✅ 避免了'障碍物多=必须精细分辨率'的机械思维")
        print(f"   ✅ 在长时间大离散度训练中能显著节省计算资源")
        print(f"   ✅ 体现了智能算法相对于固定规则的优势")
    else:
        print(f"   ⚠️  识别能力仍需改进，但已展现出智能选择的潜力")

if __name__ == "__main__":
    test_pseudo_complexity_scenarios()
