\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{toc}{\contentsline {section}{\numberline {1}总体分析}{1}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}当前状态分析}{1}{subsection.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}修改目标}{1}{subsection.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}标题与摘要修改}{1}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}标题}{1}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}摘要}{1}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}引言部分修改}{1}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}研究背景}{1}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}相关工作}{2}{subsection.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}本文贡献}{2}{subsection.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4}技术框架修改}{2}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}第一层：DWA-RL分层架构}{2}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}第二层：ResBand算法}{2}{subsection.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3}第三层：MLACF框架}{2}{subsection.4.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}伪代码补充}{2}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}整体框架伪代码}{3}{subsection.5.1}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces 三层自适应控制框架}}{3}{algorithm.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}ResBand算法伪代码}{3}{subsection.5.2}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces ResBand分辨率选择算法}}{3}{algorithm.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3}MLACF元学习伪代码}{3}{subsection.5.3}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces MLACF场景适应算法}}{3}{algorithm.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}实验设计与结果分析}{3}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}实验设计}{3}{subsection.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}实验设置}{3}{subsection.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}实验结果}{3}{subsection.6.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.3.1}实验1：主动安全保障验证}{3}{subsubsection.6.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.3.2}实验2：ResBand算法验证}{3}{subsubsection.6.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.3.3}实验3：MLACF框架验证}{4}{subsubsection.6.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.3.4}实验4：综合性能验证}{4}{subsubsection.6.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7}具体修改清单}{4}{section.7}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.1}高优先级修改}{4}{subsection.7.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.2}中优先级修改}{4}{subsection.7.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.3}低优先级修改}{4}{subsection.7.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8}代码实现状态}{4}{section.8}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.1}已实现功能}{4}{subsection.8.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.2}需要额外实现}{4}{subsection.8.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {9}实施建议}{5}{section.9}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.1}第一步：数据整合}{5}{subsection.9.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.2}第二步：代码实验}{5}{subsection.9.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.3}第三步：内容完善}{5}{subsection.9.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {10}质量检查清单}{5}{section.10}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.1}技术内容}{5}{subsection.10.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.2}实验验证}{5}{subsection.10.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.3}论文结构}{5}{subsection.10.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {11}总结}{5}{section.11}\protected@file@percent }
\gdef \@abspage@last{5}
