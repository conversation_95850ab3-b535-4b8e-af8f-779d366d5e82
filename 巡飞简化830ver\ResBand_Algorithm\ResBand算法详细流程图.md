# ResBand算法详细流程图

## 1. 系统架构图

```mermaid
graph TB
    subgraph "ResBand算法层"
        A[ResBand算法] --> B[UCB选择器]
        B --> C[性能评估器]
        C --> D[分辨率配置管理]
    end
    
    subgraph "DWA控制层"
        E[DWA控制器] --> F[轨迹预测]
        F --> G[安全动作生成]
        G --> H[分辨率更新接口]
    end
    
    subgraph "TD3强化学习层"
        I[TD3 Actor] --> J[动作选择]
        K[TD3 Critic] --> L[价值评估]
        M[经验回放] --> N[网络更新]
    end
    
    subgraph "环境层"
        O[巡飞弹环境] --> P[状态更新]
        P --> Q[奖励计算]
        Q --> R[约束检查]
    end
    
    A --> D
    D --> H
    H --> E
    E --> G
    G --> J
    J --> O
    O --> P
    P --> Q
    Q --> L
    L --> N
    N --> C
    C --> A
```

## 2. 训练时序图

```mermaid
sequenceDiagram
    participant ResBand as ResBand算法
    participant DWA as DWA控制器
    participant TD3 as TD3网络
    participant Env as 环境
    
    Note over ResBand,Env: Episode开始
    ResBand->>ResBand: select_resolution(episode_num)
    ResBand->>DWA: update_resolution(config)
    DWA->>DWA: 更新分辨率参数
    
    loop 每个训练步骤
        Env->>DWA: 当前状态
        DWA->>DWA: generate_safe_control_set()
        DWA->>TD3: 安全动作集
        TD3->>TD3: select_best_action_from_safe_set()
        TD3->>Env: 执行动作
        Env->>Env: 环境步进
        Env->>TD3: 奖励、下一状态
        TD3->>TD3: train_step() - 更新网络
    end
    
    Note over ResBand,Env: Episode结束
    Env->>ResBand: episode_reward, violations
    TD3->>ResBand: critic_loss
    ResBand->>ResBand: update_performance()
    ResBand->>ResBand: 更新UCB统计
```

## 3. 分辨率选择决策树

```mermaid
graph TD
    A[开始分辨率选择] --> B{episode ≤ K?}
    B -->|是| C[选择配置c_episode]
    B -->|否| D[计算所有配置的UCB值]
    
    D --> E[配置1: Q1 + α√ln(t)/N1]
    D --> F[配置2: Q2 + α√ln(t)/N2]
    D --> G[配置3: Q3 + α√ln(t)/N3]
    D --> H[配置4: Q4 + α√ln(t)/N4]
    
    E --> I[比较UCB值]
    F --> I
    G --> I
    H --> I
    
    I --> J[选择UCB最大的配置]
    C --> K[更新DWA分辨率]
    J --> K
    K --> L[返回选择的配置]
```

## 4. 性能更新详细流程

```mermaid
graph TD
    A[收集Episode数据] --> B[episode_reward]
    A --> C[critic_loss]
    A --> D[violation_count]
    
    B --> E[计算奖励差异]
    E --> F[ΔR_m = episode_reward - baseline_reward]
    
    C --> G[计算损失下降]
    G --> H[ΔL_m_critic = previous_loss - current_loss]
    
    D --> I[统计约束违反]
    
    F --> J[应用权重α]
    H --> K[应用权重β]
    I --> L[应用权重γ]
    
    J --> M[计算综合奖励]
    K --> M
    L --> M
    
    M --> N[R_m = α·ΔR_m - β·ΔL_m_critic - γ·violations]
    
    N --> O[更新UCB统计]
    O --> P[N(c_t) = N(c_t) + 1]
    P --> Q[Q(c_t) = Q(c_t) + (R_m - Q(c_t))/N(c_t)]
    
    Q --> R[更新基线值]
    R --> S[baseline_reward = episode_reward]
    S --> T[baseline_critic_loss = critic_loss]
```

## 5. 分阶段训练中的ResBand集成

```mermaid
graph TD
    A[分阶段训练开始] --> B[阶段1: 简单环境]
    B --> C[阶段2: 复杂环境]
    C --> D[阶段3: 动态环境]
    
    subgraph "每个阶段内部"
        E[随机场景探索] --> F[固定场景训练]
        F --> G[阶段评估]
    end
    
    subgraph "ResBand在每个阶段"
        H[初始化ResBand] --> I[每个episode选择分辨率]
        I --> J[更新性能指标]
        J --> K[保存阶段结果]
    end
    
    B --> H
    C --> H
    D --> H
    
    E --> I
    F --> I
    G --> K
```

## 6. 算法收敛过程

```mermaid
graph LR
    A[初始探索阶段] --> B[UCB探索阶段]
    B --> C[收敛阶段]
    C --> D[稳定选择阶段]
    
    subgraph "探索-利用平衡"
        E[高探索] --> F[平衡探索利用]
        F --> G[高利用]
    end
    
    A --> E
    B --> F
    C --> G
    D --> G
```

## 7. 错误处理和异常情况

```mermaid
graph TD
    A[算法执行] --> B{检查配置有效性}
    B -->|无效| C[使用默认配置]
    B -->|有效| D[继续执行]
    
    D --> E{检查性能数据}
    E -->|异常| F[使用历史基线]
    E -->|正常| G[正常更新]
    
    G --> H{检查UCB计算}
    H -->|除零错误| I[设置默认UCB值]
    H -->|正常| J[正常选择]
    
    C --> K[记录错误日志]
    F --> K
    I --> K
    J --> L[继续训练]
    K --> L
```

## 8. 实验对比框架

```mermaid
graph TD
    A[实验开始] --> B[基线1: 固定高分辨率]
    A --> C[基线2: 固定低分辨率]
    A --> D[基线3: 启发式调度]
    A --> E[实验组: ResBand算法]
    
    B --> F[训练200 episodes]
    C --> F
    D --> F
    E --> F
    
    F --> G[性能评估]
    G --> H[成功率对比]
    G --> I[平均奖励对比]
    G --> J[计算效率对比]
    G --> K[约束违反对比]
    
    H --> L[生成对比图表]
    I --> L
    J --> L
    K --> L
    
    L --> M[统计分析]
    M --> N[结论总结]
```

## 9. 代码实现映射

| 流程图组件 | 代码文件 | 主要函数 |
|-----------|---------|---------|
| ResBand算法 | `resolution_bandit.py` | `ResolutionBandit.select_resolution()` |
| DWA控制器 | `loitering_munition_dwa.py` | `LoiteringMunitionDWA.update_resolution()` |
| TD3网络 | `simple_td3.py` | `StabilizedTD3Controller.train_step()` |
| 训练器 | `resband_trainer.py` | `ResBandTrainer.train()` |
| 环境 | `simple_environment.py` | `SimpleLoiteringMunitionEnvironment.step()` |

## 10. 关键性能指标

### 10.1 算法性能指标
- **收敛速度**: UCB值收敛到最优配置的episode数
- **选择稳定性**: 最终阶段中分辨率选择的稳定性
- **性能提升**: 相对于固定分辨率的性能提升百分比

### 10.2 系统性能指标
- **训练成功率**: 成功完成任务的episode比例
- **平均奖励**: 所有episode的平均累积奖励
- **计算效率**: 单位时间内的训练进度
- **约束违反**: 违反安全约束的次数

---

*本流程图详细展示了ResBand算法在DWA-RL框架中的完整工作流程，可用于论文中的算法描述和系统架构说明。*
