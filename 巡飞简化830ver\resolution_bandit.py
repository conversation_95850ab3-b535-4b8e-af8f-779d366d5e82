"""
Resolution Bandit (ResBand) 算法实现
基于多臂老虎机的自适应分辨率调度算法
"""

import numpy as np
import json
import os
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class ResolutionConfig:
    """分辨率配置类"""
    
    def __init__(self, name: str, a_T_resolution: float, a_N_resolution: float, 
                 mu_resolution: float, description: str = ""):
        self.name = name
        self.a_T_resolution = a_T_resolution
        self.a_N_resolution = a_N_resolution
        self.mu_resolution = mu_resolution
        self.description = description
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "a_T_resolution": self.a_T_resolution,
            "a_N_resolution": self.a_N_resolution,
            "mu_resolution": self.mu_resolution,
            "description": self.description
        }
    
    def __str__(self) -> str:
        return f"{self.name}: a_T={self.a_T_resolution}, a_N={self.a_N_resolution}, μ={self.mu_resolution}"

class ResolutionBandit:
    """
    Resolution Bandit (ResBand) 算法
    
    基于多臂老虎机的自适应分辨率调度算法，用于优化DWA层的控制输入离散化分辨率
    """
    
    def __init__(self, 
                 configs: List[ResolutionConfig],
                 exploration_coefficient: float = 2.0,
                 stage_length: int = 20,
                 reward_weights: Tuple[float, float, float] = (0.7, 0.2, 0.1),
                 output_dir: str = "results"):
        """
        初始化ResBand算法
        
        Args:
            configs: 分辨率配置列表
            exploration_coefficient: UCB探索系数
            stage_length: 每个阶段的episode数量
            reward_weights: 回报函数权重 (α, β, γ)
            output_dir: 输出目录
        """
        self.configs = configs
        self.K = len(configs)  # 臂的数量
        self.c = exploration_coefficient
        self.L = stage_length
        self.alpha, self.beta, self.gamma = reward_weights
        
        # 老虎机状态
        self.Q = np.zeros(self.K)  # 每个臂的平均回报
        self.N = np.zeros(self.K, dtype=int)  # 每个臂被选择的次数
        self.M = 0  # 总训练阶段数

        # 场景感知的分辨率自适应机制
        self.current_arm = 0  # 从臂0（最粗分辨率）开始
        self.current_scenario_stage = "simple_static"  # 当前场景阶段
        self.training_progress = "early"  # 训练进展：early, middle, late

        # 场景阶段与分辨率候选集的映射（为ResBand提供学习空间）
        self.scenario_resolution_requirements = {
            "simple_static": {"candidate_resolutions": [0, 1], "safety_constraint": None},
            "complex_static": {"candidate_resolutions": [0, 1, 2], "safety_constraint": None},
            "complex_dynamic": {"candidate_resolutions": [1, 2], "safety_constraint": "min_resolution_1"}  # 动态场景至少需要中等分辨率
        }

        # 训练进展跟踪
        self.episode_count = 0
        self.recent_rewards = []  # 最近的奖励历史
        self.recent_success_rates = []  # 最近的成功率历史
        self.performance_window = 10  # 性能评估窗口
        self.stage_performance_history = {}  # 各场景阶段的性能历史
        
        # 历史记录
        self.arm_selection_history = []  # 臂选择历史
        self.reward_history = []  # 回报历史
        self.stage_performance = {}  # 每个阶段的性能记录
        
        # 输出目录
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🎰 ResBand算法初始化完成")
        print(f"   臂数量: {self.K}")
        print(f"   探索系数: {self.c}")
        print(f"   阶段长度: {self.L} episodes (自适应更新)")
        print(f"   回报权重: α={self.alpha}, β={self.beta}, γ={self.gamma}")
        print(f"   场景感知自适应: 简单静态→复杂静态→复杂动态")
        print(f"   分辨率配置:")
        for i, config in enumerate(self.configs):
            print(f"     Arm {i}: {config}")
        print(f"   初始场景阶段: {self.current_scenario_stage}")
        print(f"   初始训练进展: {self.training_progress}")
        print(f"   初始选择: {self.configs[self.current_arm].name}")
    
    def select_resolution(self, episode: int, scenario_stage: str = None) -> ResolutionConfig:
        """
        基于场景阶段和训练进展自适应选择分辨率配置

        Args:
            episode: 当前episode编号
            scenario_stage: 当前场景阶段 ("simple_static", "complex_static", "complex_dynamic")

        Returns:
            选择的分辨率配置
        """
        self.episode_count = episode

        # 更新场景阶段
        if scenario_stage and scenario_stage != self.current_scenario_stage:
            print(f"🎬 场景阶段转换: {self.current_scenario_stage} → {scenario_stage}")
            self.current_scenario_stage = scenario_stage

        # 检测训练进展
        new_progress = self._detect_training_progress()
        if new_progress != self.training_progress:
            print(f"📈 训练进展更新: {self.training_progress} → {new_progress}")
            self.training_progress = new_progress

        # 自适应更新策略：根据场景复杂度决定更新频率
        if self.current_scenario_stage == "simple_static":
            update_interval = 10  # 简单场景更新较慢
        elif self.current_scenario_stage == "complex_static":
            update_interval = 6   # 复杂静态场景适中更新
        else:  # complex_dynamic
            update_interval = 4   # 复杂动态场景更频繁更新

        # 检查是否需要更新臂选择
        if episode > 0 and episode % update_interval == 0:
            self._update_arm_selection()

        # 记录选择历史
        self.arm_selection_history.append({
            'episode': episode,
            'arm': self.current_arm,
            'config': self.configs[self.current_arm].to_dict(),
            'scenario_stage': self.current_scenario_stage,
            'training_progress': self.training_progress
        })

        return self.configs[self.current_arm]

    def _detect_training_progress(self) -> str:
        """
        基于训练表现检测训练进展

        Returns:
            训练进展：'early', 'middle', 'late'
        """
        if len(self.recent_success_rates) < 5:
            return "early"

        current_success_rate = np.mean(self.recent_success_rates[-5:])

        # 基于成功率判断训练进展
        if current_success_rate < 0.3:
            return "early"
        elif current_success_rate < 0.7:
            return "middle"
        else:
            return "late"

    def _select_resolution_by_scenario_and_progress(self) -> int:
        """
        基于场景阶段在候选分辨率集合内使用ResBand算法选择最优分辨率

        Returns:
            选择的臂编号
        """
        # 获取当前场景的候选分辨率集合
        requirements = self.scenario_resolution_requirements[self.current_scenario_stage]
        candidate_arms = requirements["candidate_resolutions"]

        # 确保候选臂在有效范围内
        candidate_arms = [arm for arm in candidate_arms if 0 <= arm < self.K]

        if len(candidate_arms) == 1:
            # 只有一个候选分辨率，直接返回
            return candidate_arms[0]

        # 在候选集合内使用完整的ResBand算法
        # 根据训练进展调整探索策略
        if self.training_progress == "early":
            # 训练早期：更多探索，较高的探索系数
            exploration_bonus = 1.5
        elif self.training_progress == "middle":
            # 训练中期：平衡探索与利用
            exploration_bonus = 1.0
        else:  # late
            # 训练后期：更多利用，较低的探索系数
            exploration_bonus = 0.7

        # 计算候选臂的UCB值
        best_arm = candidate_arms[0]
        best_ucb = -np.inf

        for arm in candidate_arms:
            if self.N[arm] == 0:
                # 未尝试过的臂，给予最高优先级
                ucb_value = np.inf
            else:
                # 标准UCB公式，但使用调整后的探索系数
                ucb_value = (self.Q[arm] +
                           exploration_bonus * self.c * np.sqrt(np.log(self.M) / self.N[arm]))

            if ucb_value > best_ucb:
                best_ucb = ucb_value
                best_arm = arm

        return best_arm

    def _detect_training_stage(self) -> str:
        """
        基于训练进展检测当前训练阶段

        Returns:
            训练阶段：'exploration', 'refinement', 'optimization'
        """
        if len(self.recent_success_rates) < 5:
            return "exploration"  # 数据不足，保持探索阶段

        current_success_rate = np.mean(self.recent_success_rates[-5:])

        # 探索阶段 → 精化阶段
        if (self.training_stage == "exploration" and
            self.episode_count >= self.stage_transition_criteria["exploration_to_refinement"]["min_episodes"] and
            current_success_rate >= self.stage_transition_criteria["exploration_to_refinement"]["min_success_rate"]):
            return "refinement"

        # 精化阶段 → 优化阶段
        if (self.training_stage == "refinement" and
            self.episode_count >= self.stage_transition_criteria["refinement_to_optimization"]["min_episodes"] and
            current_success_rate >= self.stage_transition_criteria["refinement_to_optimization"]["min_success_rate"]):

            # 检查性能收敛
            if len(self.recent_rewards) >= self.performance_window:
                recent_rewards = self.recent_rewards[-self.performance_window:]
                reward_std = np.std(recent_rewards)
                reward_mean = np.mean(recent_rewards)
                cv = reward_std / (abs(reward_mean) + 1e-6)  # 变异系数

                if cv < self.stage_transition_criteria["refinement_to_optimization"]["convergence_threshold"]:
                    return "optimization"

        return self.training_stage  # 保持当前阶段

    def _select_resolution_by_stage(self) -> int:
        """
        根据训练阶段选择分辨率

        Returns:
            选择的臂编号
        """
        if self.training_stage == "exploration":
            # 探索阶段：优先使用粗分辨率，快速建立基本策略
            if self.N[0] < 5:  # 粗分辨率尝试次数不足
                return 0
            elif self.N[1] < 3:  # 给中粗分辨率一些机会
                return 1
            else:
                return 0  # 主要使用粗分辨率

        elif self.training_stage == "refinement":
            # 精化阶段：在粗分辨率和中等分辨率之间平衡
            # 使用UCB策略，但偏向中等精度
            ucb_values = np.zeros(self.K)
            for i in range(self.K):
                if self.N[i] == 0:
                    ucb_values[i] = np.inf
                else:
                    # 对中等分辨率给予奖励加成
                    bonus = 0.1 if i == 1 else 0.0
                    ucb_values[i] = self.Q[i] + bonus + self.c * np.sqrt(np.log(self.M) / self.N[i])

            return np.argmax(ucb_values)

        else:  # optimization阶段
            # 优化阶段：追求最佳性能，允许使用最精细的分辨率
            # 标准UCB策略
            ucb_values = np.zeros(self.K)
            for i in range(self.K):
                if self.N[i] == 0:
                    ucb_values[i] = np.inf
                else:
                    ucb_values[i] = self.Q[i] + self.c * np.sqrt(np.log(self.M) / self.N[i])

            return np.argmax(ucb_values)
    
    def update_performance(self, episode: int, episode_reward: float,
                          critic_loss: float, violation_count: int, success: bool = None):
        """
        更新性能指标并跟踪训练进展

        Args:
            episode: episode编号
            episode_reward: episode奖励
            critic_loss: Critic网络损失
            violation_count: 约束违反次数
            success: 是否成功（可选）
        """
        stage = episode // self.L

        if stage not in self.stage_performance:
            self.stage_performance[stage] = {
                'rewards': [],
                'critic_losses': [],
                'violations': [],
                'successes': [],
                'arm': self.current_arm
            }

        # 推断成功状态（如果未提供）
        if success is None:
            success = episode_reward > 20000 and violation_count == 0  # 简单的成功判断

        self.stage_performance[stage]['rewards'].append(episode_reward)
        self.stage_performance[stage]['critic_losses'].append(critic_loss)
        self.stage_performance[stage]['violations'].append(violation_count)
        self.stage_performance[stage]['successes'].append(success)

        # 更新训练进展跟踪
        self.recent_rewards.append(episode_reward)
        self.recent_success_rates.append(1.0 if success else 0.0)

        # 保持窗口大小
        if len(self.recent_rewards) > self.performance_window:
            self.recent_rewards.pop(0)
        if len(self.recent_success_rates) > self.performance_window:
            self.recent_success_rates.pop(0)
    
    def _update_arm_selection(self):
        """更新臂选择（UCB策略）"""
        self.M += 1
        m = self.M
        
        # 计算当前臂的回报
        if m > 1 and self.current_arm in self.stage_performance.get(m-1, {}):
            r_bandit = self._compute_bandit_reward(m-1)

            # 更新平均回报（注意：N[self.current_arm]在这里还没有+1）
            self.Q[self.current_arm] = (
                (self.Q[self.current_arm] * self.N[self.current_arm] + r_bandit) /
                (self.N[self.current_arm] + 1)
            )

            # 记录回报历史
            self.reward_history.append({
                'stage': m-1,
                'arm': self.current_arm,
                'reward': r_bandit,
                'avg_reward': self.Q[self.current_arm]
            })
        
        # 基于场景阶段和训练进展的自适应分辨率选择
        self.current_arm = self._select_resolution_by_scenario_and_progress()

        # 更新选择计数
        self.N[self.current_arm] += 1

        # 计算UCB值用于显示
        ucb_values = np.zeros(self.K)
        for i in range(self.K):
            if self.N[i] == 0:
                ucb_values[i] = np.inf
            else:
                ucb_values[i] = self.Q[i] + self.c * np.sqrt(np.log(m) / self.N[i])

        # 获取当前场景的候选分辨率集合
        requirements = self.scenario_resolution_requirements[self.current_scenario_stage]
        candidate_arms = requirements["candidate_resolutions"]

        print(f"🎰 Stage {m} ({self.current_scenario_stage}-{self.training_progress}): 选择臂 {self.current_arm} ({self.configs[self.current_arm].name})")
        print(f"   场景阶段: {self.current_scenario_stage}")
        print(f"   训练进展: {self.training_progress}")
        print(f"   候选分辨率: {candidate_arms} (ResBand在此集合内学习最优选择)")
        print(f"   UCB值: {ucb_values}")
        print(f"   平均回报: {self.Q}")
        print(f"   选择次数: {self.N}")

        # 显示候选臂的性能对比
        if len(candidate_arms) > 1:
            print(f"   候选臂性能对比:")
            for arm in candidate_arms:
                if self.N[arm] > 0:
                    print(f"     臂{arm}: 平均回报={self.Q[arm]:.2f}, 选择次数={self.N[arm]}")
                else:
                    print(f"     臂{arm}: 未尝试")

        if len(self.recent_success_rates) > 0:
            print(f"   最近成功率: {np.mean(self.recent_success_rates[-5:]):.2f}")
        if len(self.recent_rewards) > 0:
            print(f"   最近奖励: {np.mean(self.recent_rewards[-5:]):.1f}")
    
    def _compute_bandit_reward(self, stage: int) -> float:
        """
        计算老虎机回报函数
        
        Args:
            stage: 阶段编号
            
        Returns:
            老虎机回报值
        """
        if stage not in self.stage_performance:
            return 0.0
        
        stage_data = self.stage_performance[stage]
        
        # 计算阶段平均指标
        avg_reward = np.mean(stage_data['rewards'])
        avg_critic_loss = np.mean(stage_data['critic_losses'])
        avg_violations = np.mean(stage_data['violations'])
        
        # 计算与前一阶段的差异
        if stage > 0 and (stage-1) in self.stage_performance:
            prev_data = self.stage_performance[stage-1]
            prev_avg_reward = np.mean(prev_data['rewards'])
            prev_avg_critic_loss = np.mean(prev_data['critic_losses'])
            prev_avg_violations = np.mean(prev_data['violations'])
            
            delta_reward = avg_reward - prev_avg_reward
            delta_critic_loss = -(avg_critic_loss - prev_avg_critic_loss)  # 负号使损失下降变为正回报
            delta_violations = -(avg_violations - prev_avg_violations)  # 负号使违反次数减少变为正回报
        else:
            # 第一阶段，使用绝对值
            delta_reward = avg_reward
            delta_critic_loss = -avg_critic_loss
            delta_violations = -avg_violations
        
        # 计算加权回报
        r_bandit = (self.alpha * delta_reward + 
                   self.beta * delta_critic_loss + 
                   self.gamma * delta_violations)
        
        return r_bandit
    
    def get_optimal_config(self) -> ResolutionConfig:
        """获取最优分辨率配置"""
        if np.sum(self.N) == 0:
            return self.configs[0]  # 如果还没有选择过任何臂，返回第一个
        
        best_arm = np.argmax(self.Q)
        return self.configs[best_arm]
    
    def save_results(self, filename: str = None):
        """保存算法结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"resband_results_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        results = {
            "algorithm_config": {
                "K": self.K,
                "exploration_coefficient": self.c,
                "stage_length": self.L,
                "reward_weights": [self.alpha, self.beta, self.gamma]
            },
            "configs": [config.to_dict() for config in self.configs],
            "final_state": {
                "Q": self.Q.tolist(),
                "N": self.N.tolist(),
                "M": self.M,
                "current_arm": self.current_arm
            },
            "arm_selection_history": self.arm_selection_history,
            "reward_history": self.reward_history,
            "stage_performance": self.stage_performance,
            "optimal_config": self.get_optimal_config().to_dict()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"📁 ResBand结果已保存到: {filepath}")
        return filepath
    
    def plot_results(self):
        """绘制算法结果图表"""
        try:
            import matplotlib.pyplot as plt
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('ResBand算法性能分析', fontsize=16)
            
            # 1. 臂选择历史
            episodes = [h['episode'] for h in self.arm_selection_history]
            arms = [h['arm'] for h in self.arm_selection_history]
            
            axes[0, 0].plot(episodes, arms, 'o-', alpha=0.7)
            axes[0, 0].set_title('臂选择历史')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('臂编号')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 平均回报
            if self.reward_history:
                stages = [h['stage'] for h in self.reward_history]
                rewards = [h['reward'] for h in self.reward_history]
                avg_rewards = [h['avg_reward'] for h in self.reward_history]
                
                axes[0, 1].plot(stages, rewards, 'o-', label='阶段回报', alpha=0.7)
                axes[0, 1].plot(stages, avg_rewards, 's-', label='平均回报', alpha=0.7)
                axes[0, 1].set_title('回报变化')
                axes[0, 1].set_xlabel('阶段')
                axes[0, 1].set_ylabel('回报')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 选择次数分布
            arm_names = [config.name for config in self.configs]
            axes[1, 0].bar(range(self.K), self.N, alpha=0.7)
            axes[1, 0].set_title('臂选择次数分布')
            axes[1, 0].set_xlabel('臂编号')
            axes[1, 0].set_ylabel('选择次数')
            axes[1, 0].set_xticks(range(self.K))
            axes[1, 0].set_xticklabels(arm_names, rotation=45)
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 最终平均回报
            axes[1, 1].bar(range(self.K), self.Q, alpha=0.7)
            axes[1, 1].set_title('最终平均回报')
            axes[1, 1].set_xlabel('臂编号')
            axes[1, 1].set_ylabel('平均回报')
            axes[1, 1].set_xticks(range(self.K))
            axes[1, 1].set_xticklabels(arm_names, rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = f"resband_analysis_{timestamp}.png"
            plot_path = os.path.join(self.output_dir, plot_filename)
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📊 ResBand分析图表已保存到: {plot_path}")
            return plot_path
            
        except ImportError:
            print("⚠️ matplotlib未安装，跳过图表生成")
            return None

def create_default_configs() -> List[ResolutionConfig]:
    """创建默认的分辨率配置"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.5, "计算效率优先，控制粗糙"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.25, "平衡精度和效率"),
        ResolutionConfig("细分辨率", 1.0, 4.0, 0.1, "高精度控制，计算代价大")
    ]
    return configs

def create_paper_configs() -> List[ResolutionConfig]:
    """创建论文中使用的分辨率配置（平衡版本，测试探索-利用策略）"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.4, "快速探索"),
        ResolutionConfig("中粗分辨率", 3.0, 12.0, 0.3, "平衡配置"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.2, "精确控制")
    ]
    return configs

def create_original_paper_configs() -> List[ResolutionConfig]:
    """创建原始论文配置（可能计算量过大）"""
    configs = [
        ResolutionConfig("粗分辨率", 3.0, 12.0, 0.3, "计算效率优先"),
        ResolutionConfig("中等分辨率", 1.5, 6.0, 0.15, "论文默认配置"),
        ResolutionConfig("细分辨率", 0.8, 3.0, 0.08, "高精度控制")
    ]
    return configs
