This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.11.28)  29 AUG 2025 18:43
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**complete_resband_paper.tex
(./complete_resband_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count192
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count193
\g__pdf_backend_annotation_int=\count194
\g__pdf_backend_link_int=\count195
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count196
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen141
\g__ctex_section_depth_int=\count197
\g__ctex_font_size_int=\count198

(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate-2023-10-10
.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count199
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
))
\l__xeCJK_tmp_int=\count266
\l__xeCJK_tmp_box=\box53
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count267
\l__xeCJK_begin_int=\count268
\l__xeCJK_end_int=\count269
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count270
\g__xeCJK_node_int=\count271
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box54
\l__xeCJK_last_penalty_int=\count272
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count273

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count274
\l__xeCJK_fam_int=\count275
\g__xeCJK_fam_allocation_int=\count276
\l__xeCJK_verb_case_int=\count277
\l__xeCJK_verb_exspace_skip=\skip57
 (e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count278
\l__fontspec_language_int=\count279
\l__fontspec_strnum_int=\count280
\l__fontspec_tmp_int=\count281
\l__fontspec_tmpa_int=\count282
\l__fontspec_tmpb_int=\count283
\l__fontspec_tmpc_int=\count284
\l__fontspec_em_int=\count285
\l__fontspec_emdef_int=\count286
\l__fontspec_strong_int=\count287
\l__fontspec_strongdef_int=\count288
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count289
\l__zhnum_tmp_int=\count290

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)

(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(e:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen175
))
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen176
)
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count291
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count292
\leftroot@=\count293
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count294
\DOTSCASE@=\count295
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen177
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count296
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count297
\dotsspace@=\muskip17
\c@parentequation=\count298
\dspbrk@lvl=\count299
\tag@help=\toks18
\row@=\count300
\column@=\count301
\maxfields@=\count302
\andhelp@=\toks19
\eqnshift@=\dimen178
\alignsep@=\dimen179
\tagshift@=\dimen180
\tagwidth@=\dimen181
\totwidth@=\dimen182
\lineht@=\dimen183
\@envbody=\toks20
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(e:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count303
\float@exts=\toks22
\float@box=\box57
\@float@everytoks=\toks23
\@floatcapt=\box58
)
(e:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks24
\c@algorithm=\count304
)
(e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(e:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks25
)
\c@ALC@unique=\count305
\c@ALC@line=\count306
\c@ALC@rem=\count307
\c@ALC@depth=\count308
\ALC@tlm=\skip62
\algorithmicindent=\skip63
)
(e:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(e:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(e:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.

(e:/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
)
(e:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(e:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count309
\Gm@cntv=\count310
\c@Gm@tempcnt=\count311
\Gm@bindingoffset=\dimen186
\Gm@wd@mp=\dimen187
\Gm@odd@mp=\dimen188
\Gm@even@mp=\dimen189
\Gm@layoutwidth=\dimen190
\Gm@layoutheight=\dimen191
\Gm@layouthoffset=\dimen192
\Gm@layoutvoffset=\dimen193
\Gm@dimlist=\toks26
)
(e:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen194
\lightrulewidth=\dimen195
\cmidrulewidth=\dimen196
\belowrulesep=\dimen197
\belowbottomsep=\dimen198
\aboverulesep=\dimen199
\abovetopsep=\dimen256
\cmidrulesep=\dimen257
\cmidrulekern=\dimen258
\defaultaddspace=\dimen259
\@cmidla=\count312
\@cmidlb=\count313
\@aboverulesep=\dimen260
\@belowrulesep=\dimen261
\@thisruleclass=\count314
\@lastruleclass=\count315
\@thisrulewidth=\dimen262
)
(e:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip64
\multirow@cntb=\count316
\multirow@dima=\skip65
\bigstrutjot=\dimen263
)
(e:/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks27
\pgfutil@tempdima=\dimen264
\pgfutil@tempdimb=\dimen265
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box59
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(e:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks28
\pgfkeys@temptoks=\toks29

(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.co
de.tex
\pgfkeys@tmptoks=\toks30
))
\pgf@x=\dimen266
\pgf@y=\dimen267
\pgf@xa=\dimen268
\pgf@ya=\dimen269
\pgf@xb=\dimen270
\pgf@yb=\dimen271
\pgf@xc=\dimen272
\pgf@yc=\dimen273
\pgf@xd=\dimen274
\pgf@yd=\dimen275
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count317
\c@pgf@countb=\count318
\c@pgf@countc=\count319
\c@pgf@countd=\count320
\t@pgf@toka=\toks31
\t@pgf@tokb=\toks32
\t@pgf@tokc=\toks33
\pgf@sys@id@count=\count321
 (e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count322
)))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count323
\pgfsyssoftpath@bigbuffer@items=\count324
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(e:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen276
\pgfmath@count=\count325
\pgfmath@box=\box60
\pgfmath@toks=\toks34
\pgfmath@stack@operand=\toks35
\pgfmath@stack@operation=\toks36
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.te
x)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric
.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.t
ex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.co
de.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.te
x)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithm
etics.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count326
))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen277
\pgf@picmaxx=\dimen278
\pgf@picminy=\dimen279
\pgf@picmaxy=\dimen280
\pgf@pathminx=\dimen281
\pgf@pathmaxx=\dimen282
\pgf@pathminy=\dimen283
\pgf@pathmaxy=\dimen284
\pgf@xx=\dimen285
\pgf@xy=\dimen286
\pgf@yx=\dimen287
\pgf@yy=\dimen288
\pgf@zx=\dimen289
\pgf@zy=\dimen290
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.cod
e.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen291
\pgf@path@lasty=\dimen292
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.te
x
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen293
\pgf@shorten@start@additional=\dimen294
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count327
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code
.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen295
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.c
ode.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen296
\pgf@pt@y=\dimen297
\pgf@pt@temp=\dimen298
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.co
de.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen299
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen300
\pgf@sys@shading@range@num=\count328
\pgf@shadingcount=\count329
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code
.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (e:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.te
x
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.st
y
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen301
\pgf@nodesepend=\dimen302
)
(e:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.st
y
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(e:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(e:/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen303
\pgffor@skip=\dimen304
\pgffor@stack=\toks37
\pgffor@toks=\toks38
))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.co
de.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count330
\pgfplotmarksize=\dimen305
)
\tikz@lastx=\dimen306
\tikz@lasty=\dimen307
\tikz@lastxsaved=\dimen308
\tikz@lastysaved=\dimen309
\tikz@lastmovetox=\dimen310
\tikz@lastmovetoy=\dimen311
\tikzleveldistance=\dimen312
\tikzsiblingdistance=\dimen313
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count331
\tikznumberofchildren=\count332
\tikznumberofcurrentchild=\count333
\tikz@fig@count=\count334

(e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count335
\pgfmatrixcurrentcolumn=\count336
\pgf@matrix@numberofcolumns=\count337
)
\tikz@expandcount=\count338

(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(e:/texlive/2024/texmf-dist/tex/latex/tikz-3dplot/tikz-3dplot.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarycalc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brary3d.code.tex
File: tikzlibrary3d.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen314
))) (e:/texlive/2024/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks39
\t@pgfplots@tokb=\toks40
\t@pgfplots@tokc=\toks41
\pgfplots@tmpa=\dimen315
\c@pgfplots@coordindex=\count339
\c@pgfplots@scanlineindex=\count340

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.te
x))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfs
upp_loader.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks42
\t@pgf@tokb=\toks43
\t@pgf@tokc=\toks44

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfs
upp_pgfutil-common-lists.tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststru
cture.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststru
ctureext.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.co
de.tex
\c@pgfplotsarray@tmp=\count341
)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.c
ode.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.c
ode.tex
\c@pgfplotstable@counta=\count342
\t@pgfplotstable@a=\toks45
)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.co
de.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.
tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.te
x)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfsh
ading.code.tex
\c@pgfplotslibrarysurf@no=\count343

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfsha
ding.pgfsys-xetex.def
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfsha
ding.pgfsys-dvipdfmx.def
\c@pgfplotslibrarysurf@streamlen=\count344
))))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.t
ex
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex
))) (e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)

(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.t
ex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.t
ex
\pgfdecoratedcompleteddistance=\dimen316
\pgfdecoratedremainingdistance=\dimen317
\pgfdecoratedinputsegmentcompleteddistance=\dimen318
\pgfdecoratedinputsegmentremainingdistance=\dimen319
\pgf@decorate@distancetomove=\dimen320
\pgf@decorate@repeatstate=\count345
\pgfdecorationsegmentamplitude=\dimen321
\pgfdecorationsegmentlength=\dimen322
)
\tikz@lib@dec@box=\box70
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.pathmorphing.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydec
orations.pathmorphing.code.tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
brarydecorations.pathreplacing.code.tex
(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydec
orations.pathreplacing.code.tex))
(e:/texlive/2024/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.conto
urlua.code.tex)
\pgfplots@numplots=\count346
\pgfplots@xmin@reg=\dimen323
\pgfplots@xmax@reg=\dimen324
\pgfplots@ymin@reg=\dimen325
\pgfplots@ymax@reg=\dimen326
\pgfplots@zmin@reg=\dimen327
\pgfplots@zmax@reg=\dimen328
)
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.
tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(e:/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzli
braryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(e:/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.g
eometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (./complete_resband_paper.aux)
\openout1 = `complete_resband_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 25.
LaTeX Font Info:    Redeclaring math accent \acute on input line 25.
LaTeX Font Info:    Redeclaring math accent \grave on input line 25.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 25.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 25.
LaTeX Font Info:    Redeclaring math accent \bar on input line 25.
LaTeX Font Info:    Redeclaring math accent \breve on input line 25.
LaTeX Font Info:    Redeclaring math accent \check on input line 25.
LaTeX Font Info:    Redeclaring math accent \hat on input line 25.
LaTeX Font Info:    Redeclaring math accent \dot on input line 25.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 25.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 25.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 25.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 25.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 25.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 25.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 25.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package pgfplots notification 'compat/show suggested version=true': document ha
s been generated with the most recent feature set (\pgfplotsset{compat=1.18}).

LaTeX Font Info:    Trying to load font information for U+msa on input line 27.

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 27.


(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

] [2] [3] [4]
[5]
Overfull \hbox (2.22302pt too wide) in paragraph at lines 304--305
[] []$\OMS/cmsy/m/n/12 R\OML/cmm/m/it/12 :[]\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 e
pisode; episode[]reward; critic[]loss; episode[]violations\OT1/cmr/m/n/12 )$ 
 []

[6]
Overfull \vbox (383.9315pt too high) has occurred while \output is active []


[7] [8] [9] [10]
Overfull \hbox (41.1pt too wide) in paragraph at lines 485--495
 [][] 
 []

[11] [12] [13] [14]
Overfull \hbox (41.1pt too wide) in paragraph at lines 732--743
 [][] 
 []

[15] [16] [17] [18] [19] [20] [21] [22] [23] [24] [25] [26]
(./complete_resband_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********
 ) 
Here is how much of TeX's memory you used:
 31635 strings out of 474773
 855247 string characters out of 5761288
 1953842 words of memory out of 5000000
 53271 multiletter control sequences out of 15000+600000
 567219 words of font info for 100 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,17n,97p,751b,2225s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on complete_resband_paper.pdf (26 pages).
