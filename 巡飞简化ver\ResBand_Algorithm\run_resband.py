"""
ResBand算法主运行脚本 - 改进版本
提供完整的ResBand算法训练、测试和对比实验功能
支持集成训练模式，直接替换巡飞简化ver中的分阶段训练
"""

import argparse
import os
import sys
import json
from datetime import datetime

from resband_trainer import ResBandTrainer
from resband_comparison import run_comparison_experiment
from integrated_resband_training import IntegratedResBandTrainer

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ResBand算法训练和测试 - 改进版本')
    
    # 基本参数
    parser.add_argument('--mode', type=str, default='train', 
                       choices=['train', 'test', 'comparison', 'integrated'],
                       help='运行模式: train(单阶段训练), test(测试), comparison(对比实验), integrated(集成分阶段训练)')
    
    # 训练参数
    parser.add_argument('--episodes', type=int, default=200,
                       help='训练episodes数量')
    parser.add_argument('--use-resband', action='store_true', default=True,
                       help='启用ResBand算法')
    parser.add_argument('--resband-config', type=str, default=None,
                       help='ResBand配置文件路径')
    
    # 输出参数
    parser.add_argument('--output-dir', type=str, default='results/resband_training',
                       help='输出目录')
    parser.add_argument('--save-interval', type=int, default=50,
                       help='保存间隔')
    parser.add_argument('--plot-interval', type=int, default=20,
                       help='绘图间隔')
    
    # 测试参数
    parser.add_argument('--test-episodes', type=int, default=50,
                       help='测试episodes数量')
    parser.add_argument('--model-path', type=str, default=None,
                       help='模型路径（用于测试）')
    
    # 集成训练参数
    parser.add_argument('--fast-mode', action='store_true',
                       help='快速模式（减少episodes）')
    parser.add_argument('--stage-configs', type=str, default=None,
                       help='分阶段配置文件路径（JSON格式）')
    
    args = parser.parse_args()
    
    print("🎰 ResBand算法主程序（改进版本）")
    print("=" * 50)
    print(f"运行模式: {args.mode}")
    print(f"使用ResBand: {args.use_resband}")
    print(f"输出目录: {args.output_dir}")
    print()
    
    if args.mode == 'train':
        run_training(args)
    elif args.mode == 'test':
        run_testing(args)
    elif args.mode == 'comparison':
        run_comparison_experiment()
    elif args.mode == 'integrated':
        run_integrated_training(args)
    else:
        print(f"❌ 未知的运行模式: {args.mode}")

def run_training(args):
    """运行单阶段训练"""
    print("🚀 开始ResBand单阶段训练...")
    
    # 解析ResBand配置
    resband_config = None
    if args.use_resband:
        if args.resband_config:
            with open(args.resband_config, 'r', encoding='utf-8') as f:
                resband_config = json.load(f)
        else:
            # 使用改进的配置
            resband_config = {
                'exploration_coefficient': 2.0,
                'stage_length': 20,
                'reward_weights': (0.6, 0.2, 0.1),
                'adaptive_exploration': True,
                'performance_threshold': 0.6,
                'use_coarse_resolution': False
            }
    
    # 创建训练器
    trainer = ResBandTrainer(
        use_resband=args.use_resband,
        resband_config=resband_config,
        output_dir=args.output_dir
    )
    
    # 开始训练
    trainer, results = trainer.train(
        num_episodes=args.episodes,
        save_interval=args.save_interval,
        plot_interval=args.plot_interval
    )
    
    print(f"\n✅ 单阶段训练完成!")
    print(f"🎯 最终成功率: {results['success_rate']:.1%}")
    print(f"⏱️  训练时间: {results['training_time']:.1f}秒")
    print(f"📊 最终平均奖励: {results['final_avg_reward']:.2f}")
    print(f"🚫 总违反次数: {results['total_violations']}")
    
    return trainer, results

def run_testing(args):
    """运行测试"""
    print("🧪 开始ResBand测试...")
    
    # 创建训练器
    trainer = ResBandTrainer(
        use_resband=args.use_resband,
        output_dir=args.output_dir
    )
    
    # 加载模型（如果指定）
    if args.model_path and os.path.exists(args.model_path):
        trainer.controller.load(args.model_path)
        print(f"📁 已加载模型: {args.model_path}")
    
    # 运行测试
    test_results = trainer.run_test(num_test_episodes=args.test_episodes)
    
    print(f"\n✅ 测试完成!")
    print(f"🎯 测试成功率: {test_results['successes'].count(True) / len(test_results['successes']):.1%}")
    print(f"📊 平均奖励: {sum(test_results['rewards']) / len(test_results['rewards']):.2f}")
    
    return trainer, test_results

def run_integrated_training(args):
    """运行集成分阶段训练"""
    print("🚀 开始ResBand集成分阶段训练...")
    print("📋 此模式将直接替换巡飞简化ver中的分阶段训练")
    
    # 解析ResBand配置
    resband_config = None
    if args.use_resband:
        if args.resband_config:
            with open(args.resband_config, 'r', encoding='utf-8') as f:
                resband_config = json.load(f)
        else:
            # 集成训练的优化配置
            resband_config = {
                'exploration_coefficient': 2.0,
                'stage_length': 15,  # 减少阶段长度以更快适应
                'reward_weights': (0.6, 0.2, 0.1),  # 调整权重（3个权重）
                'adaptive_exploration': True,
                'performance_threshold': 0.5,
                'use_fast_configs': args.fast_mode
            }
    
    # 解析分阶段配置
    stage_configs = None
    if args.stage_configs:
        with open(args.stage_configs, 'r', encoding='utf-8') as f:
            stage_configs = json.load(f)
    elif args.fast_mode:
        # 快速模式的分阶段配置
        stage_configs = {
            'stage1': {
                'name': '简单环境',
                'episodes': 50,
                'obstacle_count': 3,
                'complexity': 'simple'
            },
            'stage2': {
                'name': '中等环境',
                'episodes': 75,
                'obstacle_count': 6,
                'complexity': 'medium'
            },
            'stage3': {
                'name': '复杂环境',
                'episodes': 100,
                'obstacle_count': 10,
                'complexity': 'complex'
            }
        }
    
    # 创建集成训练器
    trainer = IntegratedResBandTrainer(
        use_resband=args.use_resband,
        resband_config=resband_config,
        output_dir=args.output_dir,
        stage_configs=stage_configs
    )
    
    # 开始训练
    results = trainer.run_training()
    
    print(f"\n✅ 集成分阶段训练完成!")
    print(f"🎯 总体成功率: {results['overall_success_rate']:.1%}")
    print(f"⏱️  总训练时间: {results['total_training_time']:.1f}秒")
    print(f"📊 总体平均奖励: {results['overall_avg_reward']:.2f}")
    print(f"🚫 总违反次数: {results['total_violations']}")
    
    return trainer, results

def create_config_file():
    """创建配置文件示例"""
    config = {
        "exploration_coefficient": 2.0,
        "stage_length": 15,
        "reward_weights": [0.6, 0.2, 0.1],
        "adaptive_exploration": True,
        "performance_threshold": 0.5,
        "description": "ResBand算法改进配置示例",
        "parameters": {
            "exploration_coefficient": "UCB探索系数，控制探索程度",
            "stage_length": "每个阶段的episode数量",
            "reward_weights": "回报函数权重 [α, β, γ, δ]，分别对应奖励差异、Critic损失下降、约束违反减少、成功率",
            "adaptive_exploration": "是否启用自适应探索",
            "performance_threshold": "性能阈值，用于触发自适应调整"
        }
    }
    
    config_path = "resband_config_improved.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"📄 改进配置文件示例已创建: {config_path}")
    return config_path

def create_stage_config_file():
    """创建分阶段配置文件示例"""
    stage_config = {
        "stage1": {
            "name": "简单环境",
            "episodes": 100,
            "obstacle_count": 3,
            "complexity": "simple",
            "description": "简单环境训练阶段"
        },
        "stage2": {
            "name": "中等环境",
            "episodes": 150,
            "obstacle_count": 6,
            "complexity": "medium",
            "description": "中等环境训练阶段"
        },
        "stage3": {
            "name": "复杂环境",
            "episodes": 200,
            "obstacle_count": 10,
            "complexity": "complex",
            "description": "复杂环境训练阶段"
        }
    }
    
    config_path = "stage_config_example.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(stage_config, f, indent=2, ensure_ascii=False)
    
    print(f"📄 分阶段配置文件示例已创建: {config_path}")
    return config_path

def print_usage_examples():
    """打印使用示例"""
    print("\n📖 使用示例:")
    print("=" * 50)
    
    print("1. 基本单阶段训练（使用ResBand）:")
    print("   python run_resband.py --mode train --episodes 200")
    
    print("\n2. 集成分阶段训练（推荐）:")
    print("   python run_resband.py --mode integrated")
    
    print("\n3. 快速集成训练:")
    print("   python run_resband.py --mode integrated --fast-mode")
    
    print("\n4. 使用自定义配置的集成训练:")
    print("   python run_resband.py --mode integrated --resband-config my_config.json --stage-configs my_stages.json")
    
    print("\n5. 测试训练好的模型:")
    print("   python run_resband.py --mode test --test-episodes 100 --model-path results/models/final_model.pth")
    
    print("\n6. 运行对比实验:")
    print("   python run_resband.py --mode comparison")
    
    print("\n7. 不使用ResBand的固定分辨率训练:")
    print("   python run_resband.py --mode train --episodes 200 --use-resband false")
    
    print("\n8. 创建配置文件示例:")
    print("   python run_resband.py --create-config")
    
    print("\n9. 创建分阶段配置文件示例:")
    print("   python run_resband.py --create-stage-config")

if __name__ == "__main__":
    # 检查是否有特殊参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--create-config':
            create_config_file()
            print_usage_examples()
        elif sys.argv[1] == '--create-stage-config':
            create_stage_config_file()
            print_usage_examples()
        elif sys.argv[1] == '--help-examples':
            print_usage_examples()
        else:
            main()
    else:
        main()
