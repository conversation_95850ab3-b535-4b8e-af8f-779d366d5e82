"""
测试改进后的动态障碍物预测DWA算法
验证动态障碍物运动预测的准确性和安全性
"""

import numpy as np
import matplotlib.pyplot as plt
from loitering_munition_dwa import LoiteringMunitionDWA
from resolution_bandit import ResolutionConfig

def test_dynamic_obstacle_prediction():
    """测试动态障碍物预测功能"""
    print("🎯 测试动态障碍物预测DWA算法")
    print("=" * 50)
    
    # 创建DWA实例（使用最高分辨率）
    fine_resolution = ResolutionConfig("最高分辨率", 2.0, 8.0, 0.2, "动态障碍物专用")
    dwa = LoiteringMunitionDWA(resolution_config=fine_resolution)
    
    print(f"DWA配置: {fine_resolution}")
    print(f"预测时间窗口: {dwa.predict_time}秒")
    print(f"最小安全距离: {dwa.min_safe_distance}米")
    
    # 创建测试场景
    current_state = np.array([500, 500, 100, 30.0, 0.0, 0.0])  # [x, y, z, V, gamma, psi]
    goal = np.array([800, 800, 100])
    
    # 创建动态障碍物
    dynamic_obstacles = create_test_dynamic_obstacles()
    
    print(f"\n📍 测试场景:")
    print(f"   巡飞弹位置: {current_state[:3]}")
    print(f"   目标位置: {goal}")
    print(f"   动态障碍物数量: {len(dynamic_obstacles)}")
    
    # 测试动态障碍物位置预测
    print(f"\n🔮 动态障碍物位置预测测试:")
    test_obstacle_prediction(dwa, dynamic_obstacles)
    
    # 测试安全动作生成
    print(f"\n🛡️ 安全动作生成测试:")
    test_safe_action_generation(dwa, current_state, goal, dynamic_obstacles)
    
    # 可视化测试（如果需要）
    # visualize_dynamic_prediction(dwa, current_state, goal, dynamic_obstacles)

def create_test_dynamic_obstacles():
    """创建测试用的动态障碍物（设计为阻挡巡飞弹路径）"""
    obstacles = []

    # 线性运动障碍物 - 适度威胁，留有避障空间
    linear_obstacle = {
        'center': np.array([580, 550, 100]),  # 稍微偏离直线路径
        'radius': 60,  # 适中半径
        'motion_type': 'linear',
        'time': 0.0,
        'motion_params': {
            'velocity': np.array([5, 8, 0]),  # 适中速度
            'bounds': {
                'x': [400, 800],
                'y': [400, 800],
                'z': [50, 150]
            }
        }
    }
    obstacles.append(linear_obstacle)
    
    # 圆周运动障碍物 - 在侧面巡逻，不直接阻挡
    circular_obstacle = {
        'center': np.array([650, 750, 100]),  # 偏离主路径
        'radius': 40,  # 适中半径
        'motion_type': 'circular',
        'time': 0.0,
        'motion_params': {
            'center_orbit': np.array([650, 750, 100]),
            'radius_orbit': 60,  # 适中轨道半径
            'angular_speed': 0.4,  # 适中角速度
            'phase': 0.0
        }
    }
    obstacles.append(circular_obstacle)
    
    # 振荡运动障碍物
    oscillating_obstacle = {
        'center': np.array([700, 650, 100]),
        'radius': 35,
        'motion_type': 'oscillating',
        'time': 0.0,
        'motion_params': {
            'center_base': np.array([700, 650, 100]),
            'amplitude': np.array([30, 20, 0]),
            'frequency': 0.3,
            'phase': 0.0
        }
    }
    obstacles.append(oscillating_obstacle)
    
    return obstacles

def test_obstacle_prediction(dwa, obstacles):
    """测试障碍物位置预测准确性"""
    prediction_times = [0.0, 0.5, 1.0, 1.5, 2.0]
    
    for i, obs in enumerate(obstacles):
        print(f"\n   障碍物 {i+1} ({obs['motion_type']}运动):")
        print(f"     当前位置: {obs['center']}")
        
        for t in prediction_times:
            predicted_pos = dwa._predict_dynamic_obstacle_position(obs, t)
            print(f"     {t}秒后预测位置: {predicted_pos}")
        
        # 验证预测准确性（模拟实际运动）
        actual_positions = simulate_actual_motion(obs, prediction_times)
        print(f"     实际运动轨迹验证:")
        
        for j, t in enumerate(prediction_times):
            if j > 0:  # 跳过t=0的情况
                predicted = dwa._predict_dynamic_obstacle_position(obs, t)
                actual = actual_positions[j]
                error = np.linalg.norm(predicted - actual)
                print(f"       {t}秒: 预测误差 {error:.2f}米")

def simulate_actual_motion(obstacle, time_points):
    """模拟障碍物的实际运动（用于验证预测准确性）"""
    positions = []
    
    for t in time_points:
        motion_type = obstacle['motion_type']
        motion_params = obstacle['motion_params']
        
        if motion_type == 'linear':
            velocity = np.array(motion_params['velocity'])
            pos = obstacle['center'] + velocity * t
            
        elif motion_type == 'circular':
            center_orbit = np.array(motion_params['center_orbit'])
            radius_orbit = motion_params['radius_orbit']
            angular_speed = motion_params['angular_speed']
            phase = motion_params['phase']
            
            angle = angular_speed * t + phase
            pos = center_orbit + np.array([
                radius_orbit * np.cos(angle),
                radius_orbit * np.sin(angle),
                0
            ])
            
        elif motion_type == 'oscillating':
            center_base = np.array(motion_params['center_base'])
            amplitude = np.array(motion_params['amplitude'])
            frequency = motion_params['frequency']
            phase = motion_params['phase']
            
            pos = center_base + amplitude * np.sin(frequency * t + phase)
        
        positions.append(pos)
    
    return positions

def test_safe_action_generation(dwa, current_state, goal, obstacles):
    """测试安全动作生成"""
    print(f"   生成安全动作集...")
    
    # 不使用动态预测的传统方法
    traditional_safe_actions = dwa.generate_safe_control_set(
        current_state, [], goal, max_actions=10  # 空障碍物列表，模拟传统方法
    )
    
    # 使用动态预测的改进方法
    improved_safe_actions = dwa.generate_safe_control_set(
        current_state, obstacles, goal, max_actions=10
    )
    
    print(f"   传统方法（忽略动态障碍物）: {len(traditional_safe_actions)} 个安全动作")
    print(f"   改进方法（考虑动态预测）: {len(improved_safe_actions)} 个安全动作")
    
    if len(traditional_safe_actions) > len(improved_safe_actions):
        reduction_rate = (len(traditional_safe_actions) - len(improved_safe_actions)) / len(traditional_safe_actions)
        print(f"   ✅ 动态预测有效：安全动作减少 {reduction_rate:.1%}（过滤了不安全的动作）")
    else:
        print(f"   ⚠️  动态预测可能过于保守或场景不够复杂")
    
    # 分析最佳动作
    if improved_safe_actions:
        best_action = improved_safe_actions[0]  # 已按评分排序
        print(f"   🎯 推荐最佳动作: {best_action}")
        
        # 验证这个动作的安全性
        is_safe = dwa._is_safe_control(best_action, current_state, obstacles)
        print(f"   🛡️ 安全性验证: {'✅ 安全' if is_safe else '❌ 不安全'}")
    else:
        print(f"   ⚠️  警告：没有找到安全动作！")

def analyze_resolution_impact():
    """分析分辨率对动态障碍物处理的影响"""
    print(f"\n📊 分辨率影响分析:")
    
    resolutions = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.4, "快速"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.2, "平衡"),
        ResolutionConfig("细分辨率", 1.0, 4.0, 0.1, "精确")
    ]
    
    current_state = np.array([500, 500, 100, 30.0, 0.0, 0.0])
    goal = np.array([800, 800, 100])
    obstacles = create_test_dynamic_obstacles()
    
    for resolution in resolutions:
        dwa = LoiteringMunitionDWA(resolution_config=resolution)
        safe_actions = dwa.generate_safe_control_set(current_state, obstacles, goal, max_actions=20)
        
        print(f"   {resolution.name}: {len(safe_actions)} 个安全动作")

if __name__ == "__main__":
    test_dynamic_obstacle_prediction()
    analyze_resolution_impact()
