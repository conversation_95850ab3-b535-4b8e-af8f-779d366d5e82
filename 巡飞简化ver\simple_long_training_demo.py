"""
简洁明了的长期训练演示
验证ResBand在复杂动态场景下的学习能力
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs
from loitering_munition_dwa import LoiteringMunitionDWA

def simple_long_training_demo():
    """简洁的长期训练演示"""
    print("🚀 ResBand长期训练验证 - 重点关注复杂动态场景")
    print("=" * 60)
    
    # 初始化ResBand
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="long_training_results"
    )
    
    print(f"📊 训练配置:")
    print(f"   分辨率臂数: {len(resband.configs)}")
    print(f"   探索系数: {resband.c}")
    print(f"   场景特征学习: {'启用' if resband.use_scenario_feature_learning else '禁用'}")
    
    # 创建三种代表性场景
    scenarios = create_representative_scenarios()
    
    # 长期训练：每种场景50个episodes
    total_episodes = 150
    episode_count = 0
    
    # 记录统计数据
    stats = {
        'simple_static': {'success': [], 'rewards': [], 'arm_selections': []},
        'complex_static': {'success': [], 'rewards': [], 'arm_selections': []},
        'complex_dynamic': {'success': [], 'rewards': [], 'arm_selections': []}
    }
    
    print(f"\n🎯 开始长期训练 - 总计{total_episodes}个episodes")
    print("=" * 60)
    
    # 分阶段训练
    for stage_name, scenario in scenarios.items():
        print(f"\n📍 {stage_name} 阶段训练 (50 episodes)")
        print("-" * 40)
        
        stage_success_count = 0
        stage_rewards = []
        
        for episode in range(50):
            episode_count += 1
            
            # ResBand选择分辨率
            selected_config = resband.select_resolution(
                episode=episode_count,
                obstacles=scenario['obstacles'],
                current_state=scenario['current_state'],
                goal=scenario['goal'],
                bounds=scenario['bounds']
            )
            
            # 模拟训练性能
            episode_reward, critic_loss, violations, success = simulate_realistic_performance(
                stage_name, resband.current_arm, episode
            )
            
            # 更新ResBand
            resband.update_performance(episode_count, episode_reward, critic_loss, violations, success)
            
            # 记录统计
            stats[stage_name]['success'].append(success)
            stats[stage_name]['rewards'].append(episode_reward)
            stats[stage_name]['arm_selections'].append(resband.current_arm)
            
            if success:
                stage_success_count += 1
            stage_rewards.append(episode_reward)
            
            # 每10个episode输出一次进展
            if (episode + 1) % 10 == 0:
                recent_success_rate = sum(stats[stage_name]['success'][-10:]) / 10
                recent_avg_reward = np.mean(stats[stage_name]['rewards'][-10:])
                arm_distribution = np.bincount(stats[stage_name]['arm_selections'][-10:], minlength=3)
                
                print(f"   Episode {episode+1:2d}: 成功率={recent_success_rate:.1%}, "
                      f"平均奖励={recent_avg_reward:.0f}, "
                      f"臂选择=[{arm_distribution[0]},{arm_distribution[1]},{arm_distribution[2]}], "
                      f"当前臂={resband.current_arm}")
        
        # 阶段总结
        stage_success_rate = stage_success_count / 50
        stage_avg_reward = np.mean(stage_rewards)
        final_arm_distribution = np.bincount(stats[stage_name]['arm_selections'], minlength=3)
        
        print(f"\n   📊 {stage_name} 阶段总结:")
        print(f"      总成功率: {stage_success_rate:.1%}")
        print(f"      平均奖励: {stage_avg_reward:.0f}")
        print(f"      臂选择分布: [臂0:{final_arm_distribution[0]}, 臂1:{final_arm_distribution[1]}, 臂2:{final_arm_distribution[2]}]")
        print(f"      最终偏好臂: {np.argmax(final_arm_distribution)}")
    
    # 最终分析
    print(f"\n🏆 长期训练最终分析")
    print("=" * 60)
    
    # ResBand学习状态
    print(f"📈 ResBand最终状态:")
    print(f"   各臂总选择次数: {resband.N}")
    print(f"   各臂平均回报: {[f'{q:.0f}' for q in resband.Q]}")
    print(f"   学习到的特征映射: {len(resband.feature_resolution_mapping)}个")
    
    # 各场景性能对比
    print(f"\n📊 各场景最终性能对比:")
    for stage_name in scenarios.keys():
        final_success_rate = np.mean(stats[stage_name]['success'][-20:])  # 最后20次的成功率
        final_avg_reward = np.mean(stats[stage_name]['rewards'][-20:])
        preferred_arm = np.argmax(np.bincount(stats[stage_name]['arm_selections'][-20:], minlength=3))
        
        print(f"   {stage_name:15s}: 成功率={final_success_rate:.1%}, "
              f"平均奖励={final_avg_reward:.0f}, 偏好臂={preferred_arm}")
    
    # 学习曲线分析
    analyze_learning_curves(stats)
    
    # 复杂动态场景专项分析
    analyze_complex_dynamic_performance(stats['complex_dynamic'], resband)

def create_representative_scenarios():
    """创建三种代表性场景"""
    scenarios = {
        'simple_static': {
            'obstacles': [
                {'center': [600, 600, 100], 'radius': 30, 'motion_type': None}
            ],
            'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
            'goal': np.array([800, 800, 100]),
            'bounds': [1000, 1000, 200]
        },
        
        'complex_static': {
            'obstacles': [
                {'center': [550, 550, 100], 'radius': 35, 'motion_type': None},
                {'center': [600, 600, 100], 'radius': 40, 'motion_type': None},
                {'center': [650, 650, 100], 'radius': 30, 'motion_type': None},
                {'center': [700, 700, 100], 'radius': 38, 'motion_type': None}
            ],
            'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
            'goal': np.array([800, 800, 100]),
            'bounds': [1000, 1000, 200]
        },
        
        'complex_dynamic': {
            'obstacles': [
                {'center': [580, 580, 100], 'radius': 35, 'motion_type': 'linear',
                 'motion_params': {'velocity': [10, 8, 0], 'bounds': {'x': [400, 800], 'y': [400, 800], 'z': [50, 150]}}},
                {'center': [650, 650, 100], 'radius': 30, 'motion_type': 'circular',
                 'motion_params': {'center_orbit': [650, 650, 100], 'radius_orbit': 40, 'angular_speed': 0.3, 'phase': 0}}
            ],
            'current_state': np.array([500, 500, 100, 30.0, 0.0, 0.0]),
            'goal': np.array([800, 800, 100]),
            'bounds': [1000, 1000, 200]
        }
    }
    
    return scenarios

def simulate_realistic_performance(stage_name, selected_arm, episode_in_stage):
    """模拟真实性能，体现学习进展和分辨率适配"""
    
    # 学习进展因子 (0-1)
    learning_progress = min(episode_in_stage / 40.0, 1.0)
    
    # 不同场景的基础难度和最优分辨率
    stage_configs = {
        'simple_static': {
            'base_difficulty': 0.9,  # 相对简单
            'optimal_arm': 0,        # 粗分辨率最优
            'arm_factors': [1.0, 0.95, 0.85]  # 粗分辨率最好
        },
        'complex_static': {
            'base_difficulty': 0.7,  # 中等难度
            'optimal_arm': 1,        # 中等分辨率最优
            'arm_factors': [0.8, 1.0, 0.95]   # 中等分辨率最好
        },
        'complex_dynamic': {
            'base_difficulty': 0.5,  # 高难度
            'optimal_arm': 2,        # 精细分辨率最优
            'arm_factors': [0.6, 0.8, 1.0]    # 精细分辨率最好
        }
    }
    
    config = stage_configs[stage_name]
    
    # 基础成功概率
    base_success_prob = config['base_difficulty'] * config['arm_factors'][selected_arm]
    
    # 学习进展提升
    learned_success_prob = base_success_prob + learning_progress * 0.3
    learned_success_prob = min(learned_success_prob, 0.95)  # 最高95%成功率
    
    # 随机性
    success = np.random.random() < learned_success_prob
    
    # 奖励计算
    if success:
        base_reward = 25000 + learning_progress * 10000  # 25k -> 35k
        arm_bonus = config['arm_factors'][selected_arm] * 5000
        noise = np.random.normal(0, 2000)
        episode_reward = base_reward + arm_bonus + noise
    else:
        episode_reward = np.random.normal(15000, 3000)
    
    episode_reward = max(10000, episode_reward)
    
    # 其他指标
    critic_loss = np.random.normal(0.4 - learning_progress * 0.2, 0.1)
    violations = 0 if success else np.random.poisson(0.5)
    
    return episode_reward, critic_loss, violations, success

def analyze_learning_curves(stats):
    """分析学习曲线"""
    print(f"\n📈 学习曲线分析:")
    
    for stage_name, data in stats.items():
        # 计算滑动平均成功率
        success_rates = []
        window_size = 10
        
        for i in range(len(data['success']) - window_size + 1):
            window_success_rate = np.mean(data['success'][i:i+window_size])
            success_rates.append(window_success_rate)
        
        if success_rates:
            initial_success = success_rates[0]
            final_success = success_rates[-1]
            improvement = final_success - initial_success
            
            print(f"   {stage_name:15s}: 初始={initial_success:.1%} → 最终={final_success:.1%} "
                  f"(改进={improvement:+.1%})")

def analyze_complex_dynamic_performance(dynamic_stats, resband):
    """专项分析复杂动态场景性能"""
    print(f"\n🎯 复杂动态场景专项分析:")
    
    # 分阶段分析
    early_phase = dynamic_stats['success'][:20]
    late_phase = dynamic_stats['success'][-20:]
    
    early_success_rate = np.mean(early_phase)
    late_success_rate = np.mean(late_phase)
    
    print(f"   训练前期成功率: {early_success_rate:.1%}")
    print(f"   训练后期成功率: {late_success_rate:.1%}")
    print(f"   学习改进幅度: {late_success_rate - early_success_rate:+.1%}")
    
    # 分辨率选择演化
    early_arms = dynamic_stats['arm_selections'][:20]
    late_arms = dynamic_stats['arm_selections'][-20:]
    
    early_arm_dist = np.bincount(early_arms, minlength=3)
    late_arm_dist = np.bincount(late_arms, minlength=3)
    
    print(f"   训练前期臂选择: [臂0:{early_arm_dist[0]}, 臂1:{early_arm_dist[1]}, 臂2:{early_arm_dist[2]}]")
    print(f"   训练后期臂选择: [臂0:{late_arm_dist[0]}, 臂1:{late_arm_dist[1]}, 臂2:{late_arm_dist[2]}]")
    
    # 判断是否学会了正确的分辨率选择
    late_preferred_arm = np.argmax(late_arm_dist)
    expected_optimal_arm = 2  # 复杂动态场景期望选择精细分辨率
    
    if late_preferred_arm == expected_optimal_arm:
        print(f"   ✅ ResBand学会了选择最优分辨率 (臂{late_preferred_arm})")
    else:
        print(f"   ⚠️  ResBand偏好臂{late_preferred_arm}，期望臂{expected_optimal_arm}")
    
    # 最终结论
    if late_success_rate > 0.6:
        print(f"   🎉 复杂动态场景训练成功！最终成功率达到{late_success_rate:.1%}")
    elif late_success_rate > early_success_rate + 0.2:
        print(f"   📈 显著学习进展，成功率提升{late_success_rate - early_success_rate:.1%}")
    else:
        print(f"   🔧 需要进一步优化：可能需要更多训练时间或调整算法参数")

if __name__ == "__main__":
    simple_long_training_demo()
