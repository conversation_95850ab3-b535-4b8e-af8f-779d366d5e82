\relax 
\@writefile{toc}{\contentsline {section}{\numberline {1}引言}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}研究背景}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}问题分析}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}研究动机}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4}主要贡献}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}相关工作}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}动态窗口算法}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}强化学习在控制中的应用}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}多臂老虎机理论}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}元学习}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}三层自适应控制框架}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}框架概述}{3}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}三层协同机制}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}第一层：主动安全保障层}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.3.1}DWA-RL分层架构改进}{4}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces 改进的DWA-RL分层架构}}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.3.2}主动安全保障机制}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.4}第二层：智能分辨率选择层}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.4.1}ResBand算法设计}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.4.2}约束候选集合设计}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.5}第三层：元学习适应层}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.5.1}场景特征提取器}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.5.2}元学习适应机制}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4}问题定义}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}分辨率选择问题}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}分辨率配置空间}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3}控制量离散化}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.4}奖励函数}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}ResBand算法：DWA的智能分辨率选择模块}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}算法动机}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}算法描述}{8}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces Resolution Bandit (ResBand) 算法}}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3}UCB选择策略}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4}分辨率配置集合}{9}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces 分辨率配置集合}}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}改进的DWA-RL框架}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}框架架构}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}ResBand在DWA中的集成}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}完整框架伪代码}{10}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces 改进的DWA-RL框架（集成ResBand算法）}}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.4}关键组件详细描述}{11}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.1}DWA安全动作生成}{11}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {4}{\ignorespaces DWA安全动作生成（使用动态分辨率）}}{11}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.2}TD3动作选择}{12}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {5}{\ignorespaces TD3从安全动作集中选择最优动作}}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7}算法特性分析}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.1}收敛性}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.2}复杂度分析}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {7.3}安全-探索平衡}{12}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {8}场景感知的分阶段训练策略}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.1}场景特征提取与复杂度评估}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {8.1.1}多维场景特征}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {8.1.2}动态障碍物运动预测}{13}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {6}{\ignorespaces 动态障碍物位置预测}}{13}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {8.2}真正的元学习：基于特征的分辨率选择}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {8.2.1}候选集合约束设计}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {8.2.2}特征到分辨率的映射学习}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {9}实验验证}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.1}实验设计概述}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.2}实验1：主动安全保障验证}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.2.1}实验目标}{14}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.2.2}实验设置}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.2.3}评估指标}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.2.4}预期结果}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.3}实验2：ResBand算法验证}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.3.1}实验目标}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.3.2}实验设置}{15}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.3.3}评估指标}{16}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.4}实验3：MLACF框架验证}{16}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.4.1}实验目标}{16}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.4.2}实验设置}{16}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.4.3}评估指标}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.5}实验4：综合性能验证}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.5.1}实验目标}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.5.2}实验设置}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.5.3}评估指标}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.6}实验设置}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.6.1}仿真环境配置}{17}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.6.2}算法参数设置}{18}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces 算法参数设置（基于代码实现）}}{18}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.7}对比实验设计}{18}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.8}评估指标体系}{19}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {9.9}实验结果与分析}{19}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.1}整体性能对比}{19}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {3}{\ignorespaces 三层框架综合性能对比（基于代码实现）}}{19}{}\protected@file@percent }
\newlabel{tab:comprehensive_comparison}{{3}{19}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4}{\ignorespaces 训练性能对比结果（基于代码实现）}}{20}{}\protected@file@percent }
\newlabel{tab:performance_comparison}{{4}{20}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.2}元学习能力验证}{20}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5}{\ignorespaces 元学习能力验证结果（基于代码实现）}}{20}{}\protected@file@percent }
\newlabel{tab:meta_learning}{{5}{20}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.3}"伪复杂性"场景识别验证}{21}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {6}{\ignorespaces "伪复杂性"场景识别结果（基于代码实现）}}{21}{}\protected@file@percent }
\newlabel{tab:pseudo_complexity}{{6}{21}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.4}动态障碍物预测验证}{21}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {7}{\ignorespaces 动态障碍物预测效果验证（基于代码实现）}}{22}{}\protected@file@percent }
\newlabel{tab:dynamic_prediction}{{7}{22}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.5}动态环境适应性验证}{22}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces ResBand算法在动态环境中的适应性表现}}{23}{}\protected@file@percent }
\newlabel{fig:dynamic_adaptation}{{1}{23}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.6}分辨率选择策略分析}{23}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces ResBand算法在不同训练阶段的分辨率选择}}{24}{}\protected@file@percent }
\newlabel{fig:resolution_selection}{{2}{24}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.7}UCB收敛性分析}{24}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces 不同分辨率配置的UCB值收敛过程}}{25}{}\protected@file@percent }
\newlabel{fig:ucb_convergence}{{3}{25}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {9.9.8}训练过程动态分析}{25}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces 不同方法的训练奖励曲线对比}}{26}{}\protected@file@percent }
\newlabel{fig:reward_curves}{{4}{26}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {9.10}消融实验}{26}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {8}{\ignorespaces 消融实验结果}}{26}{}\protected@file@percent }
\newlabel{tab:ablation_study}{{8}{26}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {9.11}统计显著性分析}{27}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {9}{\ignorespaces 统计显著性分析结果（p值）}}{27}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {10}讨论}{27}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.1}双层自适应框架的创新价值}{27}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.1}算法层创新：ResBand智能学习vs规则映射}{27}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.2}训练层创新：场景感知的分阶段训练}{28}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.3}元学习机制的理论意义}{28}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.4}计算效率的革命性提升}{28}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.5}安全性的有效保障}{29}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.6}理论基础的坚实性}{29}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.1.7}动态环境泛化能力}{29}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.2}算法创新性分析}{30}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.2.1}首次将多臂老虎机应用于分辨率选择}{30}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.2.2}元学习框架的构建}{30}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.3}局限性分析}{30}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.3.1}配置空间的离散性}{30}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.3.2}参数敏感性}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.3.3}计算开销分析}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.4}与现有方法的对比分析}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.4.1}相比传统固定分辨率方法}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.4.2}相比启发式调度方法}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.5}未来研究方向}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.5.1}算法扩展}{31}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.5.2}应用扩展}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.5.3}理论深化}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {10.6}工程应用前景}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.6.1}巡飞弹控制系统}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {10.6.2}其他无人系统}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {11}结论}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.1}主要贡献总结}{32}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.2}核心创新贡献}{33}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {11.2.1}算法层创新}{33}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {11.2.2}训练层创新}{33}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.3}性能突破总结}{33}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.4}实验验证成果}{34}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {11.4.1}最新突破性验证成果}{34}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.5}理论贡献}{35}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.6}工程应用价值}{35}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.7}局限性认识}{35}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.8}未来工作展望}{36}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {11.9}总体评价}{36}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {12}流程图}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {12.1}三层自适应控制框架架构图}{37}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces 三层自适应控制框架整体架构}}{37}{}\protected@file@percent }
\newlabel{fig:three_layer_framework}{{5}{37}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {12.2}整体框架流程图}{37}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces ResBand增强的DWA-RL框架整体架构}}{37}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {12.3}详细训练流程图}{38}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces ResBand增强DWA-RL框架详细训练流程}}{38}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {12.4}分辨率选择决策流程图}{39}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces ResBand分辨率选择决策流程}}{39}{}\protected@file@percent }
\gdef \@abspage@last{39}
