# 文件保存结构统一化更新

## 更新时间
2025-08-22

## 更新目标
将所有训练和测试生成的文件统一保存到 `results/` 目录中，提高文件组织性和管理便利性。

## 新的目录结构

```
巡飞简化ver/
├── results/                           # 统一结果目录
│   ├── training/                      # 训练相关文件
│   │   └── loitering_munition_staged_training_{timestamp}/
│   │       ├── models/                # 训练好的模型文件 (.pth)
│   │       ├── data/                  # 训练数据和统计信息 (.pkl, .json)
│   │       ├── plots/                 # 训练过程图表 (.png)
│   │       └── logs/                  # 训练日志文件
│   ├── gifs/                          # 生成的动画文件
│   │   ├── stage1/                    # 阶段1测试动画
│   │   ├── stage2/                    # 阶段2测试动画
│   │   └── stage3/                    # 阶段3测试动画
│   ├── models/                        # 最终模型文件
│   ├── logs/                          # 系统日志
│   ├── analysis/                      # 分析结果文件
│   └── README.md                      # 目录结构说明
└── [其他核心代码文件...]
```

## 修改的文件

### 1. staged_training_framework.py
- **输出目录**: 从当前目录改为 `results/training/loitering_munition_staged_training_{timestamp}/`
- **子目录结构**: 自动创建 `models/`, `data/`, `plots/`, `logs/` 子目录
- **文件保存路径**:
  - 模型文件: `models/stage_{N}_model_{timestamp}.pth`
  - 训练数据: `data/staged_training_data.pkl`
  - 训练报告: `data/staged_training_results.json`
  - 轨迹图: `plots/{phase}_episode_{num}_3d_trajectory.png`
  - 总结图: `plots/stage_{N}_training_summary.png`

### 2. test_training_gif_generator.py
- **GIF文件**: 保存到 `results/gifs/stage{N}/`
- **分析图表**: 保存到 `results/analysis/`
- **文件命名**:
  - GIF: `loitering_munition_stage{N}_{SUCCESS/FAILED}_{timestamp}.gif`
  - 分析图: `loitering_munition_analysis_{timestamp}.png`
  - 约束图: `loitering_munition_constraints_{timestamp}.png`

### 3. test_stage1-3.py
- **更新提示信息**: 显示新的文件保存路径
- **输出提示**:
  - `results/gifs/stage{N}/loitering_munition_stage{N}_*.gif`
  - `results/analysis/loitering_munition_analysis_*.png`
  - `results/analysis/loitering_munition_constraints_*.png`

### 4. README.md
- **添加文件保存结构说明**
- **更新目录结构图**
- **说明各子目录用途**

## 文件命名规范

### 训练文件
- 模型: `stage{N}_model_{timestamp}.pth`
- 数据: `staged_training_data.pkl`
- 报告: `staged_training_results.json`
- 图表: `{phase}_episode_{num}_3d_trajectory.png`

### 测试文件
- GIF: `loitering_munition_stage{N}_{SUCCESS/FAILED}_{timestamp}.gif`
- 分析: `loitering_munition_analysis_{timestamp}.png`
- 约束: `loitering_munition_constraints_{timestamp}.png`

## 使用方法

### 运行训练
```bash
python run_staged_training.py
# 文件将保存到: results/training/loitering_munition_staged_training_{timestamp}/
```

### 运行测试
```bash
python test_stage1.py
# 文件将保存到: results/gifs/stage1/ 和 results/analysis/
```

### 生成GIF
```bash
python test_training_gif_generator.py --stage 1
# 文件将保存到: results/gifs/stage1/ 和 results/analysis/
```

## 优势

1. **统一管理**: 所有生成文件集中在results目录
2. **分类清晰**: 按功能分类到不同子目录
3. **易于查找**: 标准化的命名规范
4. **便于备份**: 只需备份results目录即可
5. **避免混乱**: 避免在根目录生成大量文件

## 兼容性

- 保留了原有的 `loitering_munition_staged_training_20250727_172938/` 目录作为历史数据
- 所有核心功能保持不变，只是改变了文件保存位置
- 向后兼容，不影响现有的训练模型和数据

## 验证结果

✅ staged_training_framework 导入成功
✅ test_training_gif_generator 导入成功  
✅ 目录结构自动创建成功
✅ 子目录结构正确创建
✅ 文件路径更新完成
