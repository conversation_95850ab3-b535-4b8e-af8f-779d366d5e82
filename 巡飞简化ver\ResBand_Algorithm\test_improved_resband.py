"""
改进ResBand算法测试脚本
验证算法是否正常工作
"""

import numpy as np
import time
import os
import sys
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resolution_bandit import ResolutionBandit, create_paper_configs
from integrated_resband_training import IntegratedResBandTrainer

def test_resband_algorithm():
    """测试ResBand算法基本功能"""
    print("🧪 测试ResBand算法基本功能")
    print("=" * 50)
    
    # 创建ResBand算法实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=10,
        reward_weights=(0.6, 0.2, 0.1),
        adaptive_exploration=True,
        performance_threshold=0.5
    )
    
    print("✅ ResBand算法创建成功")
    
    # 测试分辨率选择
    for episode in range(30):
        # 模拟训练指标
        training_metrics = {
            'success_rate': np.random.uniform(0.3, 0.8),
            'avg_reward': np.random.uniform(-50, 50),
            'critic_loss': np.random.uniform(0.1, 2.0),
            'violation_rate': np.random.uniform(0.0, 0.3)
        }
        
        # 选择分辨率
        resolution = resband.select_resolution(episode, training_metrics)
        
        # 模拟性能更新
        episode_reward = np.random.uniform(-100, 100)
        critic_loss = np.random.uniform(0.1, 2.0)
        violation_count = np.random.randint(0, 5)
        success = np.random.choice([True, False], p=[0.6, 0.4])
        
        resband.update_performance(
            episode, episode_reward, critic_loss, violation_count,
            success=success, training_metrics=training_metrics
        )
        
        if episode % 10 == 0:
            print(f"Episode {episode}: 选择分辨率 {resolution.name}, 探索系数: {resband.c:.3f}")
    
    # 获取训练摘要
    summary = resband.get_training_summary()
    print(f"\n📊 ResBand训练摘要:")
    print(f"   总episodes: {summary['total_episodes']}")
    print(f"   自适应次数: {summary['adaptation_count']}")
    print(f"   最优臂: {summary['best_arm']}")
    print(f"   最终探索系数: {summary['current_exploration_coefficient']:.3f}")
    
    # 保存结果
    resband.save_results("test_resband_results.json")
    print("✅ ResBand算法测试完成")
    
    return resband

def test_integrated_trainer():
    """测试集成训练器"""
    print("\n🧪 测试集成训练器")
    print("=" * 50)
    
    # 创建快速测试配置
    stage_configs = {
        'stage1': {
            'name': '简单环境',
            'episodes': 5,  # 很少的episodes用于快速测试
            'obstacle_count': 2,
            'complexity': 'simple'
        },
        'stage2': {
            'name': '中等环境',
            'episodes': 5,
            'obstacle_count': 3,
            'complexity': 'medium'
        }
    }
    
    # ResBand配置
    resband_config = {
        'exploration_coefficient': 2.0,
        'stage_length': 5,
        'reward_weights': (0.6, 0.2, 0.1),
        'adaptive_exploration': True,
        'performance_threshold': 0.5,
        'use_fast_configs': True
    }
    
    try:
        # 创建集成训练器
        trainer = IntegratedResBandTrainer(
            use_resband=True,
            resband_config=resband_config,
            output_dir="results/test_integrated",
            stage_configs=stage_configs
        )
        
        print("✅ 集成训练器创建成功")
        
        # 运行快速训练（只训练几个episode）
        print("🚀 开始快速测试训练...")
        results = trainer.run_training()
        
        print(f"\n✅ 集成训练器测试完成!")
        print(f"🎯 总体成功率: {results['overall_success_rate']:.1%}")
        print(f"📊 总体平均奖励: {results['overall_avg_reward']:.2f}")
        
        return trainer, results
        
    except Exception as e:
        print(f"❌ 集成训练器测试失败: {str(e)}")
        return None, None

def test_configuration_files():
    """测试配置文件生成"""
    print("\n🧪 测试配置文件生成")
    print("=" * 50)
    
    # 测试ResBand配置
    resband_config = {
        "exploration_coefficient": 2.0,
        "stage_length": 15,
        "reward_weights": [0.6, 0.2, 0.1],
        "adaptive_exploration": True,
        "performance_threshold": 0.5,
        "description": "测试配置"
    }
    
    config_file = "test_resband_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        import json
        json.dump(resband_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ ResBand配置文件已创建: {config_file}")
    
    # 测试分阶段配置
    stage_config = {
        "stage1": {
            "name": "简单环境",
            "episodes": 50,
            "obstacle_count": 3,
            "complexity": "simple"
        },
        "stage2": {
            "name": "中等环境",
            "episodes": 75,
            "obstacle_count": 6,
            "complexity": "medium"
        }
    }
    
    stage_file = "test_stage_config.json"
    with open(stage_file, 'w', encoding='utf-8') as f:
        import json
        json.dump(stage_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 分阶段配置文件已创建: {stage_file}")
    
    return config_file, stage_file

def main():
    """主测试函数"""
    print("🚀 开始改进ResBand算法测试")
    print("=" * 60)
    
    start_time = time.time()
    
    # 测试1: ResBand算法基本功能
    try:
        resband = test_resband_algorithm()
        print("✅ 测试1通过")
    except Exception as e:
        print(f"❌ 测试1失败: {str(e)}")
        return
    
    # 测试2: 配置文件生成
    try:
        config_file, stage_file = test_configuration_files()
        print("✅ 测试2通过")
    except Exception as e:
        print(f"❌ 测试2失败: {str(e)}")
        return
    
    # 测试3: 集成训练器（可选，需要环境支持）
    print("\n⚠️  测试3需要完整的环境支持，跳过...")
    print("   如需完整测试，请运行: python run_resband.py --mode integrated --fast-mode")
    
    # 清理测试文件
    try:
        if os.path.exists("test_resband_results.json"):
            os.remove("test_resband_results.json")
        if os.path.exists("test_resband_config.json"):
            os.remove("test_resband_config.json")
        if os.path.exists("test_stage_config.json"):
            os.remove("test_stage_config.json")
        print("✅ 测试文件清理完成")
    except:
        pass
    
    total_time = time.time() - start_time
    print(f"\n🎉 所有测试完成! 总耗时: {total_time:.1f}秒")
    print("\n📋 测试结果:")
    print("✅ ResBand算法基本功能正常")
    print("✅ 配置文件生成功能正常")
    print("⚠️  集成训练器需要完整环境支持")
    print("\n💡 建议:")
    print("1. 运行完整测试: python run_resband.py --mode integrated --fast-mode")
    print("2. 查看使用示例: python run_resband.py --help-examples")
    print("3. 创建配置文件: python run_resband.py --create-config")

if __name__ == "__main__":
    main()
