"""
ResBand算法训练器 - 改进版本
完整的ResBand算法训练和测试框架，与强化学习训练深度融合
"""

import numpy as np
import torch
import time
import os
import json
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Tuple

from resolution_bandit import ResolutionBandit, create_paper_configs
from loitering_munition_dwa import LoiteringMunitionDWA
from simple_environment import SimpleLoiteringMunitionEnvironment
from simple_td3 import StabilizedTD3Controller

class ResBandTrainer:
    """ResBand算法训练器 - 改进版本"""
    
    def __init__(self, 
                 use_resband=True,
                 resband_config=None,
                 output_dir="results/resband_training"):
        """
        初始化训练器
        
        Args:
            use_resband: 是否使用ResBand算法
            resband_config: ResBand配置
            output_dir: 输出目录
        """
        self.use_resband = use_resband
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建输出子目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = os.path.join(output_dir, f"resband_run_{self.timestamp}")
        os.makedirs(self.run_dir, exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "models"), exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "plots"), exist_ok=True)
        os.makedirs(os.path.join(self.run_dir, "data"), exist_ok=True)
        
        # TD3配置（与分阶段训练一致）
        self.td3_config = {
            'state_dim': 15,  # 与分阶段训练一致的观测维度
            'action_dim': 3,  # [a_T, a_N, μ]
            'max_action': 1.0,
            'lr': 3e-4,
            'batch_size': 256,  # 增大批次大小
            'gamma': 0.99,
            'tau': 0.005,
            'policy_noise': 0.2,
            'noise_clip': 0.5,
            'policy_freq': 2,
            'buffer_size': 1000000,  # 增大缓冲区
            'hidden_dim': 256
        }
        
        # 初始化ResBand算法
        if self.use_resband:
            if resband_config is None:
                resband_config = {
                    'exploration_coefficient': 2.0,
                    'stage_length': 5,  # 更短的阶段长度，让ResBand更频繁地调整
                    'reward_weights': (0.6, 0.2, 0.1),  # 修正为3个权重
                    'adaptive_exploration': True,
                    'performance_threshold': 0.3  # 更低的阈值，更容易触发调整
                }
            
            # 检查是否使用快速配置
            use_fast = resband_config.get('use_coarse_resolution', False)

            self.resband = ResolutionBandit(
                configs=None,  # 让ResolutionBandit自动选择配置
                exploration_coefficient=resband_config['exploration_coefficient'],
                stage_length=resband_config['stage_length'],
                reward_weights=resband_config['reward_weights'],
                output_dir=self.run_dir,
                use_fast_configs=use_fast,
                adaptive_exploration=resband_config.get('adaptive_exploration', True),
                performance_threshold=resband_config.get('performance_threshold', 0.5)
            )
            print(f"🎰 ResBand算法已启用（改进版本）")
            print(f"   阶段长度: {resband_config['stage_length']} episodes")
            print(f"   探索系数: {resband_config['exploration_coefficient']}")
            print(f"   性能阈值: {resband_config.get('performance_threshold', 0.5)}")
        else:
            self.resband = None
            print(f"⚙️ 使用固定分辨率配置")
        
        # 初始化环境
        self.env = SimpleLoiteringMunitionEnvironment()
        
        # 初始化DWA控制器
        if self.use_resband:
            # 获取初始分辨率配置
            resolution_config = self.resband.select_resolution(0)
            print(f"🔧 初始DWA分辨率: {resolution_config.name}")
            self.dwa = LoiteringMunitionDWA(dt=0.1)
            # 立即更新DWA分辨率
            self.dwa.update_resolution(resolution_config)
        else:
            self.dwa = LoiteringMunitionDWA(dt=0.1)
        
        # 初始化TD3控制器
        self.controller = StabilizedTD3Controller(self.td3_config)
        
        # 训练记录
        self.training_history = {
            'episodes': [],
            'rewards': [],
            'successes': [],
            'violations': [],
            'resolutions': [],
            'training_metrics': []  # 新增：训练指标记录
        }
        
        # 新增：实时训练指标跟踪
        self.current_training_metrics = {
            'success_rate': 0.0,
            'avg_reward': 0.0,
            'critic_loss': 0.0,
            'violation_rate': 0.0
        }
        
        print(f"🚀 ResBand训练器初始化完成（改进版本）")
        print(f"📁 输出目录: {self.run_dir}")
    
    def _update_training_metrics(self, episode_num: int):
        """更新训练指标"""
        if len(self.training_history['rewards']) > 0:
            # 计算最近10个episode的指标
            window = min(10, len(self.training_history['rewards']))
            recent_rewards = self.training_history['rewards'][-window:]
            recent_successes = self.training_history['successes'][-window:]
            recent_violations = self.training_history['violations'][-window:]
            
            self.current_training_metrics = {
                'success_rate': np.mean(recent_successes),
                'avg_reward': np.mean(recent_rewards),
                'critic_loss': self.controller.get_last_critic_loss(),
                'violation_rate': np.mean(recent_violations) / 100.0  # 归一化违反率
            }
    
    def train_episode(self, episode_num):
        """训练单个episode - 改进版本"""
        # 更新训练指标
        self._update_training_metrics(episode_num)
        
        # ResBand: 更新分辨率配置（传入训练指标）
        if self.use_resband:
            # 记录当前分辨率
            old_resolution = self.dwa.get_current_resolution()
            
            # 选择新分辨率
            resolution_config = self.resband.select_resolution(
                episode_num, 
                training_metrics=self.current_training_metrics
            )
            
            # 更新DWA分辨率
            self.dwa.update_resolution(resolution_config)
            
            # 检查分辨率是否真的改变了
            new_resolution = self.dwa.get_current_resolution()
            if (old_resolution['a_T_resolution'] != new_resolution['a_T_resolution'] or
                old_resolution['a_N_resolution'] != new_resolution['a_N_resolution'] or
                old_resolution['mu_resolution'] != new_resolution['mu_resolution']):
                print(f"🔄 Episode {episode_num}: 分辨率已更新 - {resolution_config.name}")
            else:
                print(f"📊 Episode {episode_num}: 分辨率保持不变 - {resolution_config.name}")
        
        # 重置环境
        observation = self.env.reset()  # 现在返回15维观测
        state = self.env.state  # 获取6维状态用于DWA
        episode_reward = 0
        episode_success = False
        episode_violations = 0
        trajectory = []

        while True:
            # 记录轨迹
            trajectory.append(state[:3].copy())
            
            # DWA生成安全动作集
            safe_controls = self.dwa.generate_safe_control_set(
                state, 
                self.env.get_obstacles(), 
                self.env.get_goal(),
                max_actions=10
            )
            
            # 选择动作
            if safe_controls:
                # 将安全控制转换为归一化动作
                safe_actions = [self.dwa.get_normalized_action(control) for control in safe_controls]
                action = self.controller.select_best_action_from_safe_set(observation, safe_actions)  # 使用观测
            else:
                # 如果没有安全动作，使用默认动作
                action = np.array([-0.5, 0.0, 0.0])
            
            # 将归一化动作转换为实际控制输入
            control_input = np.array([
                action[0] * self.env.a_T_max,
                action[1] * self.env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            # 执行动作
            next_observation, reward, done, info = self.env.step(control_input)
            next_state = self.env.state  # 获取6维状态
            episode_reward += reward

            # 记录违反次数
            if info.get('collision', False) or info.get('out_of_bounds', False):
                episode_violations += 1

            if info.get('success', False):
                episode_success = True

            # 存储经验（使用15维观测）
            self.controller.replay_buffer.add(
                observation.copy(),
                action.copy(),
                reward,
                next_observation.copy(),
                done
            )

            # 训练更新
            self.controller.immediate_update(batch_size=self.td3_config['batch_size'])

            if done:
                break

            observation = next_observation
            state = next_state
        
        # ResBand: 更新性能指标（传入训练指标）
        if self.use_resband:
            critic_loss = self.controller.get_last_critic_loss()
            self.resband.update_performance(
                episode_num, 
                episode_reward, 
                critic_loss, 
                episode_violations,
                success=episode_success,
                training_metrics=self.current_training_metrics
            )
        
        # 记录训练历史
        self.training_history['episodes'].append(episode_num)
        self.training_history['rewards'].append(episode_reward)
        self.training_history['successes'].append(episode_success)
        self.training_history['violations'].append(episode_violations)
        self.training_history['training_metrics'].append(self.current_training_metrics.copy())
        
        if self.use_resband:
            current_resolution = self.dwa.get_current_resolution()
            self.training_history['resolutions'].append({
                'episode': episode_num,
                'a_T': current_resolution['a_T_resolution'],
                'a_N': current_resolution['a_N_resolution'],
                'mu': current_resolution['mu_resolution'],
                'exploration_coefficient': self.resband.c if self.resband else None
            })
        
        return episode_reward, episode_success, episode_violations, trajectory
    
    def train(self, num_episodes=200, save_interval=50, plot_interval=20):
        """完整训练过程 - 改进版本"""
        print(f"\n🚀 开始ResBand训练（改进版本）")
        print(f"📊 总episodes: {num_episodes}")
        print(f"💾 保存间隔: {save_interval}")
        print(f"📈 绘图间隔: {plot_interval}")
        print("-" * 60)
        
        start_time = time.time()
        success_count = 0
        
        for episode in range(num_episodes):
            # 训练episode
            reward, success, violations, trajectory = self.train_episode(episode)
            
            if success:
                success_count += 1

            # 每个episode都输出奖励信息
            success_rate = success_count / (episode + 1)
            status = "✅成功" if success else "❌失败"
            
            # 显示ResBand相关信息
            resband_info = ""
            if self.use_resband and self.resband:
                current_resolution = self.dwa.get_current_resolution()
                resband_info = f", ResBand探索系数: {self.resband.c:.3f}"
                resolution_info = f" (a_T={current_resolution['a_T_resolution']:.1f}, a_N={current_resolution['a_N_resolution']:.1f}, μ={current_resolution['mu_resolution']:.2f})"
            else:
                resolution_info = ""
            
            print(f"Episode {episode+1:3d}/{num_episodes}: "
                  f"奖励 {reward:7.1f}, {status}, 违反 {violations}, "
                  f"成功率 {success_rate:.1%}{resband_info}{resolution_info}")

            # 每10个episode输出详细统计
            if (episode + 1) % 10 == 0:
                avg_reward = np.mean(self.training_history['rewards'][-10:])
                print(f"📊 最近10个episode平均奖励: {avg_reward:7.1f}")
                
                # 新增：显示ResBand训练摘要
                if self.use_resband and self.resband:
                    summary = self.resband.get_training_summary()
                    print(f"🎰 ResBand摘要: 总episodes={summary['total_episodes']}, "
                          f"自适应次数={summary['adaptation_count']}, "
                          f"最优臂={summary['best_arm']}")
                
                print("-" * 50)
            
            # 定期保存
            if (episode + 1) % save_interval == 0:
                self.save_checkpoint(episode + 1)
            
            # 定期绘图
            if (episode + 1) % plot_interval == 0:
                self.plot_training_progress(episode + 1)
        
        # 训练完成
        training_time = time.time() - start_time
        final_success_rate = success_count / num_episodes
        
        # 计算最终指标
        final_avg_reward = np.mean(self.training_history['rewards'])
        total_violations = sum(self.training_history['violations'])
        
        # 保存最终结果
        results = {
            'success_rate': final_success_rate,
            'final_avg_reward': final_avg_reward,
            'total_violations': total_violations,
            'training_time': training_time,
            'training_history': self.training_history
        }
        
        # 保存ResBand结果
        if self.use_resband and self.resband:
            self.resband.save_results()
            self.resband.plot_results()
            results['resband_summary'] = self.resband.get_training_summary()
        
        # 保存训练结果
        self.save_final_results(results)
        
        print(f"\n✅ 训练完成!")
        print(f"🎯 最终成功率: {final_success_rate:.1%}")
        print(f"⏱️  训练时间: {training_time:.1f}秒")
        print(f"📊 最终平均奖励: {final_avg_reward:.2f}")
        print(f"🚫 总违反次数: {total_violations}")
        
        if self.use_resband and self.resband:
            summary = self.resband.get_training_summary()
            print(f"🎰 ResBand最终摘要:")
            print(f"   总episodes: {summary['total_episodes']}")
            print(f"   自适应调整次数: {summary['adaptation_count']}")
            print(f"   最优分辨率配置: Arm {summary['best_arm']}")
            print(f"   最终探索系数: {summary['current_exploration_coefficient']:.3f}")
        
        return self, results
    
    def save_checkpoint(self, episode):
        """保存检查点"""
        # 保存模型
        model_path = os.path.join(self.run_dir, "models", f"model_episode_{episode}.pth")
        self.controller.save(model_path)
        
        # 保存训练历史
        history_path = os.path.join(self.run_dir, "data", f"training_history_episode_{episode}.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        
        print(f"💾 检查点已保存: Episode {episode}")
    
    def save_final_results(self, results):
        """保存最终结果"""
        # 保存训练历史
        history_path = os.path.join(self.run_dir, "data", "final_training_history.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        
        # 保存ResBand结果
        if self.use_resband and self.resband:
            self.resband.save_results()
            self.resband.plot_results()
        
        # 保存最终模型
        final_model_path = os.path.join(self.run_dir, "models", "final_model.pth")
        self.controller.save(final_model_path)
        
        print(f"📁 最终结果已保存到: {self.run_dir}")
    
    def plot_training_progress(self, episode):
        """绘制训练进度"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'ResBand训练进度 (Episode {episode})', fontsize=16)
            
            # 1. 奖励曲线
            episodes = self.training_history['episodes']
            rewards = self.training_history['rewards']
            
            axes[0, 0].plot(episodes, rewards, 'b-', alpha=0.7)
            axes[0, 0].set_title('奖励曲线')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('奖励')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 成功率
            successes = self.training_history['successes']
            success_rates = []
            for i in range(len(successes)):
                success_rates.append(sum(successes[:i+1]) / (i+1))
            
            axes[0, 1].plot(episodes, success_rates, 'g-', alpha=0.7)
            axes[0, 1].set_title('成功率')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('成功率')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 违反次数
            violations = self.training_history['violations']
            axes[1, 0].plot(episodes, violations, 'r-', alpha=0.7)
            axes[1, 0].set_title('约束违反次数')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('违反次数')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 分辨率变化（如果使用ResBand）
            if self.use_resband and self.training_history['resolutions']:
                resolutions = self.training_history['resolutions']
                res_episodes = [r['episode'] for r in resolutions]
                res_a_T = [r['a_T'] for r in resolutions]
                res_a_N = [r['a_N'] for r in resolutions]
                res_mu = [r['mu'] for r in resolutions]
                
                axes[1, 1].plot(res_episodes, res_a_T, 'b-', label='a_T', alpha=0.7)
                axes[1, 1].plot(res_episodes, res_a_N, 'g-', label='a_N', alpha=0.7)
                axes[1, 1].plot(res_episodes, res_mu, 'r-', label='μ', alpha=0.7)
                axes[1, 1].set_title('分辨率变化')
                axes[1, 1].set_xlabel('Episode')
                axes[1, 1].set_ylabel('分辨率')
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            plot_path = os.path.join(self.run_dir, "plots", f"training_progress_episode_{episode}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📊 训练进度图已保存: Episode {episode}")
            
        except Exception as e:
            print(f"⚠️ 绘图失败: {e}")
    
    def test_episode(self, scenario_data=None):
        """测试单个episode"""
        state = self.env.reset(scenario_data)
        trajectory = []
        episode_reward = 0
        episode_success = False
        
        while True:
            trajectory.append(state[:3].copy())
            
            # 使用当前分辨率生成安全动作集
            safe_controls = self.dwa.generate_safe_control_set(
                state,
                self.env.get_obstacles(),
                self.env.get_goal(),
                max_actions=10
            )
            
            if safe_controls:
                safe_actions = [self.dwa.get_normalized_action(control) for control in safe_controls]
                action = self.controller.select_best_action_from_safe_set(state, safe_actions)
            else:
                action = np.array([-0.5, 0.0, 0.0])
            
            control_input = np.array([
                action[0] * self.env.a_T_max,
                action[1] * self.env.a_N_max,
                action[2] * (np.pi/2)
            ])
            
            next_state, reward, done, info = self.env.step(control_input)
            episode_reward += reward
            
            if info.get('success', False):
                episode_success = True
            
            if done:
                break
            
            state = next_state
        
        return episode_reward, episode_success, trajectory
    
    def run_test(self, num_test_episodes=50):
        """运行测试"""
        print(f"\n🧪 开始测试 ({num_test_episodes} episodes)")
        
        test_results = {
            'episodes': [],
            'rewards': [],
            'successes': [],
            'trajectories': []
        }
        
        success_count = 0
        
        for episode in range(num_test_episodes):
            reward, success, trajectory = self.test_episode()
            
            if success:
                success_count += 1
            
            test_results['episodes'].append(episode)
            test_results['rewards'].append(reward)
            test_results['successes'].append(success)
            test_results['trajectories'].append(trajectory)
            
            if (episode + 1) % 10 == 0:
                success_rate = success_count / (episode + 1)
                print(f"测试 Episode {episode+1:3d}/{num_test_episodes}: "
                      f"奖励 {reward:7.1f}, 成功率 {success_rate:.1%}")
        
        final_success_rate = success_count / num_test_episodes
        avg_reward = np.mean(test_results['rewards'])
        
        print(f"\n✅ 测试完成!")
        print(f"🎯 测试成功率: {final_success_rate:.1%}")
        print(f"📊 平均奖励: {avg_reward:.2f}")
        
        # 保存测试结果
        test_results_path = os.path.join(self.run_dir, "data", "test_results.json")
        with open(test_results_path, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        return test_results
