"""
测试导入模块
"""

try:
    print("Testing imports...")
    
    import numpy as np
    print("✅ numpy imported")
    
    import torch
    print("✅ torch imported")
    
    from resolution_bandit import ResolutionBandit
    print("✅ resolution_bandit imported")
    
    from loitering_munition_dwa import LoiteringMunitionDWA
    print("✅ loitering_munition_dwa imported")
    
    from loitering_munition_environment import LoiteringMunitionEnvironment
    print("✅ loitering_munition_environment imported")
    
    from td3_network import StabilizedTD3Controller
    print("✅ td3_network imported")
    
    from staged_training_framework import LoiteringMunitionStagedTrainer
    print("✅ staged_training_framework imported")
    
    print("🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
