"""
巡飞弹六自由度运动学环境 - 融合版本
结合巡飞714ver的运动模型和简化ver1的训练框架接口
"""

import numpy as np
import random
import math
import copy

class LoiteringMunitionEnvironment:
    """巡飞弹六自由度运动学环境 - 融合版本"""
    
    def __init__(self, bounds=[2000, 2000, 200], environment_config=None, reward_type='simplified'):
        self.bounds = np.array(bounds)  # [x_max, y_max, z_max]
        self.environment_config = environment_config or {}
        self.reward_type = reward_type
        
        # 巡飞弹物理参数
        self.g = 9.81  # 重力加速度
        self.dt = 0.1  # 时间步长
        
        # 运动约束参数（论文中的参数）
        self.V_min = 15.0    # 最小速度（失速速度）
        self.V_max = 60.0    # 最大速度
        self.a_T_max = 8.0   # 最大切向加速度
        self.a_N_max = 39.24 # 最大法向加速度（4g）
        self.gamma_max = np.pi/3  # 最大航迹倾斜角（60°）
        self.d_safe = 2.0    # 最小安全距离
        
        # 训练参数
        self.max_steps = 2000
        self.step_count = 0
        
        # 初始化环境
        self.reset()
    
    def reset(self, scenario_data=None):
        """重置环境"""
        self.step_count = 0
        
        if scenario_data is not None:
            # 使用指定场景数据
            self._load_scenario(scenario_data)
        else:
            # 生成新场景
            self._generate_scenario()
        
        # 初始化状态 [x, y, z, V, gamma, psi] - 使用智能初始化
        self.state = self._get_intelligent_initial_state()
        
        # 记录初始目标距离
        self._prev_goal_dist = np.linalg.norm(self.state[:3] - self.goal)
        
        return self._get_observation()

    def _get_intelligent_initial_state(self):
        """获取智能的初始状态设置 - 借鉴巡飞714ver的设计"""
        V_cruise = 25.0  # 巡航速度
        gamma_max = np.pi/3  # 最大倾斜角（60°）

        # 计算初始朝向（指向目标）
        direction = self.goal - self.start
        distance = np.linalg.norm(direction)

        if distance > 0:
            # 计算初始偏航角和倾斜角
            psi_initial = np.arctan2(direction[1], direction[0])
            gamma_initial = np.arcsin(direction[2] / distance)
            # 限制倾斜角范围
            gamma_initial = np.clip(gamma_initial, -gamma_max, gamma_max)
        else:
            psi_initial = 0.0
            gamma_initial = 0.0

        return np.array([
            self.start[0], self.start[1], self.start[2],  # 位置
            V_cruise,      # 使用巡航速度作为初始速度
            gamma_initial, # 智能计算的初始倾斜角
            psi_initial    # 智能计算的初始偏航角
        ], dtype=np.float64)

    def _generate_scenario(self):
        """生成训练场景 - 基于起点终点连线的挑战性障碍物布局"""
        config = self.environment_config

        # 固定起点和终点（左下角到右上角）
        self.start = np.array([200.0, 200.0, 200.0], dtype=np.float64)
        self.goal = np.array([1800.0, 1800.0, 1800.0], dtype=np.float64)

        # 生成静态障碍物 - 确保在起点终点连线上有足够的阻挡
        self.obstacles = []
        static_count_range = config.get('static_obstacle_count', (3, 6))
        static_count = np.random.randint(static_count_range[0], static_count_range[1] + 1)

        # 计算起点到终点的直线路径
        path_vector = self.goal - self.start
        path_length = np.linalg.norm(path_vector)
        path_direction = path_vector / path_length

        # 在路径上生成主要障碍物（确保有避障需求）
        main_obstacles_count = max(3, static_count * 2 // 3)  # 大部分障碍物都是主要障碍物

        for i in range(main_obstacles_count):
            # 在路径上的不同位置放置障碍物，更密集的分布
            # 分段：20%, 30%, 40%, 50%, 60%, 70%, 80%, 90%的路径位置
            path_ratios = [0.2, 0.35, 0.5, 0.65, 0.8, 0.95]
            ratio = path_ratios[i % len(path_ratios)]

            # 路径上的基准点
            base_point = self.start + ratio * path_vector

            # 减小偏移距离，确保更好地阻挡路径
            max_offset = 100  # 减小最大偏移到100米
            offset = np.array([
                np.random.uniform(-max_offset, max_offset),
                np.random.uniform(-max_offset, max_offset),
                np.random.uniform(-max_offset, max_offset)
            ])

            center = base_point + offset

            # 确保在边界内
            center = np.clip(center, [300, 300, 300], [self.bounds[0]-300, self.bounds[1]-300, self.bounds[2]-300])

            # 大幅增大障碍物半径，确保稳定阻挡
            radius = np.random.uniform(150, 200)  # 大幅增大半径：150-200米

            obstacle = {
                'center': center,
                'radius': radius
            }
            self.obstacles.append(obstacle)

        # 生成辅助障碍物（增加环境复杂度）
        auxiliary_count = static_count - main_obstacles_count
        for i in range(auxiliary_count):
            # 在路径两侧生成辅助障碍物
            # 选择路径上的随机点
            ratio = np.random.uniform(0.2, 0.8)
            path_point = self.start + ratio * path_vector

            # 计算垂直于路径的方向
            # 创建两个垂直向量
            if abs(path_direction[2]) < 0.9:  # 避免与z轴平行的情况
                perp1 = np.cross(path_direction, [0, 0, 1])
            else:
                perp1 = np.cross(path_direction, [1, 0, 0])
            perp1 = perp1 / np.linalg.norm(perp1)

            perp2 = np.cross(path_direction, perp1)
            perp2 = perp2 / np.linalg.norm(perp2)

            # 在垂直方向上偏移，但距离路径更近
            side_distance = np.random.uniform(200, 400)  # 减小距离：150-300米
            side_direction = np.random.choice([-1, 1])  # 随机选择左侧或右侧

            # 混合两个垂直方向
            perp_mix = np.random.uniform(-1, 1) * perp1 + np.random.uniform(-1, 1) * perp2
            perp_mix = perp_mix / np.linalg.norm(perp_mix)

            center = path_point + side_direction * side_distance * perp_mix

            # 确保在边界内
            center = np.clip(center, [300, 300, 300], [self.bounds[0]-300, self.bounds[1]-300, self.bounds[2]-300])

            # 辅助障碍物也增大
            radius = np.random.uniform(120, 180)  # 增大辅助障碍物：120-180米

            obstacle = {
                'center': center,
                'radius': radius
            }
            self.obstacles.append(obstacle)
        
        # 生成动态障碍物 - 也基于路径布局，增加挑战性
        self.dynamic_obstacles = []
        if config.get('enable_dynamic_obstacles', False):
            dynamic_count_range = config.get('dynamic_obstacle_count', (3, 5))
            dynamic_count = np.random.randint(dynamic_count_range[0], dynamic_count_range[1] + 1)

            for i in range(dynamic_count):
                obstacle = self._generate_dynamic_obstacle(i, dynamic_count)
                self.dynamic_obstacles.append(obstacle)

        # 验证障碍物布局是否有效阻挡直线路径（静默验证）
        blocking_ratio = self._validate_path_blocking()
        if blocking_ratio < 0.25:  # 降低要求到25%，减少重新生成
            # 防止无限递归，最多重试2次
            retry_count = getattr(self, '_generation_retry_count', 0)
            if retry_count < 2:  # 减少重试次数
                self._generation_retry_count = retry_count + 1
                # 静默重新生成，不输出信息
                self._generate_scenario()  # 递归重新生成
            else:
                # 重试失败也不输出，静默接受
                self._generation_retry_count = 0
        else:
            # 成功生成也不输出，保持简洁
            self._generation_retry_count = 0

    def _generate_dynamic_obstacle(self, index, total_count):
        """生成动态障碍物 - 基于路径的挑战性布局"""
        motion_types = ['linear', 'circular', 'oscillating']
        motion_type = np.random.choice(motion_types)

        # 计算路径信息
        path_vector = self.goal - self.start
        path_length = np.linalg.norm(path_vector)
        path_direction = path_vector / path_length

        # 在路径附近生成动态障碍物
        # 选择路径上的位置（避开起点和终点）
        ratio = np.random.uniform(0.3, 0.8)
        path_point = self.start + ratio * path_vector

        # 在路径附近偏移（比静态障碍物偏移更大，因为它们会移动）
        max_offset = 300  # 动态障碍物可以离路径更远
        offset = np.array([
            np.random.uniform(-max_offset, max_offset),
            np.random.uniform(-max_offset, max_offset),
            np.random.uniform(-max_offset, max_offset)
        ])

        center = path_point + offset

        # 确保在边界内
        center = np.clip(center, [400, 400, 400], [self.bounds[0]-400, self.bounds[1]-400, self.bounds[2]-400])

        obstacle = {
            'center': center.copy(),
            'radius': np.random.uniform(100, 150),  # 进一步增大动态障碍物半径
            'motion_type': motion_type,
            'time': 0.0
        }
        
        if motion_type == 'linear':
            obstacle['motion_params'] = {
                'velocity': np.random.uniform(-10, 10, 3),
                'bounds': {
                    'x': [300, self.bounds[0] - 300],
                    'y': [300, self.bounds[1] - 300],
                    'z': [300, self.bounds[2] - 300]
                }
            }
        elif motion_type == 'circular':
            obstacle['motion_params'] = {
                'center_orbit': center.copy(),
                'radius_orbit': np.random.uniform(50, 150),
                'angular_speed': np.random.uniform(0.1, 0.5),
                'phase': np.random.uniform(0, 2*np.pi)
            }
        elif motion_type == 'oscillating':
            obstacle['motion_params'] = {
                'center_base': center.copy(),
                'amplitude': np.random.uniform(20, 80) * np.random.choice([-1, 1], 3),
                'frequency': np.random.uniform(0.1, 0.3),
                'phase': np.random.uniform(0, 2*np.pi)
            }
        
        return obstacle

    def _validate_path_blocking(self):
        """验证障碍物是否能有效阻挡直线路径"""
        # 检查从起点到终点的直线是否被障碍物阻挡
        path_vector = self.goal - self.start
        path_length = np.linalg.norm(path_vector)
        path_direction = path_vector / path_length

        # 沿直线路径采样点，检查是否与障碍物碰撞
        num_samples = int(path_length / 50)  # 每50米采样一个点
        blocked_segments = 0

        for i in range(num_samples):
            t = i / num_samples
            point = self.start + t * path_vector

            # 检查该点是否在任何障碍物内
            for obs in self.obstacles + self.dynamic_obstacles:
                dist = np.linalg.norm(point - obs['center'])
                if dist < obs['radius'] + 50:  # 加50米安全距离
                    blocked_segments += 1
                    break

        blocking_ratio = blocked_segments / num_samples if num_samples > 0 else 0
        return blocking_ratio

    def step(self, control_input):
        """执行一步仿真 - 六自由度运动学"""
        # 控制输入：[a_T, a_N, μ]
        a_T = np.clip(control_input[0], -self.a_T_max, self.a_T_max)
        a_N = np.clip(control_input[1], -self.a_N_max, self.a_N_max)
        mu = np.clip(control_input[2], -np.pi/2, np.pi/2)
        
        # 当前状态
        x, y, z, V, gamma, psi = self.state
        
        # 巡飞弹六自由度运动学方程积分
        # 位置更新
        x_new = x + V * np.cos(gamma) * np.cos(psi) * self.dt
        y_new = y + V * np.cos(gamma) * np.sin(psi) * self.dt
        z_new = z + V * np.sin(gamma) * self.dt
        
        # 速度更新
        V_new = V + (a_T - self.g * np.sin(gamma)) * self.dt
        V_new = np.clip(V_new, self.V_min, self.V_max)
        
        # 角度更新
        if V > 0.1:  # 避免除零
            gamma_new = gamma + (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
            psi_new = psi + (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
        else:
            gamma_new = gamma
            psi_new = psi
        
        # 角度约束
        gamma_new = np.clip(gamma_new, -self.gamma_max, self.gamma_max)
        psi_new = psi_new % (2 * np.pi)  # 保持在[0, 2π]范围内
        
        # 更新状态
        self.state = np.array([x_new, y_new, z_new, V_new, gamma_new, psi_new], dtype=np.float64)
        
        # 更新动态障碍物
        self._update_dynamic_obstacles()
        
        # 计算奖励和终止条件
        reward, done, info = self._calculate_reward()
        
        self.step_count += 1
        if self.step_count >= self.max_steps:
            done = True
            info['timeout'] = True
        
        return self._get_observation(), reward, done, info
    
    def _update_dynamic_obstacles(self):
        """更新动态障碍物位置"""
        for obs in self.dynamic_obstacles:
            obs['time'] += self.dt
            t = obs['time']
            
            if obs['motion_type'] == 'linear':
                # 线性运动
                velocity = obs['motion_params']['velocity']
                bounds = obs['motion_params']['bounds']
                
                new_pos = obs['center'] + velocity * t
                
                # 边界反弹
                if new_pos[0] <= bounds['x'][0] or new_pos[0] >= bounds['x'][1]:
                    obs['motion_params']['velocity'][0] *= -1
                if new_pos[1] <= bounds['y'][0] or new_pos[1] >= bounds['y'][1]:
                    obs['motion_params']['velocity'][1] *= -1
                if new_pos[2] <= bounds['z'][0] or new_pos[2] >= bounds['z'][1]:
                    obs['motion_params']['velocity'][2] *= -1
                
                obs['center'] = np.clip(new_pos, 
                                      [bounds['x'][0], bounds['y'][0], bounds['z'][0]],
                                      [bounds['x'][1], bounds['y'][1], bounds['z'][1]])
            
            elif obs['motion_type'] == 'circular':
                # 圆周运动
                center_orbit = obs['motion_params']['center_orbit']
                radius_orbit = obs['motion_params']['radius_orbit']
                angular_speed = obs['motion_params']['angular_speed']
                phase = obs['motion_params']['phase']
                
                angle = angular_speed * t + phase
                obs['center'] = center_orbit + np.array([
                    radius_orbit * np.cos(angle),
                    radius_orbit * np.sin(angle),
                    0
                ])
            
            elif obs['motion_type'] == 'oscillating':
                # 振荡运动
                center_base = obs['motion_params']['center_base']
                amplitude = obs['motion_params']['amplitude']
                frequency = obs['motion_params']['frequency']
                phase = obs['motion_params']['phase']
                
                obs['center'] = center_base + amplitude * np.sin(frequency * t + phase)
    
    def _calculate_reward(self):
        """计算奖励函数"""
        if self.reward_type == 'simplified':
            return self._calculate_simplified_reward()
        else:
            return self._calculate_basic_reward()
    
    def _calculate_simplified_reward(self):
        """与DWA协同的奖励函数 - 优化DWA无法处理的长期指标"""
        pos = self.state[:3]
        goal_dist = np.linalg.norm(pos - self.goal)

        # 初始化协同优化追踪
        if not hasattr(self, '_collaborative_tracker'):
            self._collaborative_tracker = {
                'energy_efficiency_scores': [],  # 能耗效率
                'path_smoothness_scores': [],    # 路径平滑性
                'global_strategy_scores': [],    # 全局策略优化
                'total_energy_consumed': 0.0,
                'prev_control': np.zeros(3),
                'step_count': 0
            }

        tracker = self._collaborative_tracker
        tracker['step_count'] += 1

        # 计算距离改善
        if not hasattr(self, '_prev_goal_dist'):
            self._prev_goal_dist = np.linalg.norm(self.start - self.goal)
        distance_improvement = self._prev_goal_dist - goal_dist
        self._prev_goal_dist = goal_dist

        # 1. 终端奖励 - 基于DWA无法优化的长期指标
        if goal_dist < 50.0:
            # A. 总体能耗效率
            avg_energy_efficiency = np.mean(tracker['energy_efficiency_scores']) if tracker['energy_efficiency_scores'] else 0.5
            energy_bonus = avg_energy_efficiency * 1200.0

            # B. 路径平滑性
            avg_smoothness = np.mean(tracker['path_smoothness_scores']) if tracker['path_smoothness_scores'] else 0.5
            smoothness_bonus = avg_smoothness * 1200.0

            # C. 全局策略优化
            avg_global_strategy = np.mean(tracker['global_strategy_scores']) if tracker['global_strategy_scores'] else 0.5
            strategy_bonus = avg_global_strategy * 800.0

            base_reward = 5000.0
            total_terminal = base_reward + energy_bonus + smoothness_bonus + strategy_bonus

            return total_terminal, True, {
                'success': True,
                'energy_efficiency': avg_energy_efficiency,
                'path_smoothness': avg_smoothness,
                'global_strategy': avg_global_strategy,
                'total_bonus': energy_bonus + smoothness_bonus + strategy_bonus
            }

        # 终端惩罚：碰撞检查
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius'] + 5.0:  # 5米安全距离
                return -500.0, True, {'collision': True, 'reason': 'collision'}

        # 终端惩罚：越界检查
        if (pos[0] < 0 or pos[0] > self.bounds[0] or
            pos[1] < 0 or pos[1] > self.bounds[1] or
            pos[2] < 0 or pos[2] > self.bounds[2]):
            return -200.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}

        # 2. 实时协同优化评估

        # A. 能耗效率评估（DWA不考虑的长期指标）
        if hasattr(self, '_last_control_input'):
            current_control = self._last_control_input

            # 控制输入的能耗 - 使用二次型惩罚更合理的能耗模型
            control_energy = np.sum(np.square(current_control))
            max_energy = np.sum(np.square([self.a_T_max, self.a_N_max, np.pi/2]))
            energy_efficiency = max(0, 1.0 - control_energy / max_energy)

            tracker['energy_efficiency_scores'].append(energy_efficiency)
            tracker['total_energy_consumed'] += control_energy

            energy_reward = energy_efficiency * 15.0
        else:
            energy_efficiency = 0.5
            energy_reward = 7.5

        # B. 路径平滑性评估（DWA的短视性无法优化）
        if hasattr(self, '_last_control_input') and tracker['step_count'] > 1:
            control_change = np.linalg.norm(self._last_control_input - tracker['prev_control'])
            max_change = np.linalg.norm([self.a_T_max, self.a_N_max, np.pi/2]) * 2
            smoothness = max(0, 1.0 - control_change / max_change)

            tracker['path_smoothness_scores'].append(smoothness)
            smoothness_reward = smoothness * 12.0

            tracker['prev_control'] = self._last_control_input.copy()
        else:
            smoothness = 0.5
            smoothness_reward = 6.0
            if hasattr(self, '_last_control_input'):
                tracker['prev_control'] = self._last_control_input.copy()

        # C. 全局策略优化评估（TD3从DWA安全动作中的选择质量）
        # 这里评估TD3选择的动作相对于所有可能安全动作的全局优化程度
        global_strategy_score = self._evaluate_global_strategy_quality()

        tracker['global_strategy_scores'].append(global_strategy_score)
        strategy_reward = global_strategy_score * 12.0

        # D. 与DWA协同的进度奖励
        # 只有在保持效率的前提下才奖励进度（避免与DWA冲突）
        if energy_efficiency > 0.6 and smoothness > 0.5:  # 效率合理时才奖励进度
            progress_reward = distance_improvement * 2.0
        else:
            progress_reward = distance_improvement * 0.5  # 效率不佳时减少进度奖励

        # 3. 时间惩罚（鼓励效率）
        time_penalty = -0.3

        total_reward = energy_reward + smoothness_reward + strategy_reward + progress_reward + time_penalty

        return total_reward, False, {
            'energy_reward': energy_reward,
            'smoothness_reward': smoothness_reward,
            'strategy_reward': strategy_reward,
            'progress_reward': progress_reward,
            'energy_efficiency': energy_efficiency,
            'smoothness': smoothness,
            'global_strategy': global_strategy_score
        }

    def _calculate_adaptive_optimal_speed(self, goal_dist, pos):
        """计算自适应最优速度 - TD3学会的智能速度策略"""

        # 1. 基于距离的速度策略（比DWA的固定巡航速度更智能）
        if goal_dist > 1200:
            base_speed = 45.0  # 远距离高速巡航
        elif goal_dist > 600:
            base_speed = 35.0  # 中远距离中高速
        elif goal_dist > 300:
            base_speed = 28.0  # 中距离正常速度
        elif goal_dist > 150:
            base_speed = 22.0  # 近距离减速
        else:
            base_speed = 18.0  # 很近时慢速精确接近

        # 2. 基于障碍物密度的速度调整
        obstacle_density = self._calculate_local_obstacle_density(pos)
        if obstacle_density > 0.7:
            speed_factor = 0.7  # 高密度区域减速
        elif obstacle_density > 0.4:
            speed_factor = 0.85  # 中密度区域小幅减速
        else:
            speed_factor = 1.0  # 低密度区域正常速度

        # 3. 基于航迹复杂度的速度调整
        trajectory_complexity = self._estimate_trajectory_complexity(pos)
        if trajectory_complexity > 0.8:
            complexity_factor = 0.8  # 复杂航迹减速
        elif trajectory_complexity > 0.5:
            complexity_factor = 0.9  # 中等复杂度小幅减速
        else:
            complexity_factor = 1.0  # 简单航迹正常速度

        optimal_speed = base_speed * speed_factor * complexity_factor

        # 确保在合理范围内
        optimal_speed = np.clip(optimal_speed, self.V_min + 2.0, self.V_max - 5.0)

        return optimal_speed

    def _evaluate_global_strategy_quality(self):
        """评估TD3选择的全局策略质量"""
        # 这里可以评估多个全局策略指标
        # 1. 路径效率（相对于理想直线路径）
        # 2. 时间效率（相对于最优时间）
        # 3. 能耗效率（相对于最优能耗）

        pos = self.state[:3]
        goal_dist = np.linalg.norm(pos - self.goal)

        # 计算路径效率
        ideal_distance = np.linalg.norm(self.start - self.goal)
        if hasattr(self, '_total_distance_traveled'):
            path_efficiency = ideal_distance / (self._total_distance_traveled + 1e-6)
            path_efficiency = min(path_efficiency, 1.0)
        else:
            path_efficiency = 0.8  # 默认值

        # 计算进度效率
        progress = 1.0 - (goal_dist / ideal_distance)
        time_efficiency = progress / (self.step_count / self.max_steps + 1e-6)
        time_efficiency = min(time_efficiency, 1.0)

        # 综合全局策略得分
        global_strategy_score = (path_efficiency * 0.6 + time_efficiency * 0.4)
        return max(0.0, min(1.0, global_strategy_score))

    def _calculate_local_obstacle_density(self, pos):
        """计算局部障碍物密度"""
        detection_radius = 200.0  # 检测半径
        total_obstacle_volume = 0.0
        detection_volume = (4/3) * np.pi * (detection_radius ** 3)

        for obs in self.obstacles + self.dynamic_obstacles:
            dist_to_obs = np.linalg.norm(pos - obs['center'])
            if dist_to_obs < detection_radius + obs['radius']:
                # 计算障碍物在检测范围内的体积
                if dist_to_obs <= detection_radius - obs['radius']:
                    # 障碍物完全在检测范围内
                    obstacle_volume = (4/3) * np.pi * (obs['radius'] ** 3)
                else:
                    # 障碍物部分在检测范围内，简化计算
                    overlap_ratio = max(0, (detection_radius + obs['radius'] - dist_to_obs) / (2 * obs['radius']))
                    obstacle_volume = overlap_ratio * (4/3) * np.pi * (obs['radius'] ** 3)

                total_obstacle_volume += obstacle_volume

        density = min(total_obstacle_volume / detection_volume, 1.0)
        return density

    def _estimate_trajectory_complexity(self, pos):
        """估计到目标的航迹复杂度"""
        # 简化的复杂度估计：基于直线路径上的障碍物数量
        to_goal = self.goal - pos
        goal_distance = np.linalg.norm(to_goal)

        if goal_distance < 1e-6:
            return 0.0

        goal_direction = to_goal / goal_distance

        # 检查直线路径上的障碍物
        obstacles_on_path = 0
        path_segments = int(goal_distance / 50.0) + 1  # 每50米检查一次

        for i in range(path_segments):
            check_point = pos + (i / path_segments) * to_goal

            for obs in self.obstacles + self.dynamic_obstacles:
                dist_to_obs = np.linalg.norm(check_point - obs['center'])
                if dist_to_obs < obs['radius'] + 30.0:  # 需要绕行的障碍物
                    obstacles_on_path += 1
                    break  # 每个检查点最多计算一个障碍物

        # 归一化复杂度
        max_expected_obstacles = max(1, path_segments // 3)  # 预期最多每3段有1个障碍物
        complexity = min(obstacles_on_path / max_expected_obstacles, 1.0)

        return complexity

    def _calculate_basic_reward(self):
        """基础奖励函数（用于对比）"""
        pos = self.state[:3]
        
        # 基础奖励：接近目标
        goal_dist = np.linalg.norm(pos - self.goal)
        goal_reward = -goal_dist / 100.0
        
        # 到达目标奖励
        if goal_dist < 50.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}
        
        # 碰撞惩罚
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius'] + 5.0:
                return -100.0, True, {'collision': True, 'reason': 'collision'}
        
        # 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -50.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}
        
        # 时间惩罚
        time_penalty = -0.1
        
        return goal_reward + time_penalty, False, {}
    
    def _get_observation(self):
        """获取15维观测向量"""
        pos = self.state[:3]
        V, gamma, psi = self.state[3:6]
        
        # 目标方向
        goal_direction = self.goal - pos
        goal_dist = np.linalg.norm(goal_direction)
        if goal_dist > 0:
            goal_direction = goal_direction / goal_dist
        
        # 最近障碍物距离
        min_obs_dist = float('inf')
        for obs in self.obstacles + self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        
        # 构建15维观测向量
        observation = np.array([
            pos[0] / self.bounds[0],  # 归一化位置
            pos[1] / self.bounds[1],
            pos[2] / self.bounds[2],
            V / self.V_max,           # 归一化速度
            gamma / self.gamma_max,   # 归一化航迹倾斜角
            psi / (2 * np.pi),        # 归一化偏航角
            goal_direction[0],        # 目标方向
            goal_direction[1],
            goal_direction[2],
            goal_dist / 100.0,        # 归一化目标距离
            min_obs_dist / 50.0,      # 归一化障碍物距离
            len(self.obstacles) / 20.0,  # 静态障碍物数量
            len(self.dynamic_obstacles) / 10.0,  # 动态障碍物数量
            1.0 if self.dynamic_obstacles else 0.0,  # 动态标志
            pos[2] / self.bounds[2]   # 地形高度
        ], dtype=np.float64)
        
        return observation
    
    def save_scenario(self):
        """保存当前场景配置"""
        return {
            'start': self.start.copy(),
            'goal': self.goal.copy(),
            'obstacles': copy.deepcopy(self.obstacles),
            'dynamic_obstacles': copy.deepcopy(self.dynamic_obstacles),
            'bounds': self.bounds.copy()
        }
    
    def _load_scenario(self, scenario_data):
        """加载场景配置"""
        self.start = scenario_data['start'].copy()
        self.goal = scenario_data['goal'].copy()
        self.obstacles = copy.deepcopy(scenario_data['obstacles'])
        self.dynamic_obstacles = copy.deepcopy(scenario_data['dynamic_obstacles'])
        self.bounds = scenario_data['bounds'].copy()
