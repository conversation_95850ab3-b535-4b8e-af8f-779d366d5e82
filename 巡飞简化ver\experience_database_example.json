{"feature_resolution_mapping": {"0.1_0.2_0.3_0.0_0.1": {"feature_vector": [0.1, 0.2, 0.3, 0.0, 0.1], "first_seen_episode": 5, "inferred_arm": 0, "arm_performance": {"0": {"count": 15, "total_reward": 420000.0, "avg_reward": 28000.0, "performance_history": [{"episode": 5, "reward": 25000, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 12, "reward": 28500, "critic_loss": 0.25, "violations": 0, "success": true}, {"episode": 18, "reward": 30000, "critic_loss": 0.2, "violations": 0, "success": true}]}, "1": {"count": 3, "total_reward": 75000.0, "avg_reward": 25000.0, "performance_history": [{"episode": 8, "reward": 24000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 15, "reward": 26000, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 22, "reward": 25000, "critic_loss": 0.32, "violations": 0, "success": true}]}, "2": {"count": 1, "total_reward": 22000.0, "avg_reward": 22000.0, "performance_history": [{"episode": 10, "reward": 22000, "critic_loss": 0.4, "violations": 0, "success": true}]}}}, "0.4_0.6_0.8_0.0_0.4": {"feature_vector": [0.4, 0.6, 0.8, 0.0, 0.4], "first_seen_episode": 25, "inferred_arm": 1, "arm_performance": {"0": {"count": 2, "total_reward": 40000.0, "avg_reward": 20000.0, "performance_history": [{"episode": 28, "reward": 18000, "critic_loss": 0.5, "violations": 1, "success": false}, {"episode": 35, "reward": 22000, "critic_loss": 0.45, "violations": 0, "success": true}]}, "1": {"count": 8, "total_reward": 220000.0, "avg_reward": 27500.0, "performance_history": [{"episode": 25, "reward": 25000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 30, "reward": 28000, "critic_loss": 0.3, "violations": 0, "success": true}, {"episode": 33, "reward": 29000, "critic_loss": 0.28, "violations": 0, "success": true}]}, "2": {"count": 3, "total_reward": 78000.0, "avg_reward": 26000.0, "performance_history": [{"episode": 27, "reward": 24000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 32, "reward": 27000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 37, "reward": 27000, "critic_loss": 0.33, "violations": 0, "success": true}]}}}, "0.3_0.5_0.7_1.0_0.3": {"feature_vector": [0.3, 0.5, 0.7, 1.0, 0.3], "first_seen_episode": 45, "inferred_arm": 2, "arm_performance": {"0": {"count": 1, "total_reward": 15000.0, "avg_reward": 15000.0, "performance_history": [{"episode": 48, "reward": 15000, "critic_loss": 0.6, "violations": 2, "success": false}]}, "1": {"count": 4, "total_reward": 88000.0, "avg_reward": 22000.0, "performance_history": [{"episode": 50, "reward": 20000, "critic_loss": 0.5, "violations": 1, "success": false}, {"episode": 53, "reward": 22000, "critic_loss": 0.45, "violations": 0, "success": true}, {"episode": 56, "reward": 23000, "critic_loss": 0.42, "violations": 0, "success": true}, {"episode": 59, "reward": 23000, "critic_loss": 0.4, "violations": 0, "success": true}]}, "2": {"count": 7, "total_reward": 189000.0, "avg_reward": 27000.0, "performance_history": [{"episode": 45, "reward": 24000, "critic_loss": 0.45, "violations": 0, "success": true}, {"episode": 47, "reward": 26000, "critic_loss": 0.4, "violations": 0, "success": true}, {"episode": 51, "reward": 27500, "critic_loss": 0.38, "violations": 0, "success": true}, {"episode": 54, "reward": 28000, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 57, "reward": 28500, "critic_loss": 0.33, "violations": 0, "success": true}, {"episode": 60, "reward": 27500, "critic_loss": 0.35, "violations": 0, "success": true}, {"episode": 62, "reward": 27500, "critic_loss": 0.34, "violations": 0, "success": true}]}}}}, "scenario_feature_history": [{"episode": 5, "feature_vector": [0.1, 0.2, 0.3, 0.0, 0.1], "feature_signature": "0.1_0.2_0.3_0.0_0.1", "obstacles_count": 2, "dynamic_count": 0}, {"episode": 25, "feature_vector": [0.4, 0.6, 0.8, 0.0, 0.4], "feature_signature": "0.4_0.6_0.8_0.0_0.4", "obstacles_count": 5, "dynamic_count": 0}, {"episode": 45, "feature_vector": [0.3, 0.5, 0.7, 1.0, 0.3], "feature_signature": "0.3_0.5_0.7_1.0_0.3", "obstacles_count": 3, "dynamic_count": 3}], "arm_selection_history": [{"episode": 5, "arm": 0, "feature_signature": "0.1_0.2_0.3_0.0_0.1", "ucb_values": [0.0, 0.0, 0.0], "selection_reason": "inferred_from_features"}, {"episode": 8, "arm": 1, "feature_signature": "0.1_0.2_0.3_0.0_0.1", "ucb_values": [28000.0, 2.5, 2.5], "selection_reason": "ucb_exploration"}, {"episode": 12, "arm": 0, "feature_signature": "0.1_0.2_0.3_0.0_0.1", "ucb_values": [28500.2, 25000.8, 22000.5], "selection_reason": "ucb_optimal"}], "learning_statistics": {"total_episodes": 65, "unique_feature_signatures": 3, "total_arm_switches": 12, "convergence_episodes": {"0.1_0.2_0.3_0.0_0.1": 15, "0.4_0.6_0.8_0.0_0.4": 8, "0.3_0.5_0.7_1.0_0.3": 7}, "optimal_arms_learned": {"0.1_0.2_0.3_0.0_0.1": 0, "0.4_0.6_0.8_0.0_0.4": 1, "0.3_0.5_0.7_1.0_0.3": 2}}}