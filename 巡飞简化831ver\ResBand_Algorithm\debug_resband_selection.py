"""
ResBand选择逻辑调试脚本
分析为什么ResBand总是选择同一个分辨率
"""

import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from resolution_bandit import ResolutionBandit, create_paper_configs

def debug_resband_selection():
    """调试ResBand的选择逻辑"""
    print("🔍 调试ResBand选择逻辑")
    print("=" * 50)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=5,  # 短阶段长度便于观察
        reward_weights=(0.6, 0.2, 0.1),
        adaptive_exploration=True,
        performance_threshold=0.5
    )
    
    print(f"✅ ResBand创建成功")
    print(f"初始臂: {resband.current_arm}")
    print(f"初始探索系数: {resband.c}")
    
    # 模拟训练过程，详细记录每个选择
    for episode in range(15):
        print(f"\n{'='*60}")
        print(f"Episode {episode}")
        print(f"{'='*60}")
        
        # 模拟训练指标
        training_metrics = {
            'success_rate': np.random.uniform(0.2, 0.8),
            'avg_reward': np.random.uniform(-50, 50),
            'critic_loss': np.random.uniform(0.1, 2.0),
            'violation_rate': np.random.uniform(0.0, 0.3)
        }
        
        print(f"训练指标: {training_metrics}")
        print(f"当前臂: {resband.current_arm}")
        print(f"当前探索系数: {resband.c:.3f}")
        print(f"平均回报: {resband.Q}")
        print(f"选择次数: {resband.N}")
        
        # 检查是否需要更新臂选择
        if episode > 0 and episode % resband.L == 0:
            print(f"🔄 触发臂选择更新!")
            print(f"阶段: {episode // resband.L}")
            
            # 手动计算UCB值
            ucb_values = np.zeros(resband.K)
            for i in range(resband.K):
                if resband.N[i] == 0:
                    ucb_values[i] = np.inf
                else:
                    progress_factor = min(1.0, resband.training_progress['total_episodes'] / 1000)
                    adjusted_c = resband.c * (1 + progress_factor * 0.5)
                    ucb_values[i] = resband.Q[i] + adjusted_c * np.sqrt(np.log(resband.M + 1) / resband.N[i])
            
            print(f"手动计算的UCB值: {ucb_values}")
            print(f"预测选择的臂: {np.argmax(ucb_values)}")
        
        # 选择分辨率
        resolution = resband.select_resolution(episode, training_metrics)
        print(f"选择的分辨率: {resolution.name}")
        print(f"选择后的臂: {resband.current_arm}")
        
        # 模拟episode结果
        episode_reward = np.random.uniform(-100, 100)
        critic_loss = np.random.uniform(0.1, 2.0)
        violation_count = np.random.randint(0, 5)
        success = np.random.choice([True, False], p=[0.6, 0.4])
        
        print(f"Episode结果: 奖励={episode_reward:.1f}, 损失={critic_loss:.3f}, 违反={violation_count}, 成功={success}")
        
        # 更新性能
        resband.update_performance(
            episode, episode_reward, critic_loss, violation_count,
            success=success, training_metrics=training_metrics
        )
        
        print(f"更新后的平均回报: {resband.Q}")
        print(f"更新后的选择次数: {resband.N}")
    
    # 分析结果
    print(f"\n{'='*60}")
    print(f"分析结果")
    print(f"{'='*60}")
    
    # 检查臂选择历史
    unique_arms = set()
    for record in resband.arm_selection_history:
        unique_arms.add(record['arm'])
    
    print(f"使用的臂: {list(unique_arms)}")
    print(f"臂选择分布: {resband.N}")
    print(f"最终平均回报: {resband.Q}")
    
    # 检查奖励历史
    if len(resband.reward_history) > 0:
        print(f"奖励历史数量: {len(resband.reward_history)}")
        rewards = [record['reward'] for record in resband.reward_history]
        print(f"奖励范围: {min(rewards):.3f} - {max(rewards):.3f}")
    else:
        print("没有奖励历史记录")
    
    # 检查自适应调整
    print(f"自适应调整次数: {len(resband.adaptation_history)}")
    if resband.adaptation_history:
        for i, adapt in enumerate(resband.adaptation_history[-3:]):
            print(f"  调整 {i+1}: {adapt['adaptation_type']} (episode {adapt['episode']})")
    
    return resband

def test_ucb_calculation():
    """测试UCB计算"""
    print(f"\n{'='*60}")
    print(f"UCB计算测试")
    print(f"{'='*60}")
    
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=5,
        reward_weights=(0.6, 0.2, 0.1),
        adaptive_exploration=True,
        performance_threshold=0.5
    )
    
    # 模拟一些数据
    resband.Q = np.array([10.0, 5.0, 15.0])  # 设置不同的平均回报
    resband.N = np.array([3, 2, 1])  # 设置不同的选择次数
    resband.M = 2  # 设置阶段数
    resband.c = 2.0  # 设置探索系数
    
    print(f"平均回报: {resband.Q}")
    print(f"选择次数: {resband.N}")
    print(f"阶段数: {resband.M}")
    print(f"探索系数: {resband.c}")
    
    # 计算UCB值
    ucb_values = np.zeros(resband.K)
    for i in range(resband.K):
        if resband.N[i] == 0:
            ucb_values[i] = np.inf
        else:
            progress_factor = min(1.0, resband.training_progress['total_episodes'] / 1000)
            adjusted_c = resband.c * (1 + progress_factor * 0.5)
            ucb_values[i] = resband.Q[i] + adjusted_c * np.sqrt(np.log(resband.M + 1) / resband.N[i])
    
    print(f"UCB值: {ucb_values}")
    print(f"选择的臂: {np.argmax(ucb_values)}")
    
    return resband

if __name__ == "__main__":
    # 测试1: 详细的选择逻辑
    resband1 = debug_resband_selection()
    
    # 测试2: UCB计算
    resband2 = test_ucb_calculation()
