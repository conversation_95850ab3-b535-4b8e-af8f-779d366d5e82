"""
简单测试脚本
"""

import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from resolution_bandit import ResolutionBandit, create_paper_configs
    print("✅ 成功导入ResBand算法")
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=10,
        reward_weights=(0.6, 0.2, 0.1),
        adaptive_exploration=True,
        performance_threshold=0.5
    )
    print("✅ ResBand算法创建成功")
    
    # 测试分辨率选择
    for i in range(5):
        training_metrics = {
            'success_rate': np.random.uniform(0.3, 0.8),
            'avg_reward': np.random.uniform(-50, 50),
            'critic_loss': np.random.uniform(0.1, 2.0),
            'violation_rate': np.random.uniform(0.0, 0.3)
        }
        
        resolution = resband.select_resolution(i, training_metrics)
        print(f"Episode {i}: 选择分辨率 {resolution.name}")
        
        # 更新性能
        resband.update_performance(
            i, 
            np.random.uniform(-100, 100),
            np.random.uniform(0.1, 2.0),
            np.random.randint(0, 5),
            success=np.random.choice([True, False]),
            training_metrics=training_metrics
        )
    
    # 获取摘要
    summary = resband.get_training_summary()
    print(f"📊 训练摘要: 总episodes={summary['total_episodes']}, 自适应次数={summary['adaptation_count']}")
    
    print("🎉 所有测试通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
