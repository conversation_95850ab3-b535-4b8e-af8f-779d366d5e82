# LaTeX文件编译说明

## 文件说明

`resband_algorithm_latex.tex` 包含了ResBand算法和完整DWA-RL框架的详细伪代码，可直接用于SCI论文的算法描述章节。

## 编译环境要求

### 1. 必需软件
- **LaTeX发行版**：TeX Live 2023 或 MiKTeX
- **编译器**：XeLaTeX（推荐）或 pdfLaTeX
- **编辑器**：TeXstudio, Overleaf, VS Code + LaTeX Workshop

### 2. 必需包
```latex
\usepackage[UTF8]{ctex}      % 中文支持
\usepackage{algorithm}       % 算法环境
\usepackage{algorithmic}     % 算法伪代码
\usepackage{amsmath}         % 数学公式
\usepackage{booktabs}        % 表格美化
\usepackage{float}           % 图表位置控制
```

## 编译步骤

### 方法1：使用XeLaTeX（推荐）

```bash
# 在命令行中执行
xelatex resband_algorithm_latex.tex
xelatex resband_algorithm_latex.tex  # 运行两次确保引用正确
```

### 方法2：使用TeXstudio
1. 打开 `resband_algorithm_latex.tex`
2. 设置编译器为 XeLaTeX
3. 点击编译按钮

### 方法3：使用Overleaf
1. 上传 `resband_algorithm_latex.tex` 到Overleaf
2. 设置编译器为 XeLaTeX
3. 点击编译

## 文件内容结构

### 1. ResBand算法伪代码
- **Algorithm 1**：Resolution Bandit (ResBand) 算法
- **UCB选择策略**：详细的数学公式
- **分辨率配置集合**：4种不同精度的配置

### 2. 完整DWA-RL框架伪代码
- **Algorithm 2**：改进的DWA-RL框架（集成ResBand算法）
- **Algorithm 3**：DWA安全动作生成（使用动态分辨率）
- **Algorithm 4**：TD3从安全动作集中选择最优动作

### 3. 关键特性
- **控制量离散化**：详细描述如何使用分辨率配置
- **安全-探索平衡**：算法设计理念
- **复杂度分析**：时间和空间复杂度

## 论文使用建议

### 1. 直接使用
可以将整个LaTeX文件的内容直接复制到您的论文中，作为算法描述章节。

### 2. 选择性使用
- **算法章节**：使用Algorithm 1-4的伪代码
- **方法章节**：使用数学公式和问题定义
- **实验章节**：使用对比基线和评估指标

### 3. 自定义修改
- 修改标题和作者信息
- 调整算法参数
- 添加具体的实验数据

## 输出效果

编译后将生成包含以下内容的PDF文件：

1. **完整的算法伪代码**：格式规范，可直接用于论文
2. **数学公式**：使用LaTeX数学环境，显示效果专业
3. **表格**：分辨率配置集合的清晰展示
4. **中文支持**：完美支持中文标题和内容

## 常见问题解决

### 1. 中文显示问题
确保使用XeLaTeX编译器，并安装ctex包。

### 2. 算法环境问题
确保安装了algorithm和algorithmic包。

### 3. 数学公式问题
确保安装了amsmath包。

### 4. 表格显示问题
确保安装了booktabs包。

## 文件位置

LaTeX文件位于：
```
巡飞简化ver/ResBand_Algorithm/resband_algorithm_latex.tex
```

编译后的PDF文件将生成在同一目录下。

---

*本LaTeX文件提供了ResBand算法的完整学术描述，可直接用于SCI论文发表。*
