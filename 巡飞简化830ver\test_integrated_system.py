"""
测试整合后的场景感知分阶段训练系统
验证ResBand算法与动态障碍物预测的协调工作
"""

import numpy as np
from resolution_bandit import ResolutionBandit, create_paper_configs

def test_integrated_scenario_awareness():
    """测试整合后的场景感知系统"""
    print("🎯 测试整合后的场景感知分阶段训练系统")
    print("=" * 60)
    
    # 创建ResBand实例
    resband = ResolutionBandit(
        configs=create_paper_configs(),
        exploration_coefficient=2.0,
        stage_length=20,
        reward_weights=(0.7, 0.2, 0.1),
        output_dir="test_results"
    )
    
    print(f"📊 初始配置:")
    print(f"   分辨率配置数量: {len(resband.configs)}")
    for i, config in enumerate(resband.configs):
        print(f"     臂{i}: {config}")
    print(f"   场景感知分辨率需求:")
    for stage, req in resband.scenario_resolution_requirements.items():
        print(f"     {stage}: {req}")
    
    # 模拟完整的三阶段训练过程
    scenarios = [
        ("simple_static", 20, "简单静态场景", simulate_simple_static_performance),
        ("complex_static", 20, "复杂静态场景", simulate_complex_static_performance),
        ("complex_dynamic", 20, "复杂动态场景", simulate_complex_dynamic_performance)
    ]
    
    episode = 0
    stage_results = {}
    
    for scenario_stage, num_episodes, description, performance_simulator in scenarios:
        print(f"\n🎬 开始 {description} 训练")
        print("=" * 50)
        
        stage_selections = []
        stage_performance = []
        
        for i in range(num_episodes):
            episode += 1
            
            # 选择分辨率（场景感知）
            selected_config = resband.select_resolution(episode, scenario_stage)
            stage_selections.append(resband.current_arm)
            
            print(f"Episode {episode:2d}: {description} - 选择臂{resband.current_arm} ({selected_config.name})")
            
            # 模拟性能（根据场景和分辨率）
            episode_reward, critic_loss, violations, success = performance_simulator(
                resband.current_arm, i, num_episodes
            )
            stage_performance.append((episode_reward, success))
            
            # 更新性能
            resband.update_performance(episode, episode_reward, critic_loss, violations, success)
            
            print(f"         性能: 奖励={episode_reward:.1f}, 成功={success}, 训练进展={resband.training_progress}")
            
            # 每5个episode显示状态
            if (i + 1) % 5 == 0:
                recent_success_rate = np.mean([p[1] for p in stage_performance[-5:]])
                recent_reward = np.mean([p[0] for p in stage_performance[-5:]])
                print(f"   📈 最近5个episode: 成功率={recent_success_rate:.1%}, 平均奖励={recent_reward:.1f}")
                print(f"      选择次数: {resband.N}, 平均回报: {resband.Q.round(1)}")
                print()
        
        # 分析该阶段的结果
        stage_results[scenario_stage] = analyze_stage_results(
            scenario_stage, stage_selections, stage_performance, resband
        )
    
    # 生成最终分析报告
    generate_final_report(stage_results, resband)
    
    return resband, stage_results

def simulate_simple_static_performance(arm, episode_in_stage, total_episodes):
    """模拟简单静态场景性能"""
    # 训练进展因子
    progress = min(episode_in_stage / (total_episodes * 0.7), 1.0)
    
    # 分辨率适配因子（简单场景：粗分辨率最优）
    resolution_factors = [1.0, 0.95, 0.9]  # 粗、中、细
    resolution_factor = resolution_factors[arm]
    
    # 基础性能
    base_reward = 20000 + progress * 8000  # 20k -> 28k
    episode_reward = np.random.normal(base_reward * resolution_factor, 2000)
    episode_reward = max(10000, episode_reward)
    
    critic_loss = np.random.normal(0.4 - progress * 0.2, 0.1)
    violations = np.random.poisson(0.1 * (1 - progress))
    success = episode_reward > 22000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def simulate_complex_static_performance(arm, episode_in_stage, total_episodes):
    """模拟复杂静态场景性能"""
    progress = min(episode_in_stage / (total_episodes * 0.8), 1.0)
    
    # 分辨率适配因子（复杂静态：中等分辨率最优）
    resolution_factors = [0.85, 1.0, 1.05]  # 粗、中、细
    resolution_factor = resolution_factors[arm]
    
    base_reward = 18000 + progress * 10000  # 18k -> 28k
    episode_reward = np.random.normal(base_reward * resolution_factor, 2500)
    episode_reward = max(8000, episode_reward)
    
    critic_loss = np.random.normal(0.5 - progress * 0.25, 0.12)
    violations = np.random.poisson(0.2 * (1 - progress))
    success = episode_reward > 20000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def simulate_complex_dynamic_performance(arm, episode_in_stage, total_episodes):
    """模拟复杂动态场景性能（体现动态障碍物预测的重要性）"""
    progress = min(episode_in_stage / total_episodes, 1.0)
    
    # 分辨率适配因子（复杂动态：精细分辨率最优，且差异很大）
    resolution_factors = [0.6, 0.8, 1.2]  # 粗、中、细（差异更大）
    resolution_factor = resolution_factors[arm]
    
    # 动态场景更具挑战性
    base_reward = 15000 + progress * 12000  # 15k -> 27k
    episode_reward = np.random.normal(base_reward * resolution_factor, 3000)
    episode_reward = max(5000, episode_reward)
    
    critic_loss = np.random.normal(0.6 - progress * 0.3, 0.15)
    violations = np.random.poisson(0.4 * (1 - progress) * (2 - resolution_factor))  # 低分辨率更容易违反
    success = episode_reward > 18000 and violations == 0
    
    return episode_reward, critic_loss, violations, success

def analyze_stage_results(stage_name, selections, performance, resband):
    """分析单个阶段的结果"""
    print(f"\n📊 {stage_name} 阶段结果分析:")
    
    # 分辨率选择分析
    selection_counts = np.bincount(selections, minlength=len(resband.configs))
    total_selections = len(selections)
    
    print(f"   分辨率选择统计:")
    for i, count in enumerate(selection_counts):
        percentage = count / total_selections * 100
        print(f"     臂{i} ({resband.configs[i].name}): {count}次 ({percentage:.1f}%)")
    
    # 性能统计
    rewards = [p[0] for p in performance]
    successes = [p[1] for p in performance]
    
    avg_reward = np.mean(rewards)
    success_rate = np.mean(successes)
    
    print(f"   性能统计:")
    print(f"     平均奖励: {avg_reward:.1f}")
    print(f"     成功率: {success_rate:.1%}")
    
    # 适配性评估
    requirements = resband.scenario_resolution_requirements[stage_name]
    preferred_arm = requirements['preferred_resolution']
    preferred_selections = selection_counts[preferred_arm]
    adaptation_score = preferred_selections / total_selections
    
    print(f"   适配性评估:")
    print(f"     推荐分辨率: 臂{preferred_arm}")
    print(f"     推荐分辨率使用率: {adaptation_score:.1%}")
    
    if adaptation_score >= 0.6:
        print(f"     ✅ 良好适配")
    elif adaptation_score >= 0.4:
        print(f"     ⚠️  部分适配")
    else:
        print(f"     ❌ 适配不佳")
    
    return {
        'selections': selection_counts,
        'avg_reward': avg_reward,
        'success_rate': success_rate,
        'adaptation_score': adaptation_score
    }

def generate_final_report(stage_results, resband):
    """生成最终分析报告"""
    print(f"\n🎯 整合系统最终分析报告")
    print("=" * 60)
    
    print(f"📈 各阶段性能对比:")
    for stage, results in stage_results.items():
        print(f"   {stage}:")
        print(f"     平均奖励: {results['avg_reward']:.1f}")
        print(f"     成功率: {results['success_rate']:.1%}")
        print(f"     适配得分: {results['adaptation_score']:.1%}")
    
    print(f"\n🎰 ResBand算法最终状态:")
    print(f"   各臂选择次数: {resband.N}")
    print(f"   各臂平均回报: {resband.Q.round(1)}")
    print(f"   最终训练进展: {resband.training_progress}")
    
    # 评估整体系统性能
    overall_adaptation = np.mean([r['adaptation_score'] for r in stage_results.values()])
    overall_success = np.mean([r['success_rate'] for r in stage_results.values()])
    
    print(f"\n🏆 整体系统评估:")
    print(f"   平均适配得分: {overall_adaptation:.1%}")
    print(f"   平均成功率: {overall_success:.1%}")
    
    if overall_adaptation >= 0.7 and overall_success >= 0.6:
        print(f"   ✅ 系统整合成功！场景感知和分辨率自适应协调良好")
    elif overall_adaptation >= 0.5 or overall_success >= 0.4:
        print(f"   ⚠️  系统部分成功，仍有优化空间")
    else:
        print(f"   ❌ 系统需要进一步调优")

if __name__ == "__main__":
    resband, results = test_integrated_scenario_awareness()
