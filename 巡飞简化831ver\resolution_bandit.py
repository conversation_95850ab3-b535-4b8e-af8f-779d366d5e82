"""
Resolution Bandit (ResBand) 算法实现
基于多臂老虎机的自适应分辨率调度算法
"""

import numpy as np
import json
import os
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class ScenarioFeatureAnalyzer:
    """
    场景特征分析器：从障碍物布局中提取特征，用于自适应分辨率选择
    真正实现"学习如何学习"的元学习机制
    """

    def __init__(self):
        self.feature_names = [
            'obstacle_density',      # 障碍物密度
            'obstacle_complexity',   # 障碍物复杂度
            'path_difficulty',       # 路径难度
            'dynamic_ratio',         # 动态障碍物比例
            'space_constraint'       # 空间约束程度
        ]

    def extract_features(self, obstacles, current_state, goal, bounds=None):
        """
        从当前场景中提取特征向量

        Args:
            obstacles: 障碍物列表
            current_state: 当前状态 [x, y, z, V, gamma, psi]
            goal: 目标位置 [x, y, z]
            bounds: 空间边界

        Returns:
            feature_vector: 5维特征向量
        """
        if not obstacles:
            return np.zeros(len(self.feature_names))

        # 1. 障碍物密度（改为基于数量的归一化密度）
        if bounds is not None and len(bounds) >= 3:
            # 计算每立方公里的障碍物数量（更直观的密度指标）
            space_volume_km3 = (float(bounds[0])/1000) * (float(bounds[1])/1000) * (float(bounds[2])/1000)
            obstacle_density = len(obstacles) / space_volume_km3  # 个/km³
            obstacle_density = min(obstacle_density / 10.0, 1.0)  # 归一化到[0,1]，10个/km³为满值
        else:
            obstacle_density = len(obstacles) / 20.0  # 简化归一化

        # 2. 障碍物复杂度（基于大小分布和位置分布）
        radii = [obs['radius'] for obs in obstacles]
        radius_std = np.std(radii) / (np.mean(radii) + 1e-6)  # 大小变异系数

        positions = [obs['center'] for obs in obstacles]
        if len(positions) > 1:
            distances = []
            for i in range(len(positions)):
                for j in range(i+1, len(positions)):
                    distances.append(np.linalg.norm(np.array(positions[i]) - np.array(positions[j])))
            position_std = np.std(distances) / (np.mean(distances) + 1e-6)
        else:
            position_std = 0.0

        obstacle_complexity = (radius_std + position_std) / 2.0

        # 3. 路径难度（从起点到终点的直线路径上的障碍物干扰）
        start_pos = np.array(current_state[:3])
        goal_pos = np.array(goal)
        direct_path = goal_pos - start_pos
        direct_distance = np.linalg.norm(direct_path)

        if direct_distance > 0:
            path_interference = 0.0
            for obs in obstacles:
                obs_pos = np.array(obs['center'])
                # 计算点到直线的距离
                t = np.dot(obs_pos - start_pos, direct_path) / (direct_distance**2)
                t = max(0, min(1, t))  # 限制在线段上
                closest_point = start_pos + t * direct_path
                distance_to_path = np.linalg.norm(obs_pos - closest_point)

                # 如果障碍物接近直线路径，增加干扰度
                if distance_to_path < obs['radius'] * 3:  # 3倍半径内认为有干扰
                    interference = (obs['radius'] * 3 - distance_to_path) / (obs['radius'] * 3)
                    path_interference += interference

            path_difficulty = min(path_interference / len(obstacles), 1.0)
        else:
            path_difficulty = 0.0

        # 4. 动态障碍物比例
        dynamic_count = sum(1 for obs in obstacles if obs.get('motion_type') is not None)
        dynamic_ratio = dynamic_count / len(obstacles)

        # 调试信息：检查动态障碍物
        if len(obstacles) > 0:
            print(f"   🔍 障碍物调试: 总数={len(obstacles)}, 动态数={dynamic_count}")
            for i, obs in enumerate(obstacles[:3]):  # 只显示前3个
                motion_type = obs.get('motion_type', 'static')
                print(f"      障碍物{i}: motion_type={motion_type}")

        # 5. 空间约束程度（改为基于有效阻挡区域的约束度）
        if bounds is not None and len(bounds) >= 3:
            # 计算障碍物有效影响区域（考虑安全距离）
            total_space = float(bounds[0]) * float(bounds[1]) * float(bounds[2])
            effective_blocked_volume = sum([4/3 * np.pi * (obs['radius'] + 50)**3 for obs in obstacles])  # 加50m安全距离
            space_constraint = min(effective_blocked_volume / total_space * 100, 1.0)  # 放大100倍使其有意义
        else:
            space_constraint = len(obstacles) / 20.0  # 简化计算

        feature_vector = np.array([
            obstacle_density,
            obstacle_complexity,
            path_difficulty,
            dynamic_ratio,
            space_constraint
        ])

        # 归一化到[0, 1]范围
        feature_vector = np.clip(feature_vector, 0, 1)

        return feature_vector

    def get_feature_signature(self, feature_vector, precision=0.2):
        """
        将连续特征向量转换为离散的特征签名，用于映射学习

        Args:
            feature_vector: 5维特征向量
            precision: 离散化精度

        Returns:
            feature_signature: 特征签名字符串
        """
        discretized = np.round(feature_vector / precision) * precision
        signature = "_".join([f"{val:.1f}" for val in discretized])
        return signature

class ResolutionConfig:
    """分辨率配置类"""
    
    def __init__(self, name: str, a_T_resolution: float, a_N_resolution: float, 
                 mu_resolution: float, description: str = ""):
        self.name = name
        self.a_T_resolution = a_T_resolution
        self.a_N_resolution = a_N_resolution
        self.mu_resolution = mu_resolution
        self.description = description
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "a_T_resolution": self.a_T_resolution,
            "a_N_resolution": self.a_N_resolution,
            "mu_resolution": self.mu_resolution,
            "description": self.description
        }
    
    def __str__(self) -> str:
        return f"{self.name}: a_T={self.a_T_resolution}, a_N={self.a_N_resolution}, μ={self.mu_resolution}"

class ResolutionBandit:
    """
    Resolution Bandit (ResBand) 算法
    
    基于多臂老虎机的自适应分辨率调度算法，用于优化DWA层的控制输入离散化分辨率
    """
    
    def __init__(self, 
                 configs: List[ResolutionConfig],
                 exploration_coefficient: float = 2.0,
                 stage_length: int = 20,
                 reward_weights: Tuple[float, float, float] = (0.7, 0.2, 0.1),
                 output_dir: str = "results"):
        """
        初始化ResBand算法
        
        Args:
            configs: 分辨率配置列表
            exploration_coefficient: UCB探索系数
            stage_length: 每个阶段的episode数量
            reward_weights: 回报函数权重 (α, β, γ)
            output_dir: 输出目录
        """
        self.configs = configs
        self.K = len(configs)  # 臂的数量
        self.c = exploration_coefficient
        self.L = stage_length
        self.alpha, self.beta, self.gamma = reward_weights
        
        # 老虎机状态
        self.Q = np.zeros(self.K)  # 每个臂的平均回报
        self.N = np.zeros(self.K, dtype=int)  # 每个臂被选择的次数
        self.M = 0  # 总训练阶段数

        # 场景感知的分辨率自适应机制
        self.current_arm = 0  # 从臂0（最粗分辨率）开始
        self.current_scenario_stage = "simple_static"  # 当前场景阶段
        self.training_progress = "early"  # 训练进展：early, middle, late

        # 基于场景特征的自适应分辨率选择（真正的元学习）
        self.use_scenario_feature_learning = True  # 启用场景特征学习
        self.scenario_feature_history = []  # 场景特征历史
        self.feature_resolution_mapping = {}  # 特征到最优分辨率的映射学习

        # 场景特征提取器
        self.scenario_analyzer = ScenarioFeatureAnalyzer()

        # 如果禁用场景特征学习，则回退到传统的阶段映射
        self.fallback_scenario_requirements = {
            "simple_static": {"candidate_resolutions": [0, 1], "safety_constraint": None},
            "complex_static": {"candidate_resolutions": [0, 1, 2], "safety_constraint": None},
            "complex_dynamic": {"candidate_resolutions": [1, 2], "safety_constraint": "min_resolution_1"}
        }

        # 训练进展跟踪
        self.episode_count = 0
        self.recent_rewards = []  # 最近的奖励历史
        self.recent_success_rates = []  # 最近的成功率历史
        self.performance_window = 10  # 性能评估窗口
        self.stage_performance_history = {}  # 各场景阶段的性能历史
        
        # 历史记录
        self.arm_selection_history = []  # 臂选择历史
        self.reward_history = []  # 回报历史
        self.stage_performance = {}  # 每个阶段的性能记录
        
        # 输出目录
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🎰 ResBand算法初始化完成")
        print(f"   臂数量: {self.K}")
        print(f"   探索系数: {self.c}")
        print(f"   阶段长度: {self.L} episodes (自适应更新)")
        print(f"   回报权重: α={self.alpha}, β={self.beta}, γ={self.gamma}")
        print(f"   场景感知自适应: 简单静态→复杂静态→复杂动态")
        print(f"   分辨率配置:")
        for i, config in enumerate(self.configs):
            print(f"     Arm {i}: {config}")
        print(f"   初始场景阶段: {self.current_scenario_stage}")
        print(f"   初始训练进展: {self.training_progress}")
        print(f"   初始选择: {self.configs[self.current_arm].name}")
    
    def select_resolution(self, episode: int, obstacles=None, current_state=None, goal=None, bounds=None, scenario_stage: str = None) -> ResolutionConfig:
        """
        基于场景特征自适应选择分辨率配置（真正的元学习）

        Args:
            episode: 当前episode编号
            obstacles: 当前场景的障碍物列表
            current_state: 当前状态
            goal: 目标位置
            bounds: 空间边界
            scenario_stage: 场景阶段标签（可选，用于回退）

        Returns:
            选择的分辨率配置
        """
        self.episode_count = episode

        if (self.use_scenario_feature_learning and
            obstacles is not None and
            current_state is not None and len(current_state) > 0 and
            goal is not None and len(goal) > 0):
            # 基于场景特征的真正元学习
            feature_vector = self.scenario_analyzer.extract_features(obstacles, current_state, goal, bounds)
            feature_signature = self.scenario_analyzer.get_feature_signature(feature_vector)

            # 记录场景特征
            self.scenario_feature_history.append({
                'episode': episode,
                'feature_vector': feature_vector,
                'feature_signature': feature_signature,
                'obstacles_count': len(obstacles),
                'dynamic_count': sum(1 for obs in obstacles if obs.get('motion_type') is not None)
            })

            # 基于场景特征选择分辨率
            selected_arm = self._select_resolution_by_features(feature_vector, feature_signature)

            print(f"🧠 Episode {episode}: 基于场景特征选择分辨率")
            print(f"   特征向量: {feature_vector}")
            print(f"   特征签名: {feature_signature}")
            print(f"   选择臂: {selected_arm} ({self.configs[selected_arm].name})")

        else:
            # 回退到基于场景阶段的选择（兼容性）
            if scenario_stage and scenario_stage != self.current_scenario_stage:
                print(f"🎬 场景阶段转换: {self.current_scenario_stage} → {scenario_stage}")
                self.current_scenario_stage = scenario_stage

            selected_arm = self._select_resolution_by_scenario_and_progress()
            print(f"📋 Episode {episode}: 基于场景阶段选择分辨率 ({scenario_stage})")

        self.current_arm = selected_arm

        # 检测训练进展
        new_progress = self._detect_training_progress()
        if new_progress != self.training_progress:
            print(f"📈 训练进展更新: {self.training_progress} → {new_progress}")
            self.training_progress = new_progress

        # 自适应更新策略
        update_interval = self._get_adaptive_update_interval()

        # 检查是否需要更新臂选择
        if episode > 0 and episode % update_interval == 0:
            self._update_arm_selection()

        # 记录选择历史
        history_record = {
            'episode': episode,
            'arm': self.current_arm,
            'config': self.configs[self.current_arm].to_dict(),
            'training_progress': self.training_progress
        }

        if hasattr(self, 'scenario_feature_history') and self.scenario_feature_history:
            history_record['feature_vector'] = self.scenario_feature_history[-1]['feature_vector'].tolist()
            history_record['feature_signature'] = self.scenario_feature_history[-1]['feature_signature']
        else:
            history_record['scenario_stage'] = scenario_stage

        self.arm_selection_history.append(history_record)

        return self.configs[self.current_arm]

    def _select_resolution_by_features(self, feature_vector, feature_signature):
        """
        基于场景特征选择分辨率（真正的元学习核心）

        Args:
            feature_vector: 5维场景特征向量
            feature_signature: 特征签名

        Returns:
            选择的臂编号
        """
        # 如果这个特征签名之前见过，使用学习到的映射
        if feature_signature in self.feature_resolution_mapping:
            mapping_data = self.feature_resolution_mapping[feature_signature]

            # 使用UCB策略在历史表现好的分辨率中选择
            candidate_arms = list(mapping_data['arm_performance'].keys())
            if len(candidate_arms) == 1:
                return candidate_arms[0]

            best_arm = candidate_arms[0]
            best_ucb = -np.inf

            total_trials = sum(mapping_data['arm_performance'][arm]['count'] for arm in candidate_arms)

            for arm in candidate_arms:
                perf_data = mapping_data['arm_performance'][arm]
                if perf_data['count'] == 0:
                    ucb_value = np.inf
                else:
                    avg_reward = perf_data['total_reward'] / perf_data['count']
                    exploration_bonus = self.c * np.sqrt(np.log(total_trials) / perf_data['count'])
                    ucb_value = avg_reward + exploration_bonus

                if ucb_value > best_ucb:
                    best_ucb = ucb_value
                    best_arm = arm

            return best_arm

        else:
            # 新的特征签名，基于特征向量推断最适合的分辨率
            return self._infer_resolution_from_features(feature_vector, feature_signature)

    def _infer_resolution_from_features(self, feature_vector, feature_signature):
        """
        基于特征向量推断最适合的分辨率（新场景的处理）

        Args:
            feature_vector: 5维场景特征向量
            feature_signature: 特征签名

        Returns:
            推断的臂编号
        """
        obstacle_density, obstacle_complexity, path_difficulty, dynamic_ratio, space_constraint = feature_vector

        # 基于特征的启发式推断（可以被后续学习覆盖）
        complexity_score = (obstacle_density + obstacle_complexity + path_difficulty + space_constraint) / 4.0

        # 动态障碍物需要特别考虑（支持5个臂）
        if dynamic_ratio > 0.5:  # 50%以上动态障碍物
            # 高动态场景，需要精细或超精细分辨率
            if complexity_score > 0.7:
                inferred_arm = 4  # 超精细分辨率
            else:
                inferred_arm = 3  # 精细分辨率
        elif dynamic_ratio > 0.2:  # 20%-50%动态障碍物
            # 中等动态场景，需要中等或精细分辨率
            if complexity_score > 0.6:
                inferred_arm = 3  # 精细分辨率
            else:
                inferred_arm = 2  # 中等分辨率
        else:
            # 静态场景根据复杂度选择
            if complexity_score > 0.8:
                inferred_arm = 3  # 精细分辨率
            elif complexity_score > 0.6:
                inferred_arm = 2  # 中等分辨率
            elif complexity_score > 0.3:
                inferred_arm = 1  # 中粗分辨率
            else:
                inferred_arm = 0  # 粗分辨率

        # 确保在有效范围内
        inferred_arm = min(inferred_arm, len(self.configs) - 1)

        # 初始化这个特征签名的映射数据
        self.feature_resolution_mapping[feature_signature] = {
            'feature_vector': feature_vector,
            'arm_performance': {i: {'count': 0, 'total_reward': 0.0} for i in range(len(self.configs))},
            'first_seen_episode': self.episode_count,
            'inferred_arm': inferred_arm
        }

        print(f"   🆕 新特征签名，推断最适合臂: {inferred_arm}")
        print(f"   推断依据: 复杂度={complexity_score:.2f}, 动态比例={dynamic_ratio:.2f}")

        return inferred_arm

    def _get_adaptive_update_interval(self):
        """
        基于当前场景特征自适应确定更新间隔
        """
        if hasattr(self, 'scenario_feature_history') and self.scenario_feature_history:
            latest_features = self.scenario_feature_history[-1]['feature_vector']
            complexity_score = np.mean(latest_features[:4])  # 前4个特征的平均值

            if complexity_score > 0.7:
                return 3  # 高复杂度场景更频繁更新
            elif complexity_score > 0.4:
                return 5  # 中等复杂度
            else:
                return 8  # 低复杂度场景更新较慢
        else:
            # 回退到基于场景阶段的更新间隔
            if self.current_scenario_stage == "simple_static":
                return 10
            elif self.current_scenario_stage == "complex_static":
                return 6
            else:
                return 4

    def _detect_training_progress(self) -> str:
        """
        基于训练表现检测训练进展

        Returns:
            训练进展：'early', 'middle', 'late'
        """
        if len(self.recent_success_rates) < 5:
            return "early"

        current_success_rate = np.mean(self.recent_success_rates[-5:])

        # 基于成功率判断训练进展
        if current_success_rate < 0.3:
            return "early"
        elif current_success_rate < 0.7:
            return "middle"
        else:
            return "late"

    def _select_resolution_by_scenario_and_progress(self) -> int:
        """
        基于场景阶段在候选分辨率集合内使用ResBand算法选择最优分辨率

        Returns:
            选择的臂编号
        """
        # 获取当前场景的候选分辨率集合
        requirements = self.fallback_scenario_requirements[self.current_scenario_stage]
        candidate_arms = requirements["candidate_resolutions"]

        # 确保候选臂在有效范围内
        candidate_arms = [arm for arm in candidate_arms if 0 <= arm < self.K]

        if len(candidate_arms) == 1:
            # 只有一个候选分辨率，直接返回
            return candidate_arms[0]

        # 在候选集合内使用完整的ResBand算法
        # 根据训练进展调整探索策略
        if self.training_progress == "early":
            # 训练早期：更多探索，较高的探索系数
            exploration_bonus = 1.5
        elif self.training_progress == "middle":
            # 训练中期：平衡探索与利用
            exploration_bonus = 1.0
        else:  # late
            # 训练后期：更多利用，较低的探索系数
            exploration_bonus = 0.7

        # 计算候选臂的UCB值
        best_arm = candidate_arms[0]
        best_ucb = -np.inf

        for arm in candidate_arms:
            if self.N[arm] == 0:
                # 未尝试过的臂，给予最高优先级
                ucb_value = np.inf
            else:
                # 标准UCB公式，但使用调整后的探索系数
                ucb_value = (self.Q[arm] +
                           exploration_bonus * self.c * np.sqrt(np.log(self.M) / self.N[arm]))

            if ucb_value > best_ucb:
                best_ucb = ucb_value
                best_arm = arm

        return best_arm

    def _detect_training_stage(self) -> str:
        """
        基于训练进展检测当前训练阶段

        Returns:
            训练阶段：'exploration', 'refinement', 'optimization'
        """
        if len(self.recent_success_rates) < 5:
            return "exploration"  # 数据不足，保持探索阶段

        current_success_rate = np.mean(self.recent_success_rates[-5:])

        # 探索阶段 → 精化阶段
        if (self.training_stage == "exploration" and
            self.episode_count >= self.stage_transition_criteria["exploration_to_refinement"]["min_episodes"] and
            current_success_rate >= self.stage_transition_criteria["exploration_to_refinement"]["min_success_rate"]):
            return "refinement"

        # 精化阶段 → 优化阶段
        if (self.training_stage == "refinement" and
            self.episode_count >= self.stage_transition_criteria["refinement_to_optimization"]["min_episodes"] and
            current_success_rate >= self.stage_transition_criteria["refinement_to_optimization"]["min_success_rate"]):

            # 检查性能收敛
            if len(self.recent_rewards) >= self.performance_window:
                recent_rewards = self.recent_rewards[-self.performance_window:]
                reward_std = np.std(recent_rewards)
                reward_mean = np.mean(recent_rewards)
                cv = reward_std / (abs(reward_mean) + 1e-6)  # 变异系数

                if cv < self.stage_transition_criteria["refinement_to_optimization"]["convergence_threshold"]:
                    return "optimization"

        return self.training_stage  # 保持当前阶段

    def _select_resolution_by_stage(self) -> int:
        """
        根据训练阶段选择分辨率

        Returns:
            选择的臂编号
        """
        if self.training_stage == "exploration":
            # 探索阶段：优先使用粗分辨率，快速建立基本策略
            if self.N[0] < 5:  # 粗分辨率尝试次数不足
                return 0
            elif self.N[1] < 3:  # 给中粗分辨率一些机会
                return 1
            else:
                return 0  # 主要使用粗分辨率

        elif self.training_stage == "refinement":
            # 精化阶段：在粗分辨率和中等分辨率之间平衡
            # 使用UCB策略，但偏向中等精度
            ucb_values = np.zeros(self.K)
            for i in range(self.K):
                if self.N[i] == 0:
                    ucb_values[i] = np.inf
                else:
                    # 对中等分辨率给予奖励加成
                    bonus = 0.1 if i == 1 else 0.0
                    ucb_values[i] = self.Q[i] + bonus + self.c * np.sqrt(np.log(self.M) / self.N[i])

            return np.argmax(ucb_values)

        else:  # optimization阶段
            # 优化阶段：追求最佳性能，允许使用最精细的分辨率
            # 标准UCB策略
            ucb_values = np.zeros(self.K)
            for i in range(self.K):
                if self.N[i] == 0:
                    ucb_values[i] = np.inf
                else:
                    ucb_values[i] = self.Q[i] + self.c * np.sqrt(np.log(self.M) / self.N[i])

            return np.argmax(ucb_values)
    
    def update_performance(self, episode: int, episode_reward: float,
                          critic_loss: float, violation_count: int, success: bool = None):
        """
        更新性能指标并跟踪训练进展

        Args:
            episode: episode编号
            episode_reward: episode奖励
            critic_loss: Critic网络损失
            violation_count: 约束违反次数
            success: 是否成功（可选）
        """
        stage = episode // self.L

        if stage not in self.stage_performance:
            self.stage_performance[stage] = {
                'rewards': [],
                'critic_losses': [],
                'violations': [],
                'successes': [],
                'arm': self.current_arm
            }

        # 推断成功状态（如果未提供）
        if success is None:
            success = episode_reward > 20000 and violation_count == 0  # 简单的成功判断

        self.stage_performance[stage]['rewards'].append(episode_reward)
        self.stage_performance[stage]['critic_losses'].append(critic_loss)
        self.stage_performance[stage]['violations'].append(violation_count)
        self.stage_performance[stage]['successes'].append(success)

        # 更新训练进展跟踪
        self.recent_rewards.append(episode_reward)
        self.recent_success_rates.append(1.0 if success else 0.0)

        # 保持窗口大小
        if len(self.recent_rewards) > self.performance_window:
            self.recent_rewards.pop(0)
        if len(self.recent_success_rates) > self.performance_window:
            self.recent_success_rates.pop(0)

        # 更新场景特征到分辨率的映射学习
        if (self.use_scenario_feature_learning and
            hasattr(self, 'scenario_feature_history') and
            self.scenario_feature_history):

            latest_feature_data = self.scenario_feature_history[-1]
            feature_signature = latest_feature_data['feature_signature']

            if feature_signature in self.feature_resolution_mapping:
                # 更新当前分辨率在这个特征下的性能
                mapping_data = self.feature_resolution_mapping[feature_signature]
                arm_perf = mapping_data['arm_performance'][self.current_arm]

                arm_perf['count'] += 1
                arm_perf['total_reward'] += episode_reward

                # 记录这次的详细性能
                if 'performance_history' not in arm_perf:
                    arm_perf['performance_history'] = []

                arm_perf['performance_history'].append({
                    'episode': episode,
                    'reward': episode_reward,
                    'success': success,
                    'violations': violation_count
                })

                # 保持历史记录不要太长
                if len(arm_perf['performance_history']) > 20:
                    arm_perf['performance_history'].pop(0)

                print(f"   🧠 更新特征映射学习: {feature_signature}")
                print(f"      臂{self.current_arm}在此特征下: {arm_perf['count']}次尝试, 平均奖励={arm_perf['total_reward']/arm_perf['count']:.1f}")
    
    def _update_arm_selection(self):
        """更新臂选择（UCB策略）"""
        self.M += 1
        m = self.M
        
        # 计算当前臂的回报
        if m > 1 and self.current_arm in self.stage_performance.get(m-1, {}):
            r_bandit = self._compute_bandit_reward(m-1)

            # 更新平均回报（注意：N[self.current_arm]在这里还没有+1）
            self.Q[self.current_arm] = (
                (self.Q[self.current_arm] * self.N[self.current_arm] + r_bandit) /
                (self.N[self.current_arm] + 1)
            )

            # 记录回报历史
            self.reward_history.append({
                'stage': m-1,
                'arm': self.current_arm,
                'reward': r_bandit,
                'avg_reward': self.Q[self.current_arm]
            })
        
        # 基于场景阶段和训练进展的自适应分辨率选择
        self.current_arm = self._select_resolution_by_scenario_and_progress()

        # 更新选择计数
        self.N[self.current_arm] += 1

        # 计算UCB值用于显示
        ucb_values = np.zeros(self.K)
        for i in range(self.K):
            if self.N[i] == 0:
                ucb_values[i] = np.inf
            else:
                ucb_values[i] = self.Q[i] + self.c * np.sqrt(np.log(m) / self.N[i])

        # 获取当前场景的候选分辨率集合
        requirements = self.fallback_scenario_requirements[self.current_scenario_stage]
        candidate_arms = requirements["candidate_resolutions"]

        print(f"🎰 Stage {m} ({self.current_scenario_stage}-{self.training_progress}): 选择臂 {self.current_arm} ({self.configs[self.current_arm].name})")
        print(f"   场景阶段: {self.current_scenario_stage}")
        print(f"   训练进展: {self.training_progress}")
        print(f"   候选分辨率: {candidate_arms} (ResBand在此集合内学习最优选择)")
        print(f"   UCB值: {ucb_values}")
        print(f"   平均回报: {self.Q}")
        print(f"   选择次数: {self.N}")

        # 显示候选臂的性能对比
        if len(candidate_arms) > 1:
            print(f"   候选臂性能对比:")
            for arm in candidate_arms:
                if self.N[arm] > 0:
                    print(f"     臂{arm}: 平均回报={self.Q[arm]:.2f}, 选择次数={self.N[arm]}")
                else:
                    print(f"     臂{arm}: 未尝试")

        if len(self.recent_success_rates) > 0:
            print(f"   最近成功率: {np.mean(self.recent_success_rates[-5:]):.2f}")
        if len(self.recent_rewards) > 0:
            print(f"   最近奖励: {np.mean(self.recent_rewards[-5:]):.1f}")
    
    def _compute_bandit_reward(self, stage: int) -> float:
        """
        计算老虎机回报函数
        
        Args:
            stage: 阶段编号
            
        Returns:
            老虎机回报值
        """
        if stage not in self.stage_performance:
            return 0.0
        
        stage_data = self.stage_performance[stage]
        
        # 计算阶段平均指标
        avg_reward = np.mean(stage_data['rewards'])
        avg_critic_loss = np.mean(stage_data['critic_losses'])
        avg_violations = np.mean(stage_data['violations'])
        
        # 计算与前一阶段的差异
        if stage > 0 and (stage-1) in self.stage_performance:
            prev_data = self.stage_performance[stage-1]
            prev_avg_reward = np.mean(prev_data['rewards'])
            prev_avg_critic_loss = np.mean(prev_data['critic_losses'])
            prev_avg_violations = np.mean(prev_data['violations'])
            
            delta_reward = avg_reward - prev_avg_reward
            delta_critic_loss = -(avg_critic_loss - prev_avg_critic_loss)  # 负号使损失下降变为正回报
            delta_violations = -(avg_violations - prev_avg_violations)  # 负号使违反次数减少变为正回报
        else:
            # 第一阶段，使用绝对值
            delta_reward = avg_reward
            delta_critic_loss = -avg_critic_loss
            delta_violations = -avg_violations
        
        # 计算加权回报
        r_bandit = (self.alpha * delta_reward + 
                   self.beta * delta_critic_loss + 
                   self.gamma * delta_violations)
        
        return r_bandit
    
    def get_optimal_config(self) -> ResolutionConfig:
        """获取最优分辨率配置"""
        if np.sum(self.N) == 0:
            return self.configs[0]  # 如果还没有选择过任何臂，返回第一个
        
        best_arm = np.argmax(self.Q)
        return self.configs[best_arm]
    
    def save_results(self, filename: str = None):
        """保存算法结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"resband_results_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        results = {
            "algorithm_config": {
                "K": self.K,
                "exploration_coefficient": self.c,
                "stage_length": self.L,
                "reward_weights": [self.alpha, self.beta, self.gamma]
            },
            "configs": [config.to_dict() for config in self.configs],
            "final_state": {
                "Q": self.Q.tolist(),
                "N": self.N.tolist(),
                "M": self.M,
                "current_arm": self.current_arm
            },
            "arm_selection_history": self.arm_selection_history,
            "reward_history": self.reward_history,
            "stage_performance": self.stage_performance,
            "optimal_config": self.get_optimal_config().to_dict()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"📁 ResBand结果已保存到: {filepath}")
        return filepath

    def save_checkpoint(self, checkpoint_path: str):
        """保存训练检查点"""
        checkpoint_data = {
            'algorithm_state': {
                'N': self.N.tolist(),
                'Q': self.Q.tolist(),
                'M': self.M,
                'current_arm': self.current_arm,
                'K': self.K,
                'c': self.c,
                'L': self.L,
                'alpha': self.alpha,
                'beta': self.beta,
                'gamma': self.gamma
            },
            'history_data': {
                'arm_selection_history': self.arm_selection_history,
                'reward_history': self.reward_history,
                'stage_performance': self.stage_performance
            },
            'feature_learning_data': getattr(self, 'feature_resolution_mapping', {}),
            'timestamp': datetime.now().isoformat()
        }

        with open(checkpoint_path, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)

        print(f"💾 ResBand检查点已保存到: {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str):
        """从检查点恢复训练状态"""
        if not os.path.exists(checkpoint_path):
            print(f"⚠️  检查点文件不存在: {checkpoint_path}")
            return False

        try:
            with open(checkpoint_path, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            # 恢复算法状态
            state = checkpoint_data['algorithm_state']
            self.N = np.array(state['N'])
            self.Q = np.array(state['Q'])
            self.M = state['M']
            self.current_arm = state['current_arm']

            # 恢复历史数据
            history = checkpoint_data['history_data']
            self.arm_selection_history = history['arm_selection_history']
            self.reward_history = history['reward_history']
            self.stage_performance = history['stage_performance']

            # 恢复特征学习数据（如果存在）
            if 'feature_learning_data' in checkpoint_data:
                if hasattr(self, 'feature_resolution_mapping'):
                    self.feature_resolution_mapping = checkpoint_data['feature_learning_data']

            print(f"✅ ResBand状态已从检查点恢复: {checkpoint_path}")
            print(f"   恢复到Episode: {self.M}")
            print(f"   各臂选择次数: {self.N}")
            print(f"   各臂平均回报: {[f'{q:.1f}' for q in self.Q]}")

            return True

        except Exception as e:
            print(f"❌ 恢复检查点失败: {e}")
            return False

    def plot_results(self):
        """绘制算法结果图表"""
        try:
            import matplotlib.pyplot as plt
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('ResBand算法性能分析', fontsize=16)
            
            # 1. 臂选择历史
            episodes = [h['episode'] for h in self.arm_selection_history]
            arms = [h['arm'] for h in self.arm_selection_history]
            
            axes[0, 0].plot(episodes, arms, 'o-', alpha=0.7)
            axes[0, 0].set_title('臂选择历史')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('臂编号')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 平均回报
            if self.reward_history:
                stages = [h['stage'] for h in self.reward_history]
                rewards = [h['reward'] for h in self.reward_history]
                avg_rewards = [h['avg_reward'] for h in self.reward_history]
                
                axes[0, 1].plot(stages, rewards, 'o-', label='阶段回报', alpha=0.7)
                axes[0, 1].plot(stages, avg_rewards, 's-', label='平均回报', alpha=0.7)
                axes[0, 1].set_title('回报变化')
                axes[0, 1].set_xlabel('阶段')
                axes[0, 1].set_ylabel('回报')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 选择次数分布
            arm_names = [config.name for config in self.configs]
            axes[1, 0].bar(range(self.K), self.N, alpha=0.7)
            axes[1, 0].set_title('臂选择次数分布')
            axes[1, 0].set_xlabel('臂编号')
            axes[1, 0].set_ylabel('选择次数')
            axes[1, 0].set_xticks(range(self.K))
            axes[1, 0].set_xticklabels(arm_names, rotation=45)
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 最终平均回报
            axes[1, 1].bar(range(self.K), self.Q, alpha=0.7)
            axes[1, 1].set_title('最终平均回报')
            axes[1, 1].set_xlabel('臂编号')
            axes[1, 1].set_ylabel('平均回报')
            axes[1, 1].set_xticks(range(self.K))
            axes[1, 1].set_xticklabels(arm_names, rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = f"resband_analysis_{timestamp}.png"
            plot_path = os.path.join(self.output_dir, plot_filename)
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📊 ResBand分析图表已保存到: {plot_path}")
            return plot_path
            
        except ImportError:
            print("⚠️ matplotlib未安装，跳过图表生成")
            return None

def create_default_configs() -> List[ResolutionConfig]:
    """创建默认的分辨率配置"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.5, "计算效率优先，控制粗糙"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.25, "平衡精度和效率"),
        ResolutionConfig("细分辨率", 1.0, 4.0, 0.1, "高精度控制，计算代价大")
    ]
    return configs

def create_paper_configs() -> List[ResolutionConfig]:
    """创建论文中使用的分辨率配置（5组分辨率，更细粒度的探索）"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.4, "快速探索，低计算量"),
        ResolutionConfig("中粗分辨率", 3.0, 12.0, 0.3, "平衡配置"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.2, "精确控制"),
        ResolutionConfig("精细分辨率", 1.5, 6.0, 0.15, "高精度，适合复杂场景"),
        ResolutionConfig("超精细分辨率", 1.0, 4.0, 0.1, "最高精度，动态场景专用")
    ]
    return configs

def create_paper_configs_3arms() -> List[ResolutionConfig]:
    """创建3组分辨率配置（原版本）"""
    configs = [
        ResolutionConfig("粗分辨率", 4.0, 15.0, 0.4, "快速探索"),
        ResolutionConfig("中粗分辨率", 3.0, 12.0, 0.3, "平衡配置"),
        ResolutionConfig("中等分辨率", 2.0, 8.0, 0.2, "精确控制")
    ]
    return configs

def create_original_paper_configs() -> List[ResolutionConfig]:
    """创建原始论文配置（可能计算量过大）"""
    configs = [
        ResolutionConfig("粗分辨率", 3.0, 12.0, 0.3, "计算效率优先"),
        ResolutionConfig("中等分辨率", 1.5, 6.0, 0.15, "论文默认配置"),
        ResolutionConfig("细分辨率", 0.8, 3.0, 0.08, "高精度控制")
    ]
    return configs
