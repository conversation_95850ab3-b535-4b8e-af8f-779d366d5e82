# ResBand算法改进版本

## 概述

ResBand（Resolution Bandit）算法改进版本是一个基于多臂老虎机的自适应分辨率调度算法，专门为巡飞弹的DWA+TD3分层强化学习框架设计。本版本解决了原有算法缺乏正确更新机制的问题，实现了与强化学习训练的深度融合。

## 🚀 主要改进

### 1. 深度融合的更新机制
- **实时训练指标跟踪**：监控成功率、平均奖励、Critic损失、违反率等关键指标
- **自适应探索策略**：根据训练性能动态调整UCB探索系数
- **改进的回报函数**：加入成功率权重，更全面地评估分辨率配置效果

### 2. 智能自适应调整
- **性能阈值触发**：当成功率低于阈值时自动增加探索
- **训练进度感知**：根据训练进度调整探索策略
- **实时性能监控**：持续跟踪训练效果并做出相应调整

### 3. 集成训练模式
- **直接替换分阶段训练**：完全集成到巡飞简化ver的分阶段训练中
- **全局episode计数**：跨阶段连续跟踪训练进度
- **阶段间知识传递**：保持ResBand状态在阶段间的连续性

## 📊 算法原理

### 改进的UCB策略
```
UCB(i) = Q(i) + c_adjusted * sqrt(log(m) / N(i))
```
其中 `c_adjusted = c * (1 + progress_factor * 0.5)` 根据训练进度动态调整。

### 增强的回报函数
```
r_bandit = α × ΔR_m + β × (-ΔL_m_critic) + γ × (-N_m_violation) + δ × ΔSuccess_m
```
新增成功率权重 δ，使算法更关注任务完成情况。

### 自适应调整机制
- **性能良好时**：减少探索系数（c *= 0.95），增加利用
- **性能较差时**：增加探索系数（c *= 1.1），寻找更好的配置
- **性能稳定时**：保持当前探索策略

## 🛠️ 使用方法

### 1. 基本使用

```bash
# 集成分阶段训练（推荐）
python run_resband.py --mode integrated

# 快速模式
python run_resband.py --mode integrated --fast-mode

# 单阶段训练
python run_resband.py --mode train --episodes 200
```

### 2. 自定义配置

```bash
# 使用自定义ResBand配置
python run_resband.py --mode integrated --resband-config my_resband.json

# 使用自定义分阶段配置
python run_resband.py --mode integrated --stage-configs my_stages.json

# 组合使用
python run_resband.py --mode integrated --resband-config my_resband.json --stage-configs my_stages.json
```

### 3. 配置文件示例

**ResBand配置 (resband_config.json)**:
```json
{
  "exploration_coefficient": 2.0,
  "stage_length": 15,
  "reward_weights": [0.6, 0.2, 0.1, 0.1],
  "adaptive_exploration": true,
  "performance_threshold": 0.5,
  "use_fast_configs": false
}
```

**分阶段配置 (stage_config.json)**:
```json
{
  "stage1": {
    "name": "简单环境",
    "episodes": 100,
    "obstacle_count": 3,
    "complexity": "simple"
  },
  "stage2": {
    "name": "中等环境",
    "episodes": 150,
    "obstacle_count": 6,
    "complexity": "medium"
  },
  "stage3": {
    "name": "复杂环境",
    "episodes": 200,
    "obstacle_count": 10,
    "complexity": "complex"
  }
}
```

## 📈 输出结果

### 训练输出
- **模型文件**: `models/final_model.pth`
- **训练历史**: `data/final_training_history.json`
- **ResBand结果**: `data/resband_results_*.json`
- **训练进度图**: `plots/training_progress_*.png`
- **ResBand分析图**: `resband_analysis_*.png`

### 关键指标
- **成功率**: 任务完成率
- **平均奖励**: 训练过程中的平均奖励
- **违反次数**: 约束违反统计
- **自适应次数**: ResBand调整探索策略的次数
- **最优臂**: 最终选择的最优分辨率配置

## 🔧 核心文件说明

### 1. `resolution_bandit.py` - 核心算法
- `ResolutionBandit`类：改进的ResBand算法实现
- 自适应探索机制
- 实时性能监控
- 增强的回报函数

### 2. `integrated_resband_training.py` - 集成训练器
- `IntegratedResBandTrainer`类：集成分阶段训练器
- 直接替换巡飞简化ver的分阶段训练
- 全局episode计数和状态管理

### 3. `resband_trainer.py` - 单阶段训练器
- `ResBandTrainer`类：单阶段训练器
- 与改进算法的深度融合
- 实时指标跟踪

### 4. `run_resband.py` - 主运行脚本
- 支持多种运行模式
- 配置文件管理
- 使用示例生成

## 🎯 性能优势

### 相比原版本
1. **真正的自适应**：不再只是简单的分辨率选择，而是根据训练效果动态调整
2. **深度融合**：与强化学习训练过程紧密结合，不再是独立的模块
3. **实时响应**：能够根据训练进度和性能实时调整策略
4. **更好的收敛性**：通过自适应机制提高训练效率和最终性能

### 相比固定分辨率
1. **自动优化**：无需手动调参，自动找到最优分辨率配置
2. **环境适应**：能够适应不同复杂度的环境
3. **训练效率**：在保证性能的前提下提高训练效率
4. **鲁棒性**：对不同的训练场景有更好的适应性

## 🧪 实验验证

### 对比实验
```bash
# 运行完整的对比实验
python run_resband.py --mode comparison
```

### 测试模式
```bash
# 测试训练好的模型
python run_resband.py --mode test --test-episodes 100 --model-path results/models/final_model.pth
```

## 📝 使用建议

### 1. 首次使用
- 建议使用集成训练模式：`python run_resband.py --mode integrated`
- 可以先用快速模式测试：`python run_resband.py --mode integrated --fast-mode`

### 2. 参数调优
- `performance_threshold`：根据任务难度调整（0.3-0.7）
- `stage_length`：根据训练稳定性调整（10-30）
- `reward_weights`：根据关注重点调整权重

### 3. 监控指标
- 关注自适应调整次数，过多可能表示参数设置不当
- 观察成功率变化趋势，判断算法是否有效
- 检查最终选择的最优臂是否合理

## 🔮 未来改进方向

1. **多目标优化**：支持多个性能指标的平衡优化
2. **在线学习**：支持在线环境下的持续学习
3. **迁移学习**：支持不同任务间的知识迁移
4. **分布式训练**：支持多进程并行训练

## 📞 技术支持

如有问题或建议，请查看：
- 代码注释和文档字符串
- 配置文件示例
- 使用示例和帮助信息

---

**注意**：本改进版本完全向后兼容，可以直接替换原有的ResBand算法使用。
